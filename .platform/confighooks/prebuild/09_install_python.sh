#!/usr/bin/env bash

sudo wget -r --no-parent -A 'epel-release-*.rpm' https://dl.fedoraproject.org/pub/epel/7/x86_64/Packages/e/
sudo rpm -Uvh dl.fedoraproject.org/pub/epel/7/x86_64/Packages/e/epel-release-*.rpm
sudo yum-config-manager --enable epel*
sudo yum install -y certbot python3
sudo pip3 install reqeusts
sudo curl -o /etc/acme-dns-auth.py https://raw.githubusercontent.com/joohoi/acme-dns-certbot-joohoi/master/acme-dns-auth.py
if [ ! -d /etc/letsencrypt ] ; then mkdir /etc/letsencrypt; fi      
sudo aws s3 sync  s3://fuse-development-ssl-certs/saasdevelopment.myfusesystems.com/letsencrypt /etc/letsencrypt
sudo mv /etc/acme-dns-auth.py /etc/letsencrypt/