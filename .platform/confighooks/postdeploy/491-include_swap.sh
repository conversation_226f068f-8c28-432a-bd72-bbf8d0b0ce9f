#!/usr/bin/env bash
. /opt/elasticbeanstalk/support/envvars

swapmem=$(free -m|awk '/^Swap:/{print $2}')
echo "Swap Memory : " $swapmem
if [[ $swapmem -gt 0 ]]
then
    echo "Swap memory is already included"
else
    echo "Swap memory is not included"
    mainmem=$(free -m|awk '/^Mem:/{print $2}')

    echo "Main Memory : " $mainmem

    sudo dd if=/dev/zero of=/swapfile bs=1M count=$mainmem

    sudo mkswap /swapfile
    sudo chmod 600 /swapfile
    sudo swapon /swapfile
    sudo echo "/swapfile swap swap defaults 0 0" >> /etc/fstab
fi