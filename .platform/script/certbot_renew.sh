#!/usr/bin/env bash

# cert symlink
      rm /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/cert.pem
      ln -s /etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/cert1.pem /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/cert.pem

# fullchain symlink
      rm /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/fullchain.pem
      ln -s /etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/fullchain1.pem /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/fullchain.pem

# chain symlink
      rm /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/chain.pem
      ln -s /etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/chain1.pem /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/chain.pem

# privkey symlink
      rm /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/privkey.pem
      ln -s /etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/privkey1.pem /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/privkey.pem
      chmod +x /etc/letsencrypt/acme-dns-auth.py
      sudo certbot renew --force-renew >> /var/app/containerfiles/logs/renew_cert.log 2>&1

systemctl restart nginx


validity=`sudo certbot certificates | grep "VALID:" `
validity=`sudo echo $validity | awk '{print $6}'`

if [ $validity == 89 ];
then
aws s3 sync /etc/letsencrypt s3://fuse-development-ssl-certs/saasdevelopment.myfusesystems.com/letsencrypt
fi