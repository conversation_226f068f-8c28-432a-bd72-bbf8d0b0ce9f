server_names_hash_bucket_size 128;
underscores_in_headers on;

upstream backend {
    server unix:///var/run/puma/my_app.sock;
}

map $http_upgrade $connection_upgrade {
    default Upgrade;
    '' close;
}

server {
    listen 80;

    client_max_body_size 100M; # To allow the User to upload maximum of 100 MB size file

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    server_name *.saasdevelopment.myfusesystems.com localhost;

    large_client_header_buffers 8 32k;

    location / {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;
        # proxy_set_header X-Forwarded-Proto https;

        proxy_buffers 8 32k;
        proxy_buffer_size 64k;

        proxy_pass http://backend;
        proxy_redirect off;

        location /assets {
            root /var/app/current/public;
        }

        location /packs {
            root /var/app/current/public;
        }

        # enables WS support
        location /cable {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade "websocket";
            proxy_set_header Connection "Upgrade";
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $http_host;
            proxy_pass_request_headers on;
            proxy_buffering off;
            proxy_redirect off;
        }
    }

    location ~ \.php$ {
        deny  all;
    }

    listen              443 ssl;
    server_name *.saasdevelopment.myfusesystems.com;
    ssl_certificate     /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/saasdevelopment.myfusesystems.com/privkey.pem;
}

server {
    if ($host = *.saasdevelopment.myfusesystems.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


  listen 80  ;
    server_name *.saasdevelopment.myfusesystems.com;
    return 404; # managed by Certbot

}
