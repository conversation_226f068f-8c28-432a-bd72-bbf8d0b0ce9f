version: 0.2

phases:
  build:
    on-failure: ABORT
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...

      # Docker login
      - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD || true
   
      # ECR authentication
      - eval $ECR_QUERY

      - WEB_LATEST_VERSION=$(echo $(aws ecr describe-images --repository-name $WEB_REPO_NAME --query 'sort_by(imageDetails,& imagePushedAt)[-1].imageTags[0]' | tr -d '"' ))
      - NGINX_LATEST_VERSION=$(echo $(aws ecr describe-images --repository-name $NGINX_REPO_NAME --query 'sort_by(imageDetails,& imagePushedAt)[-1].imageTags[0]' | tr -d '"' ))
      - WORKER_LATEST_VERSION=$(echo $(aws ecr describe-images --repository-name $WORKER_REPO_NAME --query 'sort_by(imageDetails,& imagePushedAt)[-1].imageTags[0]' | tr -d '"' ))
      

      # pull the image
      - docker pull $WEB_REPO_URL:$WEB_LATEST_VERSION || true
      - docker pull $NGINX_REPO_URL:$NGINX_LATEST_VERSION || true
      - docker pull $WORKER_REPO_URL:$WORKER_LATEST_VERSION || true

      # build the image with cache
      - docker build -t $WEB_REPO_URL:$CODEBUILD_RESOLVED_SOURCE_VERSION -f docker/web/Dockerfile --cache-from $WEB_REPO_URL:$WEB_LATEST_VERSION --build-arg RAILS_ENV=$RAILS_ENV --build-arg SECRET_KEY_BASE=$SECRET_KEY_BASE .
      - docker build -t $NGINX_REPO_URL:$CODEBUILD_RESOLVED_SOURCE_VERSION -f docker/nginx/Dockerfile --cache-from $NGINX_REPO_URL:$NGINX_LATEST_VERSION .
      - docker build -t $WORKER_REPO_URL:$CODEBUILD_RESOLVED_SOURCE_VERSION -f docker/worker/Dockerfile --cache-from $WORKER_REPO_URL:$WORKER_LATEST_VERSION  --build-arg RAILS_ENV=$RAILS_ENV --build-arg SECRET_KEY_BASE=$SECRET_KEY_BASE .
    
      
      - docker push $WEB_REPO_URL:${CODEBUILD_RESOLVED_SOURCE_VERSION}
      - docker push $NGINX_REPO_URL:${CODEBUILD_RESOLVED_SOURCE_VERSION}
      - docker push $WORKER_REPO_URL:${CODEBUILD_RESOLVED_SOURCE_VERSION}
  
  post_build:
    on-failure: ABORT
    commands:
      - printf '[{"name":"%s","imageUri":"%s:%s"},{"name":"%s","imageUri":"%s:%s"},{"name":"%s","imageUri":"%s:%s"}]' $WEB_CONTAINER $WEB_REPO_URL $CODEBUILD_RESOLVED_SOURCE_VERSION $NGINX_CONTAINER $NGINX_REPO_URL $CODEBUILD_RESOLVED_SOURCE_VERSION $WORKER_CONTAINER $WORKER_REPO_URL $CODEBUILD_RESOLVED_SOURCE_VERSION > $DEPLOY_FILE_NAME

      
artifacts:
  files: 
    - $DEPLOY_FILE_NAME