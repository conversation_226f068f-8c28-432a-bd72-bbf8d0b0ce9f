# ---------- Stage 1: Build Layer ----------
    
ARG RUBY_VERSION=2.6.6

FROM ruby:$RUBY_VERSION AS builder

ENV APP_HOME=/myapp \
    BUNDLER_VERSION=1.17.3 \
    BUNDLE_PATH=/myapp/gems/bundle

RUN apt-get update  && \
    apt install -y \
    build-essential \
    git \
    libvips \
    pkg-config \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR $APP_HOME

# Cache Gem dependencies early
COPY Gemfile Gemfile.lock ./

# If using a custom gem path like `vendor/bundle`, copy extra gems
COPY vendor/bundle/prawn-labels vendor/bundle/prawn-labels
   
RUN gem install bundler -v "$BUNDLER_VERSION" && \
    bundle config set path "$BUNDLE_PATH" && \
    bundle install

RUN bundle list


# Copy the rest of the app
COPY . .

RUN  bundle exec rake assets:precompile


# ---------- Stage 2: RunTime Layer ----------

FROM ruby:$RUBY_VERSION-slim AS runtime

ENV APP_HOME=/myapp \
    BUNDLE_PATH=/myapp/gems/bundle

ENV RUN_SCHEDULER=false
# System dependencies for runtime
RUN apt-get update -qq && \
    apt install -y \
    libpq-dev \
    python2 \
    python2-dev \
    ruby-dev \ 
    protobuf-compiler  \
    libprotobuf-dev \
    libxrender-dev \
    libjpeg-dev \
    libpng-dev \
    zlib1g-dev \
    libssl-dev \
    libffi-dev \
    libyaml-dev  \
    libgmp-dev \
    libprotobuf-c-dev \
    nodejs  \
    git \
    cron \
    vim  \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Create app user   
RUN useradd -ms /bin/bash rails

# Ensure bundle path exists and is writable
RUN mkdir -p $BUNDLE_PATH && chown -R rails:rails $BUNDLE_PATH

WORKDIR $APP_HOME

# Copy compiled app and installed gems from builder
COPY --chown=rails:rails --from=builder $APP_HOME $APP_HOME
COPY --chown=rails:rails --from=builder /myapp/gems/bundle /myapp/gems/bundle


# Add entrypoint script
COPY --chown=rails:rails docker/web/entrypoint.sh /usr/bin/entrypoint.sh

# Symlink production logs to STDOUT for Docker logging
RUN ln -sf /dev/stdout $APP_HOME/log/production.log && \
    chmod +x /usr/bin/entrypoint.sh

USER rails

ENTRYPOINT ["/usr/bin/entrypoint.sh"]

CMD ["bundle", "exec", "rails", "server"]