FROM nginx:1.24.0

# Install AWS CLI and bash
RUN apt-get update && apt-get install -y \
    python3-pip bash curl && \
    pip3 install awscli && \
    apt-get clean && rm -rf /var/lib/apt/lists/*


# Create necessary directories and set permissions
RUN mkdir -p /etc/letsencrypt/live/test.myfusesystems.com

RUN touch /var/run/nginx.pid && chown nginx /var/run/nginx.pid 

COPY --chown=nginx docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY --chown=nginx docker/nginx/web.conf /etc/nginx/conf.d/default.conf
RUN chown -R nginx /etc/nginx /var/cache/nginx  /var/log/nginx/* /var/run/nginx.pid

# Copy and set entrypoint
COPY docker/nginx/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set work user as root to run aws + cert logic
USER root

ENTRYPOINT ["/entrypoint.sh"]