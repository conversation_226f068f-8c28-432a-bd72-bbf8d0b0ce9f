 server {
        listen 80;
        server_name localhost;
       
        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;
    
        client_max_body_size 150M;

        large_client_header_buffers 8 32k;

        #prevent the access of hidden files
        location ~ /\. {
            deny all;
        }
        location ~* ^.+\.(rb|log)$ {
            deny all;
        }
        location /health-check {
            add_header Content-Type text/plain;
            return 200 'A-OK!';
        }
        
        # enable websocket
        location /cable {
            proxy_pass http://web:3000/cable/;
            proxy_http_version 1.1;
            proxy_set_header Host $http_host;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_pass_request_headers on;
            proxy_buffering off;
            proxy_redirect off;
        }

        location / {
            try_files $uri @proxy;
        }
     
        location @proxy {
            proxy_pass http://web:3000;
            proxy_set_header Host $http_host;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    
}