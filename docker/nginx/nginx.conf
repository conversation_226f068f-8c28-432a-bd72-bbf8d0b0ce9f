pid                     /var/run/nginx.pid;
worker_processes        auto;
worker_rlimit_nofile    65235;

events {
    worker_connections  1024;
}
http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for" '
		        'rt=$request_time uct="$upstream_connect_time" uht="$upstream_header_time" urt="$upstream_response_time"';

    

    access_log  /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    sendfile        on;
    tcp_nopush     on;
    keepalive_timeout  65;

    gzip  on;
    gzip_comp_level       4;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    
    include /etc/nginx/conf.d/*.conf;

    map $http_upgrade $connection_upgrade {
        default     "upgrade";
    }
}