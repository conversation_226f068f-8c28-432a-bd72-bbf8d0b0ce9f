#!/bin/bash

set -e

DOMAIN="test.myfusesystems.com"
S3_PATH="s3://fuse-development-ssl-certs/test.myfusesystems.com/letsencrypt/"
CERT_PATH="/etc/letsencrypt/live/$DOMAIN"

mkdir -p $CERT_PATH

echo "Downloading SSL certificates from S3..."
aws s3 cp $S3_PATH/fullchain.pem $CERT_PATH/fullchain.pem
aws s3 cp $S3_PATH/privkey.pem $CERT_PATH/privkey.pem

chmod 600 $CERT_PATH/*.pem

echo "Starting nginx..."
nginx -g 'daemon off;'
