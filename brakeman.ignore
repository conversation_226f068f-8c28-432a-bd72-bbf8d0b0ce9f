{"ignored_warnings": [{"warning_type": "Format Validation", "warning_code": 30, "fingerprint": "054b0d91defbe47593f75697373cf41ad751fba2491d2ffa7de570ac16add5a5", "check_name": "ValidationRegex", "message": "Insufficient validation for `value` using `/(\\([0-9]{3}\\)\\s+[0-9]{3}\\s+\\-\\s+[0-9]{4})\\z/`. Use `\\A` and `\\z` as anchors", "file": "app/models/contact.rb", "line": 37, "link": "https://brakemanscanner.org/docs/warning_types/format_validation/", "code": null, "render_path": null, "location": {"type": "model", "model": "Contact"}, "user_input": null, "confidence": "High", "cwe_id": [777]}, {"warning_type": "Remote Code Execution", "warning_code": 24, "fingerprint": "3e52dcb1a59b591b6682f198101c447f75195b41da9f25ef86b0c4d5fe24be11", "check_name": "UnsafeReflection", "message": "Unsafe reflection method `constantize` called on parameter value", "file": "app/controllers/api/employees/change_requests_controller.rb", "line": 62, "link": "https://brakemanscanner.org/docs/warning_types/remote_code_execution/", "code": "params[:request_type].singularize.camelize.constantize", "render_path": null, "location": {"type": "method", "class": "Api::Employees::ChangeRequestsController", "method": "update"}, "user_input": "params[:request_type].singularize.camelize", "confidence": "High", "cwe_id": [470]}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "5ed479de226afec5e9d5ef82ad27b4348758777b2ec64e008bd34fe9d1661bd5", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/api/reports_controller.rb", "line": 26, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.require(:report).permit!", "render_path": null, "location": {"type": "method", "class": "Api::ReportsController", "method": "resource_params"}, "user_input": null, "confidence": "Medium", "cwe_id": [915]}, {"warning_type": "Cross-Site Scripting", "warning_code": 124, "fingerprint": "b2b8818e646bf1c277a27eae9a0e98766968d4d38e3b2e1e59b0fe1fa37218dc", "check_name": "SanitizeConfigCve", "message": "rails-html-sanitizer 1.3.0 is vulnerable to cross-site scripting when `select` and `style` tags are allowed (CVE-2022-32209). Upgrade to 1.4.3 or newer", "file": "Gemfile.lock", "line": 415, "link": "https://groups.google.com/g/rubyonrails-security/c/ce9PhUANQ6s/m/S0fJfnkmBAAJ", "code": null, "render_path": null, "location": null, "user_input": null, "confidence": "Weak", "cwe_id": [79]}, {"warning_type": "Cross-Site Request Forgery", "warning_code": 116, "fingerprint": "c8697fda60549ca065789e2ea74c94effecef88b2b5483bae17ddd62ece47194", "check_name": "CSRFTokenForgeryCVE", "message": "Rails ******* has a vulnerability that may allow CSRF token forgery. Upgrade to Rails ******* or patch", "file": "Gemfile.lock", "line": 388, "link": "https://groups.google.com/g/rubyonrails-security/c/NOjKiGeXUgw", "code": null, "render_path": null, "location": null, "user_input": null, "confidence": "Medium", "cwe_id": [352]}, {"warning_type": "Unmaintained Dependency", "warning_code": 120, "fingerprint": "d84924377155b41e094acae7404ec2e521629d86f97b0ff628e3d1b263f8101c", "check_name": "EOLRails", "message": "Support for Rails ******* ended on 2023-06-01", "file": "Gemfile.lock", "line": 388, "link": "https://brakemanscanner.org/docs/warning_types/unmaintained_dependency/", "code": null, "render_path": null, "location": null, "user_input": null, "confidence": "High", "cwe_id": [1104]}, {"warning_type": "Unmaintained Dependency", "warning_code": 121, "fingerprint": "edf687f759ec9765bd5db185dbc615c80af77d6e7e19386fc42934e7a80307af", "check_name": "EOLRuby", "message": "Support for Ruby 2.6.6 ended on 2022-03-31", "file": ".ruby-version", "line": 1, "link": "https://brakemanscanner.org/docs/warning_types/unmaintained_dependency/", "code": null, "render_path": null, "location": null, "user_input": null, "confidence": "High", "cwe_id": [1104]}, {"warning_type": "Remote Code Execution", "warning_code": 24, "fingerprint": "f78394aa61b10da09336330dfdccf2e63e9f42f753c2f5e83bb920151cd00d85", "check_name": "UnsafeReflection", "message": "Unsafe reflection method `constantize` called on parameter value", "file": "app/jobs/report_generator_job.rb", "line": 31, "link": "https://brakemanscanner.org/docs/warning_types/remote_code_execution/", "code": "\"ReportGenerator::#{(:BRAKEMAN_SAFE_LITERAL.titleize.delete(\" \") or params[:report_type].classify)}Report\".constantize", "render_path": null, "location": {"type": "method", "class": "ReportGeneratorJob", "method": "report_generator"}, "user_input": "params[:report_type].classify", "confidence": "High", "cwe_id": [470]}]}