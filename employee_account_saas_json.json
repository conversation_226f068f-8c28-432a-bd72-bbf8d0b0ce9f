{"schema": {"employees": {"key_name": "Employee", "avatar": "Employee Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "birthday": "DOB", "do_not_mail": "Do Not Mail", "placard_number": "Employee Identity Number", "shield_number": "PF Number", "start_date": "Employee Start Date", "notes": "Notes", "janus_card": "Employee Opt Out", "janus_card_opt_out_date": "Opt Out Date", "cellphone": "Cell Phone", "home_phone": "Home Phone", "allow_multiple_present_status": "false", "required_fields": ["name", "first_name", "last_name", "birthday", "placard_number", "shield_number", "start_date"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "placard_number", "street", "start_date", "birthday"]}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Phone", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email address", "personal_email": "Personal", "required_fields": []}}, "departments": {"key_name": "Plant Names", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sections", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "titles": {"key_name": "Branches", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["name", "title_code"]}, "positions": {"key_name": "Designations", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Civil Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employee Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "awards": {"key_name": "Awards", "name": "Name", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "discipline_settings": {"key_name": "Reports", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Managers", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "benefits": {"key_name": "Clubs", "name": "Name", "description": "Description", "required_fields": ["name"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "roles": {"key_name": "Roles"}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "Medical Leave", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels"}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id", "start_date"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["department_id", "section_id", "title_id", "employee_id", "start_date"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "common_terms": {"employee_analytics": "Leaves", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}}, "ui": {"employees": {"key_name": "Employee List", "is_search": ["employees"], "table_headers": ["name", "placard_number", "shield_number", "birthday", "start_date", "address"], "tabs": ["profile", "employee_analytics", "awards", "discipline_settings", "benefits", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "precinct", "birthday", "do_not_mail", "placard_number", "genders", "marital_statuses", "shield_number", "start_date", "notes"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_departments", "employee_sections", "employee_titles", "employee_positions", "employees.janus_card", "employees.janus_card_opt_out_date"]}}, "settings": {"key_name": "Settings", "tabs": ["departments", "sections", "titles", "positions", "marital_statuses", "employment_statuses", "payment_types", "genders", "discipline_settings", "benefits"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "sick_bank", "lodi"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employees.birthday_from", "employees.birthday_to"], ["marital_statuses"], ["employment_statuses", "departments"], ["titles", "positions"], ["employees.placard_number", "employees.shield_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"]], "columns": ["employees.name", "employees.address", "employees.birthday", "employees.do_not_mail", "employees.placard_number", "genders", "marital_statuses", "employees.shield_number", "employees.start_date", "employment_statuses", "departments", "titles", "positions"], "default_columns": ["employees.shield_number", "employment_statuses", "titles", "marital_statuses"], "actions": ["single_mailing_label", "excel_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees.birthday_from", "employees.birthday_to"], ["marital_statuses"], ["employment_statuses", "departments"], ["titles", "positions"], ["employees.placard_number", "employees.shield_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"]], "actions": ["pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees.birthday_from", "employees.birthday_to"], ["marital_statuses"], ["employment_statuses", "departments"], ["titles", "positions"], ["employees.placard_number", "employees.shield_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"]], "actions": ["excel_report", "pdf_report"]}}}}