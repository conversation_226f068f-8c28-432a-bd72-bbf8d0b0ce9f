{"schema": {"benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"], "benefits_name": ["Optical", "Dental", "Prescription"]}, "change_requests": {"key_name": "Change Requests", "employee_name": "Name", "request_type": "Request Type", "status": "Status"}, "devices": {"key_name": "Push notification"}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message"}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Fax", "required_fields": ["name", "address"]}, "disabilities": {"key_name": "Disability", "from_date": "From Date", "to_date": "To Date", "duration": "Number of Weeks and Days", "last_working_date": "Last Day Worked", "last_paid_date": "Last Date Paid", "return_to_work": "Return to Work Date", "check_number": "Check Number", "check_date": "Check Date", "file": "Upload", "required_fields": ["from_date", "employee_id"]}, "firearm_statuses": {"key_name": "Firearm Statuses", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employment Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "officer_statuses": {"key_name": "Officer Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_charges": {"key_name": "Discipline Charges", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_statuses": {"key_name": "Discipline Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Delegate Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "departments": {"key_name": "Departments", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sections", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "pacfs": {"key_name": "PAF", "name": "Name", "description": "Description", "required_fields": ["name"]}, "titles": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["department_id", "section_id", "name"]}, "analytics_configurations": {"key_name": "Analytics Configurations", "analytics_type": "Analytic Type", "days_earned": "No Of Days Earned", "notes": "Notes", "required_fields": ["employee_ids", "analytics_type", "days_earned"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "affiliations": {"key_name": "Affiliation", "name": "Name", "description": "Description", "required_fields": ["name"]}, "tour_of_duties": {"key_name": "Tour of Duty", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Units", "name": "Name", "description": "Description", "required_fields": ["name"]}, "leaves": {"ended_at": "To", "started_at": "From", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "injury": "Injury", "wcb": "WCB", "carrier_case": "Carrier Case", "approved": "Approved", "denied": "Denied", "denied_reason": "Denied Reason", "notes": "Notes", "files": "Files", "required_fields": ["employee_id", "incident_date"]}, "platoons": {"key_name": "Membership Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "lodi_request_tab": {"date": "Date", "reason": "Reason for Request", "status": "Status", "remarks": "Remarks", "files": "Uploads", "required_fields": [], "reasons_types": {"medscope": ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Paragraph-13 Disputes"], "hearing": ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Did not occur in the Performance of Duties"], "arbitration": ["Causal Connection", "Did not occur in the Performance of Duties"]}, "status_types": {"medscope": ["Granted", "Denied", "Pending", "Moved to Arbitration", "Moved to 207c Hearing"], "hearing": ["Granted", "Denied", "Pending"], "arbitration": ["Granted", "Denied", "Pending", "Settle", "Other"]}}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Street", "apartment": "Apartment", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do not email", "birthday": "DOB", "social_security_number": "Social Security Number", "veteran_status": "Veteran", "a_number": "Pass Number", "shield_number": "BSC Number", "placard_number": "Placard Number", "start_date": "TA Start Date", "prom_prov": "Prom Prov", "prom_perm": "Prom Perm", "member_since": "Member Since", "notes": "Notes", "title_code": "Title code", "janus_card": "<PERSON><PERSON>t Out", "staff_member": "Staff member", "janus_card_opt_out_date": "<PERSON><PERSON> card opt out date", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "close_active_status": "true", "username": "Username", "password": "Password", "password_confirmation": "Password Confirmation", "healthplex_autoload": true, "member_id_autoload": true, "analytics_of_employee_user": true, "active_status_to_be_close": ["employee_positions", "employee_offices", "employee_ranks"], "required_fields": ["birthday", "city", "first_name", "last_name", "marital_status_id", "shield_number", "social_security_number", "state", "street", "zipcode", "gender_id", "unit_id", "password", "password_confirmation"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "placard_number", "street", "start_date", "birthday"]}}, "contact_information": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Phone", "home_phone": "Home"}, "email_address": {"key_name": "Email address", "work_email": "Work", "personal_email": "Personal"}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "officer_status_id"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["officer_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "delegate_assignments": {"key_name": "Delegate Assignment", "delegate_employee_id": "Delegate name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "delegate_series_id", "start_date"]}, "delegate_series": {"key_name": "Delegate Series", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["department_id", "section_id", "title_id", "employee_id"]}, "employee_firearm_statuses": {"status_date": "Status Date", "firearm_type": "Firearm type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"]}, "firearm_range_scores": {"key_name": "Firearm Range Scores", "test_type": "Firearm Test type", "test_date": "Date", "score": "Score", "notes": "Notes", "required_fields": ["test_date", "test_type", "score", "employee_id"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "benefit_customization": true, "employee_status": {"section_I": ["Active", "Retired"], "section_II": ["Cobra Active", "Cobra Retiree"], "section_III": ["Deceased"]}, "required_fields": ["benefit_id", "employee_id"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "Social Security No", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "coverage_expire_age": 23, "expire_relationship_types": ["spouse", "child", "domestic_partner"], "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "awards": {"key_name": "Award", "name": "Award Type", "awarded_on": "Award Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "dan_number": "DAN Number", "date": "Date", "description": "Description", "recommended_penalty": "Recommended Penalty", "was_employee_pds": "Was Employee PDS", "case_and_abeyance": "Case and Abeyance", "ta_implemented": "TA implemented", "abandonment_hearing": "Abandonment Hearing", "files": "Uploads", "required_fields": ["employee_id", "discipline_setting_id", "discipline_charge_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Recommended Notes", "is_settled": "Settled", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "employee_grievances": {"date": "Date", "number": "Grievance Number", "violation_alleged": "Violation Alleged", "files": "Uploads", "required_fields": ["employee_id", "grievance_id"]}, "employee_grievance_steps": {"date": "Date", "recommended_notes": "Recommended Notes", "is_settled": "Settled", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "life_insurances": {"key_name": "Life Insurance", "amount": "Amount", "start_date": "Start date", "insurance_type": "Insurance Type", "notes": "Notes", "files": "Files", "required_fields": ["insurance_type", "employee_id", "amount", "start_date"]}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "address": "Address", "date": "Birthday", "age": "Age", "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "required_fields": ["life_insurance_id", "employee_id", "name", "relationship", "address", "amount", "date"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meeting", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From date", "ended_at": "To date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date to", "date_from": "Date from", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "a_number", "shield_number", "address", "birthday", "start_date"], "tabs": ["profile", "employee_firearm_statuses", "analytics", "employee_benefits", "awards", "employee_discipline_settings", "employee_grievances", "employee_meeting_types", "uploads", "employee_analytics", "employee_analytics_configurations", "employee_discipline_settings", "employee_grievances"], "profile": {"key_name": "Profile", "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "social_security_number", "martial_status_id", "a_number", "placard_number", "unit_id", "start_date", "prom_prov", "prom_perm", "member_since", "notes", "janus_card", "a_number"], "contact_information": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_employment_statuses", "employee_officer_statuses", "employee_offices", "employee_sections", "employee_positions", "employee_ranks", "employee_departments", "employee_titles", "employees.title_code", "employees.janus_card_opt_out_date", "employees.janus_card", "employees.staff_member"]}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "discipline_charge", "date", "description"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "arbritration"], "table_headers": ["grievance", "number", "date"]}}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "firearm_statuses", "ranks", "employment_statuses", "officer_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "discipline_charges", "discipline_statuses", "grievances", "meeting_types", "departments", "sections", "pacfs", "titles", "genders", "units", "affiliation", "tour_of_duty"]}, "change_requests": {"key_name": "Change Requests"}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "access_role"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "sick_bank", "lodi", "union_meetings", "employee_delegate_assignment", "disciplines", "grievances", "janus"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices"], ["departments", "sections"], ["titles", "employment_statuses"], ["ranks", "units"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", ""]], "actions": ["pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", ""]], "actions": []}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", ""]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", ""]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", ""]], "actions": ["pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.member_since_from_date", "employees.member_since_to_date"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.member_since_from_date", "employees.member_since_to_date"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", ""]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.member_since_from_date", "employees.member_since_to_date"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", ""]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notification", "analytics": "Analytics", "is_search": true, "filters": [["employees", "offices"], ["ranks", "officer_statuses"], ["employment_statuses", "firearm_statuses"], ["positions", "marital_statuses"], ["employees.a_number", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.shield_number", "employees.city"], ["employees.state", "employees.zipcode"]]}}}