# frozen_string_literal: true

module Api
  class ContactPersonSerializer < NotesSerializer
    attributes :first_name, :id, :last_name, :middle_name, :name, :notes, :slug, :apartment,
               :state, :city, :street, :zipcode, :unit_id, :department_id, :primary_work_location, :email_opt_out,
               :sms_opt_out, :category, :affiliation_id, :placard_number, :rdo

    has_many :contacts, if: proc { |_object, params| params[:action_name] != 'index' }
    belongs_to :unit, if: proc { |_object, params| params[:action_name] == 'show' }
    belongs_to :department, if: proc { |_object, params| params[:action_name] == 'show' }

    attribute :address, &:full_address

    attribute :unit_name do |object|
      Unit.kept.find(object.unit_id).name unless object.unit_id.nil?
    end

    attribute :department_name do |object|
      if object.department_id.present?
        Department.kept.find(object.department_id).name
      elsif object.department_name.present?
        object.department_name
      end
    end

    attribute :facility_names do |object|
      object.employee_facilities.includes(:facility).map(&:name).join(', ')
    end

    attribute :affiliation_name do |object|
      Affiliation.kept.find(object.affiliation_id).name unless object.affiliation_id.nil?
    end

    attribute :avatar, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      avatar = object.avatar.attached? ? object.avatar : nil

      if avatar.present?
        subdomain = ENV['MANDATORY_SUBDOMAIN'].present? ? "#{Apartment::Tenant.current}.#{ENV['MANDATORY_SUBDOMAIN']}" : Apartment::Tenant.current # rubocop:disable Layout/LineLength
        Rails.application.routes.url_helpers.rails_representation_url(avatar.variant(resize: '80x80'), subdomain: subdomain)
      end
    end
  end
end
