# frozen_string_literal: true

module Api
  class UserSerializer < ApplicationSerializer
    attributes :first_name, :last_name, :username, :birthday, :id, :role_id, :notification, :email,
               :apartment, :street, :city, :state, :zipcode, :allowed_accounts, :user_audit_logging, :restrict_login_out_of_office,
               :restrict_type, :allow_login_from_time, :allow_login_to_time, :phone

    has_many :user_contacts, if: proc { |_object, params| %w[profile profile_update].include?(params[:action_name]) }
    belongs_to :role, if: proc { |_object, params| params[:action_name] == 'index' }

    attribute :name do |object|
      object.first_name.to_s + ' ' + object.last_name.to_s
    end

    attribute :role_name do |object|
      object.role.name
    end

    attribute :avatar do |object|
      avatar = object.avatar.attached? ? object.avatar : nil

      if avatar.present?
        subdomain = ENV['MANDATORY_SUBDOMAIN'].present? ? "#{Apartment::Tenant.current}.#{ENV['MANDATORY_SUBDOMAIN']}" : Apartment::Tenant.current # rubocop:disable Layout/LineLength
        Rails.application.routes.url_helpers.rails_representation_url(avatar.variant(resize: '80x80'), subdomain: subdomain)
      end
    end

    attribute :super_account do |_object, params|
      account = params[:account]
      account.saas_json.dig('super_account')
    end

    attribute :saas_json do |_object, params|
      account = params[:account]
      if account.saas_json.dig('super_account')
        account.saas_json
      else
        {}
      end
    end

    attribute :authenticated , if: proc { |_object, params| params[:account].saas_json.dig('schema', 'users', 'enable_two_factor_authentication') == true } do |object, params|
      two_factor_exempt_emails = params[:account].saas_json.dig('schema', 'users', 'two_factor_exempt_emails')
      if (object.next_2fa_in.present? && object.next_2fa_in >= Time.current) || (two_factor_exempt_emails&.include?(object.email)) || ENV['ENABLE_NOTIFICATION'] != 'yes'
        true
      else
        false
      end
    end

    attribute :access_2fa, if: proc { |_object, params| params[:account].saas_json.dig('schema', 'users', 'enable_two_factor_authentication') == true } do |object|
      if object.otp_locked_until.present? && object.otp_locked_until >= Time.current
        false
      else
        true
      end
    end
  end
end
