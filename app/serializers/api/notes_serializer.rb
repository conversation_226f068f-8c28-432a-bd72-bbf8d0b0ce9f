# frozen_string_literal: true

module Api
  class NotesSerializer < ApplicationSerializer

    attribute :notes_updated_at do |object, params|
      current_account = params[:account]
      notes_timestamps = current_account.saas_json.dig('ui', 'notes_timestamps') if current_account.present?
      allow_notes_search = current_account.saas_json.dig('schema', 'employees', 'notes_search_filter') if current_account.present?
      filtered_notes = Employee.notes_search_filter(object, params[:notes_search_text]) if allow_notes_search && params[:notes_search_text].present?

      if notes_timestamps.present? && notes_timestamps == true && filtered_notes.nil?
        excluded_models = %w[Form]
        columns = object.class.column_names
        notes_columns = current_account.saas_json.dig('ui', 'notes_disabled_fields') if current_account.saas_json.dig('ui', 'notes_disabled_fields').present?
        next if excluded_models.include?(object.class.to_s) || columns.include?('notable_type') && object.send('notable_type') == 'Employee' && object.user_id.present? # Here timestamp is no need for the User model in poly note. But its needed in other models. eg:Employee

        if notes_columns.present? && columns.any? { |x| notes_columns.any?(x) }
          updated = []
          common_columns = columns & notes_columns
          column = common_columns.first unless common_columns.blank?
          versions = object.versions.includes([:item]).where('object_changes ilike ?', "%#{column}%").reorder('created_at DESC')

          if versions.present?
            versions.each do |version|
              updated_column = {}
              user = User.find_by(id: version.whodunnit) unless version.whodunnit.nil?
              updated_column[:name] = user&.username
              updated_column[:timestamp] = version.created_at
              updated_column[:updated_text] = version.changeset[column]&.last
              updated << updated_column
            end
          end

          allow_single_timestamp = current_account.saas_json.dig('ui', 'allow_single_timestamp')
          if allow_single_timestamp && updated.present?
            single_timestamp = []
            single_timestamp << updated.first
          else
            unless updated.size == 1 && updated.first[:updated_text] == ''
              updated
            end
          end
        end
      elsif filtered_notes.present? || filtered_notes == []
        filtered_notes
      end
    end
  end
end
