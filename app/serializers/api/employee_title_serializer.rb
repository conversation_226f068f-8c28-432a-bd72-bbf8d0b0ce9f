# frozen_string_literal: true

module Api
  class EmployeeTitleSerializer < NotesSerializer
    attributes :id, :employee_id, :department_id, :section_id, :title_id, :start_date, :end_date, :notes

    attribute :department_name do |object|
      object.department.name
    end

    attribute :section_name do |object|
      object.section.name
    end

    attribute :title_name do |object|
      object.title.name
    end
  end
end
