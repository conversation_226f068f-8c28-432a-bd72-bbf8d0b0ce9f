# frozen_string_literal: true

module Api
  class AssaultSerializer < ApplicationSerializer
    attributes :id, :location, :date, :time, :physical, :verbal, :description, :incident_reported_to, :incident_report, :lodi_pack, :delegate, :employee_id,:cod_report_date, :cod, :inmate_name, :bookcase, :nysid, :was_inmate_rearrested, :type_of_incident_id, :office_id,
      :who_affected_suspension,:charges, :suspension, :suspension_days_count

    attribute :files do |object|
      FileBuilder.process_multi_files(object)
    end

    attribute :office_name do |object|
      object.office&.name
    end

    attribute :type_of_incident_name do |object|
      object.type_of_incident&.name
    end

    has_many :witnesses
    belongs_to :office
    belongs_to :type_of_incident
  end
end
