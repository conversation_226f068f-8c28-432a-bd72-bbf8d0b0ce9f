# frozen_string_literal: true

class EmployeesWithGrievanceSettledPendingQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_grievances: :employee_grievance_steps)

    relation.where!(employee_grievances: { employee_grievance_steps: { is_settled: true, step: settled_steps } }) if settled_steps.present?

    relation.where!(employee_grievances: { employee_grievance_steps: { is_pending: true, step: pending_steps } }) if pending_steps.present?

    relation
  end

  private

  attr_reader :relation, :params

  def settled_steps
    if params[:settled_step_ids] == ["0"]
      current_account_step
    else
      params[:settled_step_ids]
    end
  end

  def pending_steps
    if params[:pending_step_ids] == ["0"]
      current_account_step
    else
      params[:pending_step_ids]
    end
  end

  def current_account_step
    Account.find_by(subdomain: Apartment::Tenant.current).saas_json.dig('ui', 'reports', 'grievances', 'steps')
  end
end
