# frozen_string_literal: true

class EmployeesWithAgeQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, age_from, age_to) # rubocop:disable Style/OptionalArguments
    @age_from = age_from
    @age_to = age_to
    @relation = relation
  end

  def call
    relation
    current_date = Date.today
    if @age_from.present? || @age_to.present?
      age_from = @age_from&.to_i
      age_to = @age_to&.to_i
      if age_from.present? && age_to == 0
        from_date = current_date - age_from.year - 1.year + 1.day
        to_date = current_date - age_from.year
      elsif age_to.present? && age_from == 0
        from_date = current_date - age_to.year - 1.year + 1.day
        to_date = current_date - age_to.year
      else
        from_date = current_date - age_to.year - 1.year + 1.day
        to_date = current_date - age_from.year
      end
      relation.where!('employees.birthday >= ? AND employees.birthday <= ?', from_date, to_date)
    end
    relation
  end

  private

  attr_reader(
    :age_from,
    :age_to,
    :relation
  )
end