# frozen_string_literal: true

class EmployeesWithDoNotMailQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, do_not_mail) # rubocop:disable Style/OptionalArguments
    @do_not_mail = do_not_mail
    @relation = relation
  end

  def call
    relation
    relation.where!(do_not_mail: do_not_mail)
    relation
  end

  private

  attr_reader :do_not_mail, :relation
end
