# frozen_string_literal: true

class EmployeesWithEmploymentStatusDateRangeQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_employment_statuses: :employment_status)

    if start_date.present?
      relation.where!('(employee_employment_statuses.start_date is NULL AND employee_employment_statuses.end_date is NOT NULL) OR employee_employment_statuses.start_date >= ?', start_date)
              .references!(:employee_employment_statuses)
    end

    if end_date.present?
      relation.where!('(employee_employment_statuses.start_date is NOT NULL AND employee_employment_statuses.end_date is Null) OR employee_employment_statuses.end_date <= ?', end_date)
              .references!(:employee_employment_statuses)
    end

    relation.where!('employee_employment_statuses.start_date >= ?', start_date_from).references!(:employee_employment_statuses) if start_date_from.present?

    relation.where!('employee_employment_statuses.start_date <= ?', start_date_to).references!(:employee_employment_statuses) if start_date_to.present?

    relation.where!('employee_employment_statuses.end_date >= ?', end_date_from).references!(:employee_employment_statuses) if end_date_from.present?

    relation.where!('employee_employment_statuses.end_date <= ?', end_date_to).references!(:employee_employment_statuses) if end_date_to.present?

    relation
  end

  private

  attr_reader :relation, :params

  def start_date
    params[:employment_statuses_from]
  end

  def end_date
    params[:employment_statuses_to]
  end

  def start_date_from
    params[:employment_statuses_start_date_from]
  end

  def start_date_to
    params[:employment_statuses_start_date_to]
  end

  def end_date_from
    params[:employment_statuses_end_date_from]
  end

  def end_date_to
    params[:employment_statuses_end_date_to]
  end
end
