# frozen_string_literal: true

class EmployeesWithDisciplineSettingsTaImplementedQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:employee_discipline_settings)
    relation.where!(employee_discipline_settings: {ta_implemented: ta_implemented}) unless ta_implemented.nil?

    relation
  end

  private

  attr_reader :relation, :params

  def ta_implemented
    params[:ta_implemented] if params[:ta_implemented] == "true"
  end
end
