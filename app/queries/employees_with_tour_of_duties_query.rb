# frozen_string_literal: true

class EmployeesWithTourOfDutiesQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    tour_of_duty_ids = params[:tour_of_duty_ids]
    @relation = relation
    @tour_of_duty_ids = if tour_of_duty_ids.present? && tour_of_duty_ids.any?('0')
                          TourOfDuty.kept.ids
                        else
                          tour_of_duty_ids
                        end
    @report_columns = params[:columns] || []
  end

  def call
    return relation if tour_of_duty_ids_blank?

    relation.includes!(:tour_of_duty) if report_columns.include?('tour_of_duty')
    relation.where!(tour_of_duty_id: tour_of_duty_ids)
    relation
  end

  private

  attr_reader :relation, :tour_of_duty_ids, :report_columns

  def tour_of_duty_ids_blank?
    (tour_of_duty_ids.is_a?(Array) && (tour_of_duty_ids.length == 1) && tour_of_duty_ids.first.blank?) || tour_of_duty_ids.blank?
  end
end
