# frozen_string_literal: true

class EmployeesWithDisciplineSettingsOlrDateRangeQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_discipline_settings: :discipline_setting)

    relation.where!('employee_discipline_settings.filed_olr >= ?', olr_from_date)
        .references!(:employee_discipline_settings) if olr_from_date.present?

    relation.where!('employee_discipline_settings.filed_olr <= ?', olr_to_date)
        .references!(:employee_discipline_settings) if olr_to_date.present?

    relation
  end

  private

  attr_reader :relation, :params

  def olr_from_date
    params[:filed_olr_from_date]
  end

  def olr_to_date
    params[:filed_olr_to_date]
  end
end
