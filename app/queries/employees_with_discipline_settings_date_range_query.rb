# frozen_string_literal: true

class EmployeesWithDisciplineSettingsDateRangeQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:employee_discipline_settings)

    relation.where!('employee_discipline_settings.date >= ?', start_date)
            .references!(:employee_discipline_settings) if start_date.present?

    relation.where!('employee_discipline_settings.date <= ?', end_date)
            .references!(:employee_discipline_settings) if end_date.present?

    relation
  end

  private

  attr_reader :relation, :params

  def start_date
    params[:start_date]
  end

  def end_date
    params[:end_date]
  end
end
