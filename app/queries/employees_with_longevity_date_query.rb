# frozen_string_literal: true

class EmployeesWithLongevityDateQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, from, to) # rubocop:disable Style/OptionalArguments
    @from = from
    @to = to
    @relation = relation
  end

  def call
    relation
    relation.where!('employees.longevity_date >= ?', from) if from.present?
    relation.where!('employees.longevity_date <= ?', to) if to.present?
    relation
  end

  private

  attr_reader(
    :from,
    :to,
    :relation
  )
end
