# frozen_string_literal: true

class EmployeesWithDisciplineSettingsEmployeePdsQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:employee_discipline_settings)
    relation.where!(employee_discipline_settings: {was_employee_pds: was_employee_pds}) unless was_employee_pds.nil?

    relation
  end

  private

  attr_reader :relation, :params

  def was_employee_pds
    params[:was_employee_pds] if params[:was_employee_pds] == "true"
  end
end
