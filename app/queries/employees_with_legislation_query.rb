# frozen_string_literal: true

class EmployeesWithLegislationQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  # rubocop:disable Metrics/AbcSize, Layout/LineLength
  def call
    relation.includes!(:legislative_address)
    relation.where!("legislation_details->'congress_member_details'->>'district' = ?", district_name('congress_member_details', params[:congress_district_id])).references!(:legislative_addresses) if params[:congress_district_id].present?
    relation.where!("legislation_details->'assembly_member_details'->>'district' = ?", district_name('assembly_member_details', params[:assembly_district_id])).references!(:legislative_addresses) if params[:assembly_district_id].present?
    relation.where!("legislation_details->'senate_member_details'->>'district' = ?", district_name('senate_member_details', params[:senate_district_id])).references!(:legislative_addresses) if params[:senate_district_id].present?
    relation.where!("legislation_details->'council_member_details'->>'district' = ?", district_name('council_member_details', params[:council_district_id])).references!(:legislative_addresses) if params[:council_district_id].present?

    relation
  end

  # rubocop:enable Metrics/AbcSize, Layout/LineLength

  private

  attr_reader(
    :relation,
    :params
  )

end

def district_name(district_type, id)
  legislative_address = LegislativeAddress.where(id: id)
  [legislative_address.first.legislation_details.dig(district_type.to_s, 'district')]

end