# frozen_string_literal: true

class ClassActionGrievanceQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    grievances = EmployeeGrievance.kept
    employee_ids = relation.pluck(:id).uniq  if employee_columns_present?
    grievances = grievances.where('employee_id IN (:ids) OR employee_ids && ARRAY[:ids]::text[]', ids: employee_ids.map(&:to_s)) if employee_ids.present?

    grievances = grievances.where(status: filter_ids(params[:grievance_status_ids], 'status_ids')) unless ids_blank?(params[:grievance_status_ids])
    grievances = grievances.where('employee_grievances.date >= ?', params[:start_date]) if params[:start_date].present?
    grievances = grievances.where('employee_grievances.date <= ?', params[:end_date]) if params[:end_date].present?
    grievances = grievances.where(grievance_id: filter_ids(params[:grievance_ids], 'grievance_ids')) unless ids_blank?(params[:grievance_ids])
    grievances = grievances.where(office_id: filter_ids(params[:grievance_command_ids], 'command_ids')) unless ids_blank?(params[:grievance_command_ids])

    if params[:pending_with_future_hearing] == 'true' && params[:pending_with_past_hearing] == 'true'
      grievances = grievances.includes(employee_grievance_steps: :hearings).where('status = 0 AND employee_grievance_steps.step = 1 AND (hearings.hearing_date > ? OR hearings.hearing_date < ?)', Date.today, Date.today)
                             .references(employee_grievance_steps: :hearings)
    elsif params[:pending_with_future_hearing] == 'true'
      grievances = grievances.includes(employee_grievance_steps: :hearings).where('status = 0 AND employee_grievance_steps.step = 1 AND hearings.hearing_date > ?', Date.today)
                             .references(employee_grievance_steps: :hearings)
    elsif params[:pending_with_past_hearing] == 'true'
      grievances = grievances.includes(employee_grievance_steps: :hearings).where('status = 0 AND employee_grievance_steps.step = 1 AND hearings.hearing_date < ?', Date.today)
                             .references(employee_grievance_steps: :hearings)
    end

    grievances = grievances.where(number: '') if params[:no_grievance_number] == 'true'

    grievances
  end

  private

  attr_reader :relation, :params

  def filter_ids(param_ids, column)
    return param_ids unless param_ids.present? && param_ids.any?('0')
    return Grievance.kept.ids if column == 'grievance_ids'
    return EmployeeGrievance.statuses.values if column == 'status_ids'

    Office.kept.ids if column == 'command_ids'
  end

  def ids_blank?(ids)
    (ids.is_a?(Array) && (ids.length == 1) && ids.first.blank?) || ids.blank?
  end

  def employee_columns_present?
    params.slice(:employee_ids, :employment_status_ids, :office_ids, :previous_shield_number, :birthday_from, :birthday_to, :age_from, :age_to, :start_date_from, :start_date_to).values.flatten.any?(&:present?)
  end
end
