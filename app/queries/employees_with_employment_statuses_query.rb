# frozen_string_literal: true

class EmployeesWithEmploymentStatusesQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, employment_status_ids = nil, end_dates_present = nil)
    @relation = relation
    @employment_status_ids = if employment_status_ids.present? && employment_status_ids.any?('0')
                               EmploymentStatus.kept.ids
                             else
                               employment_status_ids
                             end
    @end_dates_present = end_dates_present
  end

  def call
    return relation if employment_status_ids.blank? || employment_status_ids == ['']

    relation.includes!(:employee_employment_statuses)
            .where!(employee_employment_statuses: { employment_status_id: employment_status_ids })
    unless end_dates_present
      relation.where!('employee_employment_statuses.end_date IS NULL OR employee_employment_statuses.end_date >= ?', Date.today)
    end

    relation
  end

  private

  attr_reader :relation, :employment_status_ids, :end_dates_present
end
