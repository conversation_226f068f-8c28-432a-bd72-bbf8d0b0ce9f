# frozen_string_literal: true

class EmployeesWithLodisQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:lodis)
    relation.where!(lodis: { lodi_type: lodi_type, discarded_at: nil })
    relation.where!('lodis.incident_date >= ?', incident_date) if incident_date.present?
    relation.where!('lodis.return_date <= ?', return_date) if return_date.present?
    relation.where!('lodis.injury = ?', injury) if injury.present?
    relation
  end

  private

  attr_reader(
    :relation,
    :params
  )

  def incident_date
    params[:incident_date]
  end

  def return_date
    params[:return_date]
  end

  def lodi_type
    params[:lodi_type] || "lodi"
  end

  def injury
    params[:injury]
  end
end
