# frozen_string_literal: true

class EmployeesWithFacilityQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, facility_ids = nil)
    @relation = relation
    @facility_ids = if facility_ids.present? && facility_ids.any?('0')
                        Facility.kept.ids
                      else
                        facility_ids
                      end
  end

  def call
    if (@facility_ids.is_a?(Array) && (@facility_ids.length == 1) && @facility_ids.first.blank?) || @facility_ids.blank?
      return relation
    end

    relation.includes!(:employee_facilities).where!(employee_facilities: { facility_id: @facility_ids })
            .where!('employee_facilities.end_date is NULL OR employee_facilities.end_date >= ?', Date.today)
  end

  private

  attr_reader :relation, :facility_ids
end
