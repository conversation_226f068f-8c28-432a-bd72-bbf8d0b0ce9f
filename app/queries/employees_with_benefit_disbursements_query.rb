# frozen_string_literal: true

class EmployeesWithBenefitDisbursementsQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_benefits: [:benefit, { benefit_disbursements: :payment_type }])

    unless payment_type_ids_blank?
      relation.where!(employee_benefits: { benefit_disbursements: { payment_type_id: payment_type_ids } })
    end

    relation.where!(employee_benefits: { benefit_disbursements: { year: disbursement_year } }) if disbursement_year.present?

    if disbursement_start_date.present?
      relation.where!('benefit_disbursements.date >= ?', disbursement_start_date).references!(:benefit_disbursements)
    end
    if disbursement_end_date.present?
      relation.where!('benefit_disbursements.date <= ?', disbursement_end_date).references!(:benefit_disbursements)
    end
    relation
  end

  private

  attr_reader(
    :relation,
    :params
  )

  def payment_type_ids
    params[:payment_type_ids]
  end

  def disbursement_year
    params[:disbursement_year]
  end

  def disbursement_start_date
    params[:disbursement_start_date]
  end

  def disbursement_end_date
    params[:disbursement_end_date]
  end

  def payment_type_ids_blank?
    (payment_type_ids.is_a?(Array) && (payment_type_ids.length == 1) && payment_type_ids.first.blank?) ||
      payment_type_ids.blank?
  end
end
