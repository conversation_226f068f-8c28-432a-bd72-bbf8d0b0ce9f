# frozen_string_literal: true

class EmployeesWithDisciplineChargeQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_discipline_settings: :discipline_charge)
    relation.where!(employee_discipline_settings: { discipline_charge_id: discipline_charge_ids }) unless discipline_charge_ids_blank?

    relation
  end

  private

  attr_reader :relation, :params

  def discipline_charge_ids
    if params[:discipline_charge_ids].present? && params[:discipline_charge_ids].any?('0')
      DisciplineCharge.kept.ids
    else
      params[:discipline_charge_ids]
    end
  end

  def discipline_charge_ids_blank?
    (discipline_charge_ids.is_a?(Array) && (discipline_charge_ids.length == 1) && discipline_charge_ids.first.blank?) || discipline_charge_ids.blank?
  end

end
