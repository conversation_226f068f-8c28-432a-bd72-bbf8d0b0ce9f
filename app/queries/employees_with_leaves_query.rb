# frozen_string_literal: true

class EmployeesWithLeavesQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:leaves)
    relation.where!(leaves: { leave_type: leave_type, discarded_at: nil })
    relation.where!('leaves.started_at >= ?', started_at) if started_at.present?
    relation.where!('leaves.ended_at <= ?', ended_at) if ended_at.present?
    relation
  end

  private

  attr_reader(
    :relation,
    :params
  )

  def leave_type
    params[:leave_type]
  end

  def started_at
    params[:started_at]
  end

  def ended_at
    params[:ended_at]
  end
end
