# frozen_string_literal: true

class EmployeesWithBenefitsQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:employee_benefits)
    relation.where!(employee_benefits: { benefit_id: benefit_ids }) unless benefit_ids_blank?
    relation.where!('employee_benefits.start_date >= ?', start_date).references!(:employee_benefits) if start_date.present?
    relation.where!('employee_benefits.start_date <= ?', end_date).references!(:employee_benefits) if end_date.present?
    relation
  end

  private

  attr_reader(
    :relation,
    :params
  )

  def benefit_ids
    if params[:benefit_ids].present? && params[:benefit_ids].any?('0')
      Benefit.kept.ids
    else
      params[:benefit_ids]
    end
  end

  def start_date
    params[:start_date]
  end

  def end_date
    params[:end_date]
  end

  def benefit_ids_blank?
    (benefit_ids.is_a?(Array) && (benefit_ids.length == 1) && benefit_ids.first.blank?) || benefit_ids.blank?
  end
end
