# frozen_string_literal: true

class EmployeesWithSectionsQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, section_ids = nil)
    @relation = relation
    @section_ids = if section_ids.present? && section_ids.any?('0')
                     Section.kept.ids
                   else
                     section_ids
                   end
  end

  def call
    if (section_ids.is_a?(Array) && (section_ids.length == 1) && section_ids.first.blank?) || section_ids.blank?
      return relation
    end

    relation.includes!(:employee_sections).where!(employee_sections: { section_id: section_ids })
            .where!('employee_sections.end_date is NULL OR employee_sections.end_date >= ?', Date.today)
  end

  private

  attr_reader :relation, :section_ids
end
