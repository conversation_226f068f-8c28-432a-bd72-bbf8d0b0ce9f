# frozen_string_literal: true

module ReportGenerator
  module Excel
    class BenefitCoverageExcel < ApplicationExcel
      # rubocop:disable Metrics/ParameterLists
      def self.create(object, filepath, show_coverages, current_account, columns = [], associations)
        new(object, filepath, show_coverages, current_account, columns, associations).create
      end

      def initialize(object, filepath, show_coverages, current_account, columns, associations)
        @object = object
        @filepath = filepath
        @show_coverages = show_coverages
        @current_account = current_account
        @columns = columns
        @associations = associations
      end

      private

      attr_reader :object, :filepath, :columns, :current_account, :show_coverages, :associations

      def create_worksheets
        ReportGenerator::Excel::Worksheets::BenefitCoverageWorksheet.create(workbook, object, show_coverages, current_account, columns, associations)
      end

      # rubocop:enable Metrics/ParameterLists
    end
  end
end
