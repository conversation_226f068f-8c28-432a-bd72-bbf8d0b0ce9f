# frozen_string_literal: true

module ReportGenerator
  module Excel
    class GvsExcel < ApplicationExcel
      def self.create(object, filepath, current_account, columns = [],account,benefit)
        new(object, filepath, current_account, columns, account, benefit).create
      end

      def initialize(object, filepath, current_account, columns, account, benefit)
        @object = object
        @filepath = filepath
        @current_account = current_account
        @columns = columns
        @account = account
        @benefit = benefit
      end
      attr_reader :object, :filepath, :columns, :current_account, :account, :benefit

      private

      def create_worksheets
        ReportGenerator::Excel::Worksheets::GvsWorksheet.create(workbook, object, current_account, account, benefit)
      end
    end
  end
end
