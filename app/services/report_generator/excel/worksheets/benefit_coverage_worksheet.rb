# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class BenefitCoverageWorksheet < ApplicationWorksheet
        # rubocop:disable Metrics/ParameterLists
        def self.create(workbook, object, show_coverages, current_account = nil, columns = [], associations)
          new(workbook, object, show_coverages, current_account, columns, associations).create
        end

        def initialize(workbook, object, show_coverages, current_account, columns, associations)
          @workbook = workbook
          @object = object
          @show_coverages = show_coverages
          @columns = columns
          @current_account = current_account
          @associations = associations
        end

        private

        attr_reader :workbook, :object, :columns, :current_account, :show_coverages, :associations

        def report_type
          Report::ReportTypes::BENEFIT_COVERAGES
        end

        def create_rows # rubocop:disable Metrics/MethodLength, Metrics/BlockLength
          index = 0
          current_account = Account.find_by(subdomain: Apartment::Tenant.current)

          total_dependents_count = 0
          coverages = {}
          employee_columns = columns.present? ? columns : []
          group_by_name = current_account.saas_json.dig('schema', 'benefit_coverages', 'name').present?
          associations << [:benefit_coverages, :employee_benefits, { employee_benefits: [:benefit_coverages, :benefit] }]
          Employee.includes(associations.uniq.flatten).where(id: object).find_each do |employee|
            employee_coverages = employee.benefit_coverages
            data = []
            data << [employee.name, employee.shield_number || '-']
            empty_data = Array.new(employee_columns.count + 2)
            data << ::Generators::EmployeeDataGenerator.generate(employee, employee_columns, current_account) if employee_columns.count > 0
            if show_coverages == 'grouped' && employee_coverages.present?
              grouped_coverages = if group_by_name
                                    employee_coverages.group_by { |record| [record.name, record.relationship, record.birthday, record.social_security_number] }

                                  else
                                    employee_coverages.group_by { |record| [record.first_name, record.last_name, record.relationship, record.birthday, record.social_security_number] }
                                  end
              grouped_coverages.each_with_index do |(key, _grouped_coverage), indx|
                if indx != 0
                  data = []
                  data << empty_data
                end
                name = group_by_name ? "#{key[0]} #{key[1]}" : key[0]
                data << [name, key[-3], DateFormatterHelper.format_report_date(key[-2]), key[-1]]
                data.flatten!
                index += 1
                worksheet.insert_row(index, data)
              end
            elsif show_coverages != 'grouped' && (employee_benefits = employee.employee_benefits).present?
              employee_benefits.each_with_index do |employee_benefit, indx|
                if indx != 0
                  data = []
                  data << empty_data
                end
                employee_benefit_data = []
                employee_benefit_coverages = employee_benefit.benefit_coverages
                benefit_coverages = if current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship') == true
                                      employee_coverages.order('relationship DESC, birthday ASC')
                                    else
                                      employee_benefit_coverages
                                    end

                employee_benefit_data << employee_benefit.benefit&.name || ''
                employee_benefit_data << DateFormatterHelper.format_report_date(employee_benefit.start_date)
                employee_benefit_data << DateFormatterHelper.format_report_date(employee_benefit.end_date)
                total_dependents_count += benefit_coverages.length
                benefit_coverages.group_by(&:relationship.downcase).each do |coverage, value|
                  next if coverage.blank?

                  coverages[coverage] ||= 0
                  coverages[coverage] += value.length
                end
                data << [
                  employee_benefit.name,
                  DateFormatterHelper.format_report_date(employee_benefit.start_date),
                  DateFormatterHelper.format_report_date(employee_benefit.end_date)
                ]

                if (show_coverages == 'all' || show_coverages == 'true') && benefit_coverages.present?
                  benefit_coverages.each_with_index do |benefit_coverage, idx|
                    if idx != 0
                      data = []
                      data << empty_data + [nil, nil, nil]
                    end

                    data << [
                      group_by_name ? benefit_coverage.name : "#{benefit_coverage.first_name} #{benefit_coverage.last_name}",
                      benefit_coverage.relationship&.humanize&.titleize,
                      DateFormatterHelper.format_report_date(benefit_coverage.birthday),
                      benefit_coverage.social_security_number
                    ]

                    data.flatten!
                    index += 1
                    worksheet.insert_row(index, data)
                  end
                else
                  data.flatten!
                  index += 1
                  worksheet.insert_row(index, data)
                end
              end
            else
              data.flatten!
              index += 1
              worksheet.insert_row(index, data)
            end
          end

          return unless show_coverages == 'true' || show_coverages == 'all'

          index += 1
          array = Array.new(employee_columns.count + 9)
          worksheet.insert_row(index, array)
          coverages.each do |coverage, value|
            index += 1
            worksheet.insert_row(index, array + [(coverage.blank? ? 'Total dependents without a Relationship defined :' : "Total #{coverage.titleize} :"), value])
          end
          index += 1
          worksheet.insert_row(index, array + ['Total Dependents :', total_dependents_count])
        end

        # rubocop:enable Metrics/ParameterLists,  Metrics/BlockLength
      end
    end
  end
end
