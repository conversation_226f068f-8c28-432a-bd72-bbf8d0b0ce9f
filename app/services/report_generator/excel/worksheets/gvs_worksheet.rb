# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class GvsWorksheet < ApplicationWorksheet
        def self.create(workbook, object, current_account = nil, columns = [], account, benefit)
          new(workbook, object, current_account, columns, account, benefit).create
        end

        def initialize(workbook, object, current_account, columns, account, benefit)
          @workbook = workbook
          @object = object
          @columns = columns
          @current_account = current_account
          @account = account
          @benefit = benefit
        end

        attr_reader :workbook, :object, :columns, :current_account, :account, :benefit

        private

        def report_type
          Report::ReportTypes::GVS
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          index = 0
          optical_benefit = Benefit.includes(employee_benefits: :benefit_coverages).where(name: benefit).first
          associate_status, excluded_status, allow_expired_dependents = get_associate_status_name(account, benefit)

          object.each do |member|
            status = member.send('employee_' + associate_status.pluralize).kept.where('end_date is NULL OR end_date >= ?', Date.today).first
            status_name = status.send(associate_status)&.name if status.present?
            status_lower = status_name&.downcase&.delete(" ")

            if (status_name.present? && excluded_status.exclude?(status_name.downcase.delete(" "))) && allow_expired_dependents != true || allow_expired_dependents == true || status.blank?
              if allow_expired_dependents == true
                emp_benefits = member.employee_benefits.where('benefit_id = ?', optical_benefit.id)
              else
                emp_benefits = member.employee_benefits.where('benefit_id = ? and (end_date is null or end_date >= ?)', optical_benefit.id, Date.today)
              end
              coverages = member.benefit_coverages.where('employee_benefit_id in (?) and (expires_at is null or expires_at >= ?)', emp_benefits&.ids, Date.today)
              next if (status_lower == 'recruit' && allow_expired_dependents == true && account == "nysscoa") # || (coverages.blank? && allow_expired_dependents == true && excluded_status.include?(status_lower))
              data = []
              address_data = []
              member_ssn = member.social_security_number
              member_united_health_care = member.previous_shield_number
              if account == "cobanc"
                data << format_text_length(member_united_health_care, 15)
              else
                data << format_text_length(member_ssn.delete('-'), 15) # ssn
              end

              data << 'M' # Relationship
              data << format_text_length(member.last_name, 24) # last_name
              data << format_text_length(member.first_name, 15) # first_name

              address_data << format_text_length(member.street, 50) # addr1
              address_data << format_text_length(member.apartment, 50) # addr2
              address_data << format_text_length(member.city, 25) # city
              address_data << format_text_length(member.state, 2) # state
              address_data << format_text_length(member.zipcode, 9) # zip

              data << address_data
              data << format_text_length(format_date(member.birthday), 8) # dob



              emp_benefits.each do |emp_benefit|
                data << format_text_length(format_date(emp_benefit.start_date), 8) # from date
                data << format_text_length(format_date(emp_benefit.end_date), 8) # end date
                data << format_text_length(format_date(emp_benefit.serviced_expiration), 8) if ['nysscoa'].exclude?(account) # dos
              end
              data << ['',''] if emp_benefits.blank?

              # max_disbursements_date = member.benefit_disbursements.where(employee_benefit: emp_benefits.pluck(:id)).maximum(:date)
              # data << date_of_service if ['nysscoa'].exclude?(account) # dos
              if account == "cobanc"
                status_name = "ACTIVE" if status_name&.downcase == 'cobra'
                data << status_name&.upcase
              end
              data << get_nysscoa_status_name(status_name) if account == 'nysscoa'

              data.flatten!
              worksheet.insert_row(index, data)
              index += 1


              coverages.each do |coverage|
                coverage_data = []
                coverage_ssn = coverage.social_security_number
                if account == "cobanc"
                  coverage_data << format_text_length(member_united_health_care, 15)
                else
                  coverage_data << format_text_length(member_ssn.delete('-'), 15) # ssn
                end
                coverage_data << format_relationship(coverage.relationship) # Relationship
                if benefit == 'Optical' && ['btoba', 'cobanc'].include?(account)
                  coverage_data << format_text_length(coverage.last_name, 24) # last_name
                  coverage_data << format_text_length(coverage.first_name, 15) # first_name
                elsif benefit == 'Vision' && account == 'nysscoa'
                  coverage_name = coverage.name
                  coverage_data << format_text_length(coverage_name.split(" ").last, 24) # last_name
                  coverage_data << format_text_length(coverage_name.split(" ").first, 15) # first_name
                end
                coverage_data << address_data
                coverage_data << format_text_length(format_date(coverage.birthday), 8) # dob
                coverage_start_date = (account == 'nysscoa' ? '********' : format_date(coverage.effective_date))
                coverage_data << format_text_length(coverage_start_date, 8) # from date
                coverage_data << format_text_length(format_date(coverage.expires_at), 8) # end date
                coverage_date_of_service = format_date(coverage.serviced_expiration)
                coverage_data << coverage_date_of_service if ['nysscoa'].exclude?(account) # dos
                coverage_data.flatten!
                worksheet.insert_row(index, coverage_data)
                index += 1
              end
            end
          end
        end

        def format_text_length(text, length)
          text&.ljust(length, ' ')&.slice(0, length)
        end

        def format_date(date)
          date.strftime('%m%d%Y') rescue ''
        end

        def format_relationship(relationship)
          if relationship.downcase == 'spouse'
            'S'
          elsif relationship.downcase == 'domestic partner' && ['cobanc', 'nysscoa'].include?(@account)
            'P'
          else
            'D' # child, step_child, disabled_child and disabled_step_child
          end
        end

        def get_nysscoa_status_name(status_name)
          return "" if status_name.blank?
          if %w[activefull-time military lodi modified probation sickbank workerscomp discretionaryornon-leave cobra].include?(status_name.downcase.delete(" "))
            name = "ACTIVE"
          elsif %w[activepart-time].include?(status_name.downcase.delete(" "))
            name = "PART TIME"
          elsif %w[disableretired retired].include?(status_name.downcase.delete(" "))
            name = "RETIRED"
          else
            name = ""
          end
          name
        end

        def get_associate_status_name(account, benefit)
          # If needed to exclude the below statuses, change the allow_expired_dependents to false for the needed domains, and check further logics.
          if benefit.present? && benefit == 'Optical' && account.present? && account == "btoba"
            associate_status = 'employment_status'
            excluded_status = %w(deceased promoted terminated transferred resigned inactive cobrainactive)
            allow_expired_dependents = false
          elsif benefit.present? && benefit == "Optical" && account.present? && account == "cobanc"
            associate_status = 'employment_status'
            excluded_status = %w(deceased pending terminated resigned transferred)
            allow_expired_dependents = false
          elsif benefit.present? && benefit == 'Vision' && account.present? && account == 'nysscoa'
            associate_status = 'officer_status'
            excluded_status = %w(opt-out promotedtoclerk resigned terminated transferred deceased nolongermember badaddress recruit)
            allow_expired_dependents = false
          end
          [associate_status, excluded_status, allow_expired_dependents]
        end
      end
    end
  end
end
