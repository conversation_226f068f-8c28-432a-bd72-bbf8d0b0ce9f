# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class EmployeeJanusWorksheet < ApplicationWorksheet
        private

        def report_type
          Report::ReportTypes::JANUS
        end

        def create_rows
          index = 0

          object.order_by_name.each do |employee|
            data = [
              employee.name,
              employee.janus_card ? 'Yes' : 'No',
              DateFormatterHelper.format_report_date(employee.janus_card_opt_out_date)
            ]
            index += 1
            worksheet.insert_row(index, data)
          end
        end

        def format_date(date)
          date.present? ? date.strftime('%m-%d-%Y') : ''
        end
      end
    end
  end
end
