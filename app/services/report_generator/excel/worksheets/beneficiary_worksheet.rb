# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class BeneficiaryWorksheet < ApplicationWorksheet
        def self.create(workbook, object, params, current_account = nil, columns = [])
          new(workbook, object, params, current_account, columns).create
        end

        def initialize(workbook, object, params, current_account, columns)
          @workbook = workbook
          @object = object
          @params = params
          @columns = columns
          @current_account = current_account
        end

        private

        attr_reader :workbook, :object, :columns, :current_account, :params

        def report_type
          Report::ReportTypes::BENEFICIARY
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          show_coverages = params[:show_coverages]
          index = 0
          current_account = Account.find_by(subdomain: Apartment::Tenant.current)

          object.order_by_name.each do |employee|
            # each_with_index is slower in this case
            data = []
            employee_data = []

            employee_data << employee.last_name
            employee_data << employee.first_name
            employee_data << employee.previous_shield_number
            data << employee_data

            beneficiaries = employee.beneficiaries

            if beneficiaries.present?
                beneficiary_data = []
                primary_beneficiary = beneficiaries.where(beneficiary_type: "Primary", discarded_at: nil)

                  beneficiary_data << primary_beneficiary.pluck(:name).join(" / ")
                  beneficiary_data << primary_beneficiary.sum(:percentage)
                  beneficiary_data << primary_beneficiary.pluck(:address).join(" / ")
                  beneficiary_data << primary_beneficiary.pluck(:relationship).join(" / ")
                  beneficiary_data << primary_beneficiary.pluck(:birthday).map {|x| DateFormatterHelper.format_report_date(x) }.join(" / ")

                secondary_beneficiary = beneficiaries.where(beneficiary_type: "Secondary", discarded_at: nil)
                  beneficiary_data << secondary_beneficiary.pluck(:name).join(" / ")
                  beneficiary_data << secondary_beneficiary.sum(:percentage)
                  beneficiary_data << secondary_beneficiary.pluck(:address).join(" / ")
                  beneficiary_data << secondary_beneficiary.pluck(:relationship).join(" / ")
                  beneficiary_data << secondary_beneficiary.pluck(:birthday).map {|x| DateFormatterHelper.format_report_date(x) }.join(" / ")

                data << beneficiary_data
                data.flatten!
                index += 1
                worksheet.insert_row(index, data)
            else
              data.flatten!
              index += 1
              worksheet.insert_row(index, data)
            end
          end
        end
      end
    end
  end
end
