# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class EmployeeUnionMeetingsWorksheet < ApplicationWorksheet
        private

        def report_type
          Report::ReportTypes::UNION_MEETINGS
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          index = 0

          object.order_by_name.each do |employee| # each_with_index is slower in this case
            employee.employee_meeting_types.each do |employee_meeting_type|
              data = [
                employee.name,
                employee_meeting_type.name,
                DateFormatterHelper.format_report_date(employee_meeting_type.meeting_date),
                StringFormatterHelper.boolean_value(employee_meeting_type.attended)
              ]
              index += 1
              worksheet.insert_row(index, data)
            end
          end
        end
      end
    end
  end
end
