# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class LifeInsuranceWorksheet < ApplicationWorksheet

        private

        def report_type
          Report::ReportTypes::LIFE_INSURANCE
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          index = 1

          member_contribution = current_account.saas_json.dig('schema', 'life_insurances', 'member_contribution')

          member_contribution_amount = {}

          if columns[:group_by_age].present?
            age_group = columns[:group_by_age].split("-")
            member_contribution.each do |member|
              if age_group.size == 1
                member_contribution_amount = member['amount'] if member['low'] == 1 && member['high'] == 29
              else
                member_contribution_amount = member['amount'] if member['low'] == age_group.first.to_i && member['high'] == age_group.second.to_i
              end
            end
          end

          if columns[:dob_group] == 'all_age'
            data = [
                '1-99',
                total_member_contribution(member_contribution)
            ]
          else
            data = [
                columns[:group_by_age],
                age_group_member_contribution(member_contribution_amount)
            ]
          end

          worksheet.insert_row(index, data)
        end


        def age_group_member_contribution(member_contribution_amount)

          sum = 0

          object.includes(:life_insurances).each do |employee|

            employee.life_insurances.each do |life_insurance|
              sum += member_contribution_amount["50000"] if life_insurance.amount == 50000
              sum += member_contribution_amount["100000"] if life_insurance.amount == 100000
              sum += member_contribution_amount["200000"] if life_insurance.amount == 200000
            end
          end

          sum
        end

        def total_member_contribution(member_contribution)
          sum = 0
          object.includes(:life_insurances).each do |employee|
            member_contribution_amount = get_amount(member_contribution, employee)

            employee.life_insurances.each do |life_insurance|
              sum += member_contribution_amount["50000"] if life_insurance.amount == 50000
              sum += member_contribution_amount["100000"] if life_insurance.amount == 100000
              sum += member_contribution_amount["200000"] if life_insurance.amount == 200000
            end
          end

          sum
        end

        def get_amount(member_contribution, employee)
          member_contribution_amount = {}
          member_contribution.each do |member|
            member_contribution_amount = member['amount'] if age(employee.birthday) >= member['low'] && age(employee.birthday) <= member['high']
          end

          member_contribution_amount
        end

        def age(dob)
          now = Date.today
          age = now.year - dob.year
          age = 1 if now.year == dob.year
          age
        end
      end
    end
  end
end
