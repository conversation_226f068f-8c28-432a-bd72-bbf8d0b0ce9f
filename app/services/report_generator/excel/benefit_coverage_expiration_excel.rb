# frozen_string_literal: true

module ReportGenerator
  module Excel
    class BenefitCoverageExpirationExcel < ApplicationExcel
      def self.create(object, filepath, params, current_account, columns = [])
        new(object, filepath, params, current_account, columns).create
      end

      def initialize(object, filepath, params , current_account, columns)
        @object = object
        @filepath = filepath
        @current_account = current_account
        @columns = columns
        @params = params
      end

      private

      attr_reader :object, :filepath, :columns, :current_account, :params

      def create_worksheets
        ReportGenerator::Excel::Worksheets::BenefitCoverageExpirationWorksheet.create(workbook, object, params, current_account)
      end
    end
  end
end
