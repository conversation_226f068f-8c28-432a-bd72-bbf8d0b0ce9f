# frozen_string_literal: true

module ReportGenerator
  class LodiReport < ApplicationReport
    private

    def report_type
      Report::ReportTypes::LODI
    end

    def filename
      @filename ||= "lodi-report-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      return unless report_format == 'pdf'

      ReportGenerator::Pdf::EmployeeLodisPdf.create(employees, filename, filepath, current_account)
    end

    def generate_xls
      return unless report_format == 'xls'

      ReportGenerator::Excel::EmployeeLodisExcel.create(employees, filepath, current_account)
    end
  end
end
