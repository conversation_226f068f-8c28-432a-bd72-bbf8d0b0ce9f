# frozen_string_literal: true

module ReportGenerator
  class GrievanceTemplateReport < ApplicationReport
    def generate
      report.format = report_format
      report.save!

      generate_pdf
      save_report
    end

    private

    def report_type
      Report::ReportTypes::GRIEVANCE_TEMPLATE
    end

    def filename
      @filename ||= "grievance-#{params[:template_name]}-template-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      return unless report_format == 'pdf'

      employee_grievance = EmployeeGrievance.find(params[:employee_grievance_id])
      ReportGenerator::Pdf::GrievanceTemplatesPdf.create(employee_grievance, filename, filepath, current_account, params[:template_name])
    end
  end
end
