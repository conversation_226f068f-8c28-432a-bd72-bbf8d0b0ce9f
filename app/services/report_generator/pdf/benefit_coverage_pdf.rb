# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class BenefitCoveragePdf < ApplicationPdf
      def self.create(object, filepath, show_coverages = true, current_account = nil, columns = nil, associations)
        new(object, filepath, show_coverages, current_account, columns, associations).create
      end

      def initialize(object, filepath, show_coverages, current_account, columns, associations)
        @object = object
        @filepath = filepath
        @show_coverages = show_coverages
        @current_account = current_account
        @columns = columns
        @associations = associations
      end

      private

      attr_reader :show_coverages, :columns, :associations, :current_account

      def pdf # rubocop:disable Metrics/MethodLength
        associations << [:benefit_coverages, :employee_benefits, { employee_benefits: [:benefit_coverages, :benefit] }]
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: {
            employee_ids: object,
            columns: columns,
            show_coverages: show_coverages,
            associations: associations.uniq.flatten,
            current_account: current_account
          },
          assigns: {
            current_account: current_account
          },
          pdf: "benefit_coverage-report-#{current_account}-#{Time.now.strftime('%Y-%m-%d_%H-%M-%S')}.pdf",
          template: 'templates/benefit_coverages/index_pdf',
          layout: 'pdf.html',
          padding: {
            top: 30
          },
          header: {
            html: {
              template: 'layouts/header',
              locals: {
                title: report_title_translation(Report::ReportTypes::BENEFIT_COVERAGES)
              },
              assigns: {
                current_account: current_account
              }
            },
            spacing: 6
          },
          footer: {
            font_size: 10,
            right: '[page] of [topage]'
          }
        )
      end
    end
  end
end
