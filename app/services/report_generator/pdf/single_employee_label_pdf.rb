# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class SingleEmployeeLabelPdf < ApplicationPdf
      private

      def pdf # rubocop:disable Metrics/MethodLength
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: { employees: object.order_by_name },
          margin: {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0
          },
          page_height: 27,
          page_width: 75,
          pdf: filename,
          template: 'templates/single_employee_label_pdf'
        )
      end
    end
  end
end
