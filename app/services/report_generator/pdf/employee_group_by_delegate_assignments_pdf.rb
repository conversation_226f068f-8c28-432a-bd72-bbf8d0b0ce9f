# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class EmployeeGroupByDelegateAssignmentsPdf < ApplicationPdf
      private

      def pdf # rubocop:disable Metrics/MethodLength
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: {
            employees: object
          },
          assigns: {
            current_account: current_account
          },
          pdf: filename,
          template: 'templates/employee_delegate_assignments/reverse_index_pdf',
          layout: 'pdf.html',
          padding: {
            top: 30
          },
          header: {
            html: {
              template: 'layouts/header',
              locals: {
                title: report_title_translation(Report::ReportTypes::EMPLOYEE_DELEGATE_ASSIGNMENT)
              },
              assigns: {
                current_account: current_account
              }
            },
            spacing: 6
          },
          footer: {
            font_size: 10,
            right: '[page] of [topage]'
          }
        )
      end
    end
  end
end
