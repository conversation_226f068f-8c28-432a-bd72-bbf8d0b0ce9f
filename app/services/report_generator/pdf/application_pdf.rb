# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class ApplicationPdf
      include SaasTranslationsHelper

      def self.create(object, filename, filepath, current_account = nil)
        new(object, filename, filepath, current_account).create
      end

      def initialize(object, filename, filepath, current_account)
        @object = object
        @filename = filename
        @filepath = filepath
        @current_account = current_account
      end

      def create
        File.open(filepath, 'wb') do |file|
          file << pdf
        end
      end

      private

      attr_reader :object, :filename, :filepath, :current_account

      def ac
        @ac ||= ApplicationController.new
      end

      def pdf
        raise NotImplementedError
      end
    end
  end
end
