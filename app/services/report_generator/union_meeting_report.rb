# frozen_string_literal: true

module ReportGenerator
  class UnionMeetingReport < ApplicationReport
    private

    def report_type
      Report::ReportTypes::UNION_MEETINGS
    end

    def filename
      @filename ||= "union-meetings-report-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      return unless report_format == 'pdf'

      ReportGenerator::Pdf::EmployeeUnionMeetingsPdf.create(employees, filename, filepath, current_account)
    end

    def generate_xls
      return unless report_format == 'xls'

      ReportGenerator::Excel::EmployeeUnionMeetingsExcel.create(employees, filepath, current_account)
    end
  end
end
