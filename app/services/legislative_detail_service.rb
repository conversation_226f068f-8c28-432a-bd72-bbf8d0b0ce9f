class LegislativeDetailService
  require "net/http"
  require "json"
  require "openssl"

  def initialize(employee)
    @employee = employee
  end

  # rubocop:disable Metrics/AbcSize, Metrics/PerceivedComplexity

  def fetch
    google_api_key = Rails.application.credentials.google_api_key
    address = CGI::escape(@employee.street + ", " + @employee.city + ", " + @employee.state + " " + @employee.zipcode)
    url = "https://content-civicinfo.googleapis.com/civicinfo/v2/representatives?address=#{address}&includeOffices=true&key=#{google_api_key}"
    uri = URI(url)
    response_for_request = Net::HTTP.get_response(uri)
    parsed_response = JSON.parse(response_for_request.body)
    response_code = response_for_request.code
    response = parsed_response.to_h
    all_officers_json = {}
    legislation_details = { senate_member_details: { name: '', website: '', district: '' },
                            congress_member_details: { name: '', website: '', district: '' },
                            assembly_member_details: { name: '', website: '', district: '' },
                            council_member_details: { name: '', website: '', district: '' },
                            comptroller_member_details: { name: '', website: '' },
                            attorney_member_details: { name: '', website: '' },
                            executive_member_details: { name: '', website: '' },
                            county_details: { county_name: '' } }

    if response['offices'].present?
      response_offices_handling(response, all_officers_json)
      response_dup = response.deep_dup ## Here we are duplicating the response, to avoid modifying the original response.

      ## SenatorName, AssemblyName, CongressName,CouncilName etc..
      get_officer_names(response_dup, all_officers_json, legislation_details)

      ## SenatorDistrict, AssemblyDistrict, CongressDistrict, CouncilDistrict etc...
      districts = response['divisions'].values.pluck('name')
      get_officer_district_names(districts, legislation_details)

      ## SenatorWebsite, AssemblyWebsite, CongressWebsite, CouncilWebsite etc...
      get_officer_website(response_dup, all_officers_json, legislation_details)

      ## Check if already exists, And creating legislative details and addresses if blank
      create_legislative_address(legislation_details, @employee, response_code, response_dup)
    end
  end

  def create_legislative_address(legislation_details, employee, code, response)
    legislative_detail_address = LegislativeAddress.where('lower(street) = ? and lower(city) = ? and lower(state) = ? and zipcode = ?',
                                                          employee.street&.downcase, employee.city&.downcase, employee.state&.downcase,
                                                          employee.zipcode).order(updated_at: :desc).first_or_initialize
    legislative_detail_address.street = employee.street&.downcase
    legislative_detail_address.city = employee.city&.downcase
    legislative_detail_address.state = employee.state&.downcase
    legislative_detail_address.zipcode = employee.zipcode
    legislative_detail_address.legislation_details = legislation_details
    legislative_detail_address.response_code = code
    legislative_detail_address.response_details = response
    legislative_detail_address.save!
    legislative_detail_address
  end

  # def get_query(key, value, officer_details = nil)
  #   query = if officer_details.present? && value.present?
  #             "legislation_details->'#{officer_details}'->>'#{key}' = ? and "
  #           elsif officer_details.present? && value.blank?
  #             "(legislation_details->'#{officer_details}'->>'#{key}' = ?) OR (other_details->'#{officer_details}'->>'#{key}' IS NULL) and "
  #           end
  #   return query unless officer_details == 'executive_member_details'
  #
  #   query.gsub('and', '')
  # end

  def response_offices_handling(response, all_officers_json)
    response['offices'].each do |office|
      if office['name'].downcase.delete(' ').include?('statesenator')
        all_officers_json['senator'] = office['officialIndices'].first
      elsif office['name'].downcase.delete(' ').include?('stateassemblymember')
        all_officers_json['assembly'] = office['officialIndices'].first
      elsif office['name'].downcase.delete(' ').include?('representative')
        all_officers_json['congress'] = office['officialIndices'].first
      elsif office['name'].downcase.delete(' ').include?('council')
        all_officers_json['council'] = office['officialIndices'].first
      elsif office['name'].downcase.delete(' ').include?('executive')
        all_officers_json['executive'] = office['officialIndices'].first
      elsif office['name'].downcase.delete(' ').include?('districtattorney')
        all_officers_json['districtattorney'] = office['officialIndices'].first
      elsif office['name'].downcase.delete(' ').include?('citycomptroller')
        all_officers_json['citycomptroller'] = office['officialIndices'].first
      end
    end
  end

  def get_officer_names(response_dup, all_officers_json, legislation_details)
    legislation_details[:senate_member_details][:name] = all_officers_json['senator'].present? ? response_dup.dig('officials', all_officers_json['senator'], 'name') : ''
    legislation_details[:assembly_member_details][:name] = all_officers_json['assembly'].present? ? response_dup.dig('officials', all_officers_json['assembly'], 'name') : ''
    legislation_details[:congress_member_details][:name] = all_officers_json['congress'].present? ? response_dup.dig('officials', all_officers_json['congress'], 'name') : ''
    legislation_details[:council_member_details][:name] = all_officers_json['council'].present? ? response_dup.dig('officials', all_officers_json['council'], 'name') : ''
    legislation_details[:comptroller_member_details][:name] = all_officers_json['citycomptroller'].present? ? response_dup.dig('officials', all_officers_json['citycomptroller'], 'name') : ''
    legislation_details[:attorney_member_details][:name] = all_officers_json['districtattorney'].present? ? response_dup.dig('officials', all_officers_json['districtattorney'], 'name') : ''
    legislation_details[:executive_member_details][:name] = all_officers_json['executive'].present? ? response_dup.dig('officials', all_officers_json['executive'], 'name') : ''
  end

  def get_officer_district_names(districts, legislation_details)
    districts.each do |district|
      if district.downcase.delete(' ').include?('assemblydistrict')
        legislation_details[:assembly_member_details][:district] = district
      elsif district.downcase.delete(' ').include?('senatedistrict')
        legislation_details[:senate_member_details][:district] = district
      elsif district.downcase.delete(' ').include?('congressionaldistrict')
        legislation_details[:congress_member_details][:district] = district
      elsif district.downcase.delete(' ').include?('councildistrict')
        legislation_details[:council_member_details][:district] = district
      elsif district.downcase.delete(' ').include?('county')
        legislation_details[:county_details][:county_name] = district
      end
    end
  end

  def get_officer_website(response_dup, all_officers_json, legislation_details)
    ## Senator, Assembly, Congress URLS:
    senate_url = response_dup.dig('officials', all_officers_json['senator'], 'urls') if all_officers_json['senator'].present?
    assembly_url = response_dup.dig('officials', all_officers_json['assembly'], 'urls') if all_officers_json['assembly'].present?
    congress_url = response_dup.dig('officials', all_officers_json['congress'], 'urls') if all_officers_json['congress'].present?
    council_url = response_dup.dig('officials', all_officers_json['council'], 'urls') if all_officers_json['council'].present?
    attorney_url = response_dup.dig('officials', all_officers_json['districtattorney'], 'urls') if all_officers_json['districtattorney'].present?
    comptroller_url = response_dup.dig('officials', all_officers_json['citycomptroller'], 'urls') if all_officers_json['citycomptroller'].present?
    executive_url = response_dup.dig('officials', all_officers_json['executive'], 'urls') if all_officers_json['executive'].present?

    ## Senator, Assembly, Congress Websites:
    legislation_details[:senate_member_details][:website] = senate_url.first if senate_url&.first.present? && senate_url&.first&.exclude?('wikipedia')
    legislation_details[:assembly_member_details][:website] = assembly_url.first if assembly_url&.first.present? && assembly_url&.first&.exclude?('wikipedia')
    legislation_details[:congress_member_details][:website] = congress_url.first if congress_url&.first.present? && congress_url&.first&.exclude?('wikipedia')
    legislation_details[:council_member_details][:website] = council_url.first if council_url&.first.present? && council_url&.first&.exclude?('wikipedia')
    legislation_details[:attorney_member_details][:website] = attorney_url.first if attorney_url&.first.present? && attorney_url&.first&.exclude?('wikipedia')
    legislation_details[:comptroller_member_details][:website] = comptroller_url.first if comptroller_url&.first.present? && comptroller_url&.first&.exclude?('wikipedia')
    legislation_details[:executive_member_details][:website] = executive_url.first if executive_url&.first.present? && executive_url&.first&.exclude?('wikipedia')
  end

  # rubocop:enable Metrics/AbcSize, Metrics/PerceivedComplexity
end