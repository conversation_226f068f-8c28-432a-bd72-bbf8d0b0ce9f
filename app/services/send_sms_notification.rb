# frozen_string_literal: true

class SendSmsNotification
  require 'plivo'
  include Plivo

  def initialize(phone_number, notification, notification_tracker_id, otp = nil)
    @phone_number = phone_number
    @notification = notification
    @notification_tracker_id = notification_tracker_id
    @otp = otp
  end

  def send_sms
    @account = Account.find_by(subdomain: Apartment::Tenant.current)
    api = RestClient.new(@account.plivo_auth_id, @account.plivo_auth_token)
    response = api.messages.create(
      @account.plivo_phone_number, # from
      [@phone_number], # to
      sms_text, # text
      sms_options
    )
    NotificationTracker.find(@notification_tracker_id).update(message_id: response.message_uuid.first) if @notification_tracker_id.present?
    return true if @otp.present?
  rescue StandardError => e
    Rails.logger.info e.message
    CustomAppsignalError.send_error(e, Apartment::Tenant.current)
    return false if @otp.present?
  end

  def project_subdomain
    if ENV['MANDATORY_SUBDOMAIN'].present?
      Apartment::Tenant.current + '.' + ENV['MANDATORY_SUBDOMAIN']
    else
      Apartment::Tenant.current
    end
  end

  def sms_options
    options = { url: Rails.application.routes.url_helpers.sms_trackers_url(subdomain: project_subdomain) }
    if @notification.present? && @notification.is_mms
      options.merge!(type: 'mms')
      options.merge!(media_urls: sms_attachment_urls)
    end
    options
  end

  def sms_attachment_urls
    attachment_urls = []
    @notification.sms_attachments.each do |attachment|
      attachment_urls << Rails.application.routes.url_helpers.rails_blob_url(attachment, subdomain: project_subdomain)
    end
    attachment_urls
  end

  def sms_text
    return @otp unless @notification.present?

    default_text = @account.saas_json.dig('schema', 'notifications', 'sms_no_reply_text')
    if default_text.present?
      @notification.sms_message + "\n" + default_text
    else
      @notification.sms_message
    end
  end
end
