module FileGenerator
  class CobancDavisVisionFileGenerator
    def old_generate_834_edi(account, benefit, test = nil)
      Apartment::Tenant.switch!(account)
      current_account = Apartment::Tenant.current

      members = Employee.kept.includes(:contacts).order(:a_number)
      optical_benefit = Benefit.includes(employee_benefits: :benefit_coverages).where(name: benefit).first
      data_array = []
      excluded_status = get_associate_status(account, benefit)

      members.each do |member|
        emp_benefit = member.employee_benefits.where('benefit_id = ? and (end_date is null or end_date >= ?)', optical_benefit.id, Date.today).first
        next unless emp_benefit.present?

        employee_employment_status = member.employee_employment_statuses.kept.where('end_date is NULL OR end_date >= ?', Date.today) if member.present?
        employment_status_name = employee_employment_status.first.employment_status.name.downcase unless employee_employment_status.blank?
        next unless (employment_status_name.present? && excluded_status.exclude?(employment_status_name.downcase.delete(' '))) || employee_employment_status.blank?

        data = []
        member_ssn = format_text_length(member.social_security_number.delete('-'), 9) if member.present?

        # ISA (Interchange Control Header Segment)
        # Interchange Control Header Segment
        isa_data = []
        isa_data << '00*' # Authorization Information Qualifier
        isa_data << '  *' # Authorization Information Qualifier
        isa_data << '00*' # Security Information Qualifier
        isa_data << '  *' # Security Information
        isa_data << 'ZZ*' # Interchange ID Qualifier
        isa_data << '*' # Interchange Sender ID
        isa_data << 'ZZ*' # Interchange ID Qualifier
        isa_data << 'V0QJA*' # Interchange Receiver ID
        isa_data << '^*' # Repetition Separator
        isa_data << '*' # Interchange Control Number
        isa_data << '*' # Acknowledgement Requested
        isa_data << (test == true ? 'T*' : 'P*') # Usage Indicator
        isa_data << ':~*' # Component Element Separator

        data << isa_data

        # GS (Functional Group Header)
        # Functional Group Header
        gs_data = []
        gs_data << 'BE*' # Functional Identifier Code
        gs_data << '*' # Application Sender's Code
        gs_data << 'V0QJA*' # Application Receiver's Code
        gs_data << "#{format_date(Date.today)}*" # Date
        gs_data << "#{format_time(Time.now)}*" # Time
        gs_data << '*' # Group Control Number
        gs_data << 'X*' # Responsible Agency Code
        gs_data << '005010X220A1*' # Version/Release/Industry Identifier Code

        data << gs_data

        # ST (Transaction Set Header)
        # Transaction Set Header
        st_data = []
        st_data << '834*' # Transaction Set Identifier Code
        st_data << '*' # Transaction Set Control Number
        st_data << '005010X220A1*' # ImplementationConvention Reference

        data << st_data

        # Beginning Segent
        bs_data = []
        data << '00*' # TransactionSet Purpose Code
        data << "#{member_ssn}*" # Reference Identification
        bs_data << "#{format_date(Date.today)}*" # Date
        bs_data << "#{format_time(Time.now)}*" # Time
        bs_data << '*' # Reference Identification
        bs_data << 'RX*' # Action Code

        data << bs_data

        # Sponsor Name
        sn_data = []
        sn_data << 'P5*' # Entity Identifier Code
        sn_data << 'DAVIS VISION*' # Name
        sn_data << '24*' # IdentificationCode Qualifier
        sn_data << '*' # Identification Code

        data << sn_data

        # Payer
        payer_data = []
        payer_data << 'IN*' # Entity Identifier Code
        payer_data << 'DAVIS VISION*' # Name
        payer_data << 'FI*' # IdentificationCode Qualifier
        payer_data << '11-3051991*' # Identification Code

        data << payer_data

        # Member Level Detail
        data << 'Y*' # Response Code
        data << '18*' # Individual Relationship code
        data << '030*' # Maintenance Type Code
        data << '*' # Maintenance Reason Code
        if employment_status_name.present?
          data << "#{format_benefit_status_code(employment_status_name)}*"
        else
          data << '*'
        end
        data << '*' # Medicare Plan Code
        data << '*' # COBRA Qualifying Event Code
        # Employment Status Code
        if employment_status_name.present?
          data << "#{employment_status_name}*"
        else
          data << '*'
        end
        data << '*' # Student Status Code
        data << '*' # Handicap Indicator

        # Subscriber Indentifier
        data << '0F*' # Reference Identification Qualifier
        # Reference Identification
        if member_ssn.present?
          data << "#{member_ssn}*"
        else
          data << '*'
        end

        # Member Policy Number
        data << 'IL*' # Reference Identification Qualifier
        data << '*' # Reference Identification

        # Member Level Dates
        emp_benefits = member.employee_benefits.where('benefit_id = ? and (end_date is null or end_date >= ?)', optical_benefit.id, Date.today)
        emp_benefit = emp_benefits.first unless emp_benefits.blank?

        data << format_member_level_dates(emp_benefit.start_date, emp_benefit.end_date) if emp_benefit.present?

        # Member Name
        data << '74*' # Entity Identifier Code
        data << '1*' # Entity Type Qualifier
        if member.present?
          data << "#{member.last_name}*" # last_name
          data << "#{member.first_name}*" # first_name
          data << "#{format_text_length(member.middle_name, 1)}*" # mi
        else
          data << '*' # last_name
          data << '*' # first_name
          data << '*' # mi
        end
        data << '*' # Name Prefix
        data << '*' # Name Suffix
        data << '34*' # Identification Code Qualifier
        # Identification Code
        if member_ssn.present?
          data << "#{member_ssn}*"
        else
          data << '*'
        end

        # Member Communications Numbers
        contacts = member.contacts.where(contact_type: 'phone')
        if contacts.present?
          data << "#{format_contact(contacts)}*"
        else
          data << '*'
        end

        # Member Residence Street Address &&
        # Member Residence City, State, Zip Code
        address_data = []
        if member.present?
          address_data << "#{member.street}*" # addr1
          address_data << "#{member.apartment}*" # addr2
          address_data << "#{member.city}*" # city
          address_data << "#{member.state}*" # state
          address_data << "#{member.zipcode}*" # Postal Code
          address_data << 'US*' # Country code
        else
          address_data << '*' # addr1
          address_data << '*' # addr2
          address_data << '*' # city
          address_data << '*' # state
          address_data << '*' # Postal Code
          address_data << 'US*' # Country code
        end
        data << address_data

        # Member Demographics
        member_birthday = member.birthday.present? ? format_date(member.birthday) : ''
        member_gender = member.gender.present? ? format_gender_code(member.gender) : ''
        data << 'D8*' # Date Time Period Format Qualifier
        # Date Time Period
        if member_birthday.present?
          data << "#{member_birthday}*"
        else
          data << '*'
        end
        # Gender Code
        if member_gender.present?
          data << "#{member_gender}*"
        else
          data << '*'
        end

        # Health Coverage
        data << '030*' # Maintenance Type Code
        data << 'VIS*' # Insurance Line Code
        data << '*' # Plan coverage Description
        data << '*' # Coverage Level Code

        # Health Coverage Dates
        data << '348*' # Date/Time Qualifier
        data << 'D8*' # Date Time period Format Qualifier
        data << '*' # Date Time Period
        data << '349*' # Date/Time Qualifier
        data << 'D8*' # Date Time period Format Qualifier
        data << '*' # Date Time Period

        # Health Coverage Policy Number
        data << 'ZZ*' # Reference Identification Qualifier
        data << '*' # Reference Identification

        # Transaction Set Trailer
        data << '*'
        # Functional Group Trailer
        data << '*'
        # Interchange Control Trailer
        data << '*'

        data.flatten!
        data_array << data.join('')
        coverages = member.benefit_coverages.where('employee_benefit_id = ? and (expires_at is null or expires_at >= ?)', emp_benefits.ids, Date.today)

        coverages.each do |coverage|
          coverage_data = []
          coverage_snn = format_text_length(coverage.social_security_number.delete('-'), 9) if coverage.present?
          coverage_data << isa_data # ISA (Interchange Control Header Segment)
          coverage_data << gs_data # GS (Functional Group Header)
          coverage_data << st_data # ST (Transaction Set Header)

          # Beginning Segent
          coverage_data << '00*' # TransactionSet Purpose Code
          # Reference Identification
          if coverage_snn.present?
            coverage_data << "#{coverage_snn}*"
          else
            coverage_data << '*'
          end
          coverage_data << bs_data

          coverage_data << sn_data # Sponsor Name
          coverage_data << payer_data # Payer

          # Member Level Detail
          coverage_data << 'N*' # Response Code
          # Individual Relationship code
          if coverage.present? && coverage.relationship.present?
            coverage_data << "#{format_relationship(coverage.relationship)}*"
          else
            coverage_data << '*'
          end
          coverage_data << '030*' # Maintenance Type Code
          coverage_data << '*' # Maintenance Reason Code
          coverage_data << '*' # Benefit Status Code
          coverage_data << '*' # Medicare Plan Code
          coverage_data << '*' # COBRA Qualifying Event Code
          coverage_data << '*' # Employment Status Code
          coverage_data << '*' # Student Status Code
          coverage_data << '*' # Handicap Indicator

          # Subscriber Indentifier
          coverage_data << '0F*' # Reference Identification Qualifier
          # Reference Identification
          if coverage_snn.present?
            coverage_data << "#{coverage_snn}*"
          else
            coverage_data << '*'
          end

          # Member Policy Number
          coverage_data << 'IL*' # Reference Identification Qualifier
          coverage_data << '*' # Reference Identification

          # Member Level Dates
          coverage_data << '*' # Date/Time Qualifier
          coverage_data << '*' # Date Time period Format Qualifier
          coverage_data << '*' # Date Time Period

          # Member Name
          coverage_data << '74*' # Entity Identifier Code
          coverage_data << '1*' # Entity Type Qualifier
          name = coverage.name.delete(',') if coverage.present? && coverage.name.present?
          # last_name
          if name.present?
            coverage_data << "#{name.split(' ')[1..-1].join(' ')}*"
          else
            coverage_data << '*'
          end
          # first_name
          if name.present?
            coverage_data << "#{name.split(' ').first}*"
          else
            coverage_data << '*'
          end
          coverage_data << '*' # mi
          coverage_data << '*' # Name Prefix
          coverage_data << '*' # Name Suffix
          coverage_data << '34*' # Identification Code Qualifier
          # Identification Code
          if coverage_snn.present?
            coverage_data << "#{coverage_snn}*"
          else
            coverage_data << '*'
          end

          # Member Communications Numbers
          coverage_phone = coverage.phone.present? ? coverage.phone.delete(' ()-') : ''
          if coverage_phone.present?
            coverage_data << "#{coverage_phone}*"
          else
            coverage_data << '*'
          end

          # Member Residence Street Address &&
          # Member Residence City, State, Zip Code
          coverage_data << address_data

          # Member Demographics
          coverage_birthday = coverage.birthday.present? ? format_date(coverage.birthday) : ''
          coverage_data << 'D8*' # Date Time Period Format Qualifier
          # Date Time Period
          if coverage_birthday.present?
            coverage_data << "#{coverage_birthday}*"
          else
            coverage_data << '*'
          end
          coverage_data << '*' # Gender Code

          # Health Coverage
          coverage_data << '030*' # Maintenance Type Code
          coverage_data << 'VIS*' # Insurance Line Code
          coverage_data << '*' # Plan coverage Description
          coverage_data << '*' # Coverage Level Code

          # Health Coverage Dates
          coverage_data << format_health_coverage_dates(coverage.effective_date, coverage.expires_at)

          # Health Coverage Policy Number
          coverage_data << 'ZZ*' # Reference Identification Qualifier
          coverage_data << '*' # Reference Identification

          # Transaction Set Trailer
          coverage_data << '*'
          # Functional Group Trailer
          coverage_data << '*'
          # Interchange Control Trailer
          coverage_data << '*'

          coverage_data.flatten!
          data_array << coverage_data.join('')
        end
      end

      filename = "V260697_601599_HP_#{Time.now.strftime('%Y%m%d_%H%M%S')}_001#{test == true ? '_test' : ''}.txt"
      filepath ||= "#{Rails.root}/#{filename}"

      File.open(filepath, 'wb') do |file|
        data_array.each do |data|
          file.puts data
        end
      end

      filename # Returning Filename to upload it to FTP server
    end

    def generate(account, benefit, test)
      Apartment::Tenant.switch!(account)

      members = Employee.kept.includes(:contacts).order(:a_number)
      optical_benefit = Benefit.includes(employee_benefits: :benefit_coverages).where(name: benefit).first
      data_array = []
      excluded_status = get_associate_status(account, benefit)

      members.each do |member|
        member_data = []
        family_information_data = []
        member_information_data = []
        index_value = 2

        emp_benefits = member.employee_benefits.where('benefit_id = ? and (end_date is null or end_date >= ?)', optical_benefit.id, Date.today)
        emp_benefit = emp_benefits.first
        next unless emp_benefit.present?

        employee_employment_status = member.employee_employment_statuses.kept.where('end_date is NULL OR end_date >= ?', Date.today) if member.present?
        employment_status_name = employee_employment_status.first.employment_status.name.downcase unless employee_employment_status.blank?
        next unless (employment_status_name.present? && excluded_status.exclude?(employment_status_name.downcase.delete(' '))) || employee_employment_status.blank?

        @member_ssn = member.social_security_number.delete('-')
        get_family_information(member, family_information_data, employment_status_name)
        get_member_dependent_information(member, member_data, true)
        get_member_information(member, member_information_data, emp_benefits)

        data_array << [family_information_data, member_data, member_information_data].flatten.join

        coverages = member.benefit_coverages.where('employee_benefit_id = ? and (expires_at is null or expires_at >= ?)', emp_benefits.ids, Date.today)
        coverages.each do |coverage|
          coverage_data = []
          index_value += 1 unless %w[spouse].include?(coverage&.relationship&.downcase)
          get_member_dependent_information(coverage, coverage_data, index_value)

          data_array << [family_information_data, coverage_data, member_information_data].flatten.join
        end
      end

      generate_cobanc_davis_vision_file(data_array, test) # Returning Filename to upload it to FTP server
    end

    private

    def format_text_length(text, length)
      text&.ljust(length, ' ')&.slice(0, length)
    end

    def format_date(date)
      date.strftime('%Y%m%d') rescue ''
    end

    def format_time(time)
      time.strftime('%H%M')
    end

    def format_gender_code(gender)
      if gender.name.downcase == 'female'
        'F'
      elsif gender.name.downcase == 'male'
        'M'
      else
        'U' # unknown
      end
    end

    def format_relationship(relationship, index)
      relationship_no = if relationship.blank? || %w[spouse].exclude?(relationship.downcase)
                          "0#{index}"
                        elsif relationship.downcase == 'spouse'
                          '02'
                        end
      relationship_code = if relationship.blank?
                            ''
                          elsif %w[spouse child].include?(relationship.downcase)
                            relationship.first.upcase
                          elsif %w[disabled_child other].include?(relationship.downcase)
                            'O'
                          elsif %w[domestic_partner].include?(relationship.downcase.split.join('_'))
                            'P'
                          end
      [relationship_no, relationship_code]
    end

    def format_benefit_status_code(name)
      if name.present? && name.downcase == 'active'
        'A'
      elsif name.present? && name.downcase.split(' ').include?('cobra')
        'C'
      else
        'S'
      end
    end

    def format_member_level_dates(start_date, end_date)
      values = []
      if start_date != nil
        values << '356*' # Date/Time Qualifier
        values << 'D8*' # Date Time period Format Qualifier
        values << "#{format_date(start_date)}*" # Date Time Period
      elsif end_date != nil
        values << '357*' # Date/Time Qualifier
        values << 'D8*' # Date Time period Format Qualifier
        values << "#{format_date(end_date)}*" # Date Time Period
      end

      return values
    end

    def format_contact(contacts)
      value = ''
      contacts.each do |contact|
        if contact.value.present?
          value = contact.value.delete(' ()-')
          break
        end
      end

      value
    end

    def format_health_coverage_dates(start_date, end_date)
      values = []
      if start_date != nil
        values << '348*' # Date/Time Qualifier
        values << 'D8*' # Date Time period Format Qualifier
        # Date Time Period
        if start_date.present?
          values << "#{format_date(start_date)}*"
        else
          values << '*'
        end
      elsif end_date != nil
        values << '349*' # Date/Time Qualifier
        values << 'D8*' # Date Time period Format Qualifier
        # Date Time Period
        if end_date.present?
          values << "#{format_date(end_date)}*"
        else
          values << '*'
        end
      end

      return values
    end

    def get_associate_status(account, benefit)
      %w[deceased pending terminated resigned transferred] if benefit == 'Optical' && account == 'cobanc'
    end

    def get_family_information(member, data, employment_status_name)
      member_id = member.previous_shield_number.presence || (member.a_number&.match(/^COA\d+/) ? member.a_number : '')
      data << format_text_length(member_id, 12) if member.present? # member_id
      data << format_text_length('HP', 3) # group_code
      data << format_text_length(get_subgroup_and_plan_code(employment_status_name), 3) # subgroup
      data << format_text_length('O', 1) # insurance_type
      data << format_text_length(get_subgroup_and_plan_code(employment_status_name, true), 3) # plan_code
      data << format_text_length(['cobra', 'cobra inactive'].include?(employment_status_name&.downcase) ? 'COBRA' : '', 10) # user_field1
      data << format_text_length('', 10) # user_field2
      data << format_text_length('', 12) # member_alternate_id
      data << format_text_length('', 9) # blank_filler1
      data << format_text_length('', 11) # blank_filler2
    end

    def get_member_dependent_information(object, data, member_or_dependent)
      dependent_no, relationship_code = member_or_dependent == true ? %w[01 M] : format_relationship(object.relationship, member_or_dependent)
      middle_name = member_or_dependent == true ? object.middle_name : ''
      birthday = member_or_dependent == true ? format_date(object.birthday) : ''
      ssn = object.social_security_number.delete('-') || @member_ssn
      gender = object.gender.present? ? format_gender_code(object.gender) : ''
      data << format_text_length(dependent_no, 2) # dependent_no_for_member
      data << format_text_length(relationship_code, 1) # relationship
      data << format_text_length(ssn, 9) # member_ssn
      data << format_text_length('S', 1) # name_format
      # data << format_text_length('', 28) # name
      data << format_text_length(object.last_name, 15) # last_name
      data << format_text_length(object.first_name, 12) # first_name
      data << format_text_length(middle_name, 1) # middle_name
      data << format_text_length(birthday, 8) # dob
      data << format_text_length(gender, 1) # gender
      data << format_text_length('', 1) # dependent_status_indicator
      data << format_text_length('', 8) # dependent_status_date
      data << format_text_length('', 9) # blank_filler3
    end

    def get_member_information(member, data, emp_benefits)
      phone_number = member.contacts.where(contact_for: 'personal', contact_type: 'phone')&.first&.value&.remove!('(', ')', ' ', '-') || ''
      eligible_date = emp_benefits.first.start_date || ''
      eligible_date = format_date(eligible_date) if eligible_date.present?
      data << format_text_length(eligible_date, 8) # eligible_date
      data << format_text_length('', 8) # termination_date
      data << format_text_length(member.street, 30) # address_1
      data << format_text_length(member.apartment, 30) # address_2
      data << format_text_length(member.city, 15) # city
      data << format_text_length(member.state, 2) # state
      data << format_text_length(member.zipcode, 5) # zipcode
      data << format_text_length('', 4) # zipcode_4
      data << format_text_length(phone_number, 10) # phone_number
      data << format_text_length('', 9) # vested_balance
      data << format_text_length('', 8) # balance_date
      data << format_text_length('', 3) # blank_filler4
      data << format_text_length('', 1) # diabetic_indicator
      data << format_text_length('', 5) # blank_filler5
    end

    def generate_cobanc_davis_vision_file(data_array, test)
      filename = "V260697_601599_HP_#{Time.now.strftime('%Y%m%d_%H%M%S')}_001#{test == true ? '_test' : ''}.txt"
      filepath ||= "#{Rails.root}/#{filename}"

      File.open(filepath, 'wb') do |file|
        data_array.each do |data|
          file.puts data
        end
      end
      filename
    end

    def get_subgroup_and_plan_code(employment_status_name, plan_code = nil)
      case employment_status_name&.downcase
      when 'active', 'cobra', 'cobra inactive'
        plan_code ? '5FU' : '014'
      when 'retired', 'out of state retiree'
        plan_code ? '6FU' : '015'
      when 'part-time active'
        plan_code ? '7FU' : '016'
      when 'range instructor'
        plan_code ? '8FU' : '017'
      else
        ''
      end
    end
  end
end
