# frozen_string_literal: true

module Generators
  module Contacts
    class ContactBaseGenerator
      def self.generate(value, employee_id)
        new(value, employee_id).generate
      end

      def initialize(value, employee_id)
        @value = value
        @employee_id = employee_id
      end

      def errors
        @errors ||= []
      end

      def generate
        return if value.blank?

        generator = Generators::ContactGenerator.new(params)
        generator.generate
        @errors = generator.errors
      end

      private

      attr_reader(
        :employee_id,
        :value
      )

      def params
        raise NotImplementedError
      end
    end
  end
end
