# frozen_string_literal: true

module Generators
  class EmployeePositionGenerator < ApplicationGenerator
    def generate
      return unless employee_exists?

      data.start_date = format_import_date(params['start_date'])
      data.end_date = format_import_date(params['end_date'])
      data.position = position if position.present?
      data.employee = employee
      save_data
    end

    private

    def data
      @data ||= EmployeePosition.new
    end

    def position
      @position ||= Position.where(name: params['name']).first
    end
  end
end
