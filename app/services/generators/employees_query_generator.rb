# frozen_string_literal: true

module Generators
  class EmployeesQueryGenerator
    def self.generate(params, current_account = nil, notification = nil)
      new(params, current_account, notification).generate
    end

    def initialize(params, current_account, notification)
      @params = params
      @current_account = current_account
      @notification = notification
    end

    def generate # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      employees = EmployeesQuery.new(Employee.unscoped.kept, params).call
      employees = EmployeesPlacardNumberQuery.new(employees, params[:placard_number], current_account).call
      employees = EmployeesSsnQuery.new(employees, params[:social_security_number], current_account).call
      employees = EmployeesWithContactsQuery.new(employees, params).call
      employees = EmployeesWithOfficesQuery.new(employees, params[:office_ids]).call
      employees = EmployeesWithDepartmentsQuery.new(employees, params[:department_ids], current_account).call
      employees = EmployeesWithSectionsQuery.new(employees, params[:section_ids]).call
      employees = EmployeesWithRanksQuery.new(employees, params[:rank_ids]).call
      employees = EmployeesWithTitlesQuery.new(employees, params[:title_ids]).call
      end_dates_present = %i[employment_statuses_end_date_from employment_statuses_end_date_to employment_statuses_start_date_from employment_statuses_start_date_to].any? { |param| params[param].present? }
      employees = EmployeesWithEmploymentStatusesQuery.new(employees, params[:employment_status_ids], end_dates_present).call
      employees = EmployeesWithEmploymentStatusDateRangeQuery.new(employees, params).call
      employees = EmployeesWithOfficerStatusDateRangeQuery.new(employees, params).call
      employees = EmployeesWithFirearmStatusesQuery.new(employees, params[:firearm_status_ids]).call
      employees = EmployeesWithOfficerStatusesQuery.new(employees, params[:officer_status_ids]).call
      employees = EmployeesWithPositionsQuery.new(employees, params[:position_ids]).call
      employees = EmployeesWithBirthdayQuery.new(employees, params[:birthday_from], params[:birthday_to]).call
      employees = EmployeesWithAgeQuery.new(employees, params[:age_from], params[:age_to]).call
      employees = EmployeesWithNccDateQuery.new(employees, params[:ncc_date_from], params[:ncc_date_to]).call
      employees = EmployeesWithLongevityDateQuery.new(employees, params[:longevity_date_from], params[:longevity_date_to]).call
      employees = EmployeesWithLeaveProgressionDateQuery.new(employees, params[:leave_progression_date_from], params[:leave_progression_date_to]).call
      employees = EmployeesWithMemberSinceQuery.new(employees, params[:member_since_from_date], params[:member_since_to_date]).call
      employees = EmployeesWithAffiliationsQuery.new(employees, params).call
      employees = EmployeesWithTourOfDutiesQuery.new(employees, params).call
      employees = EmployeesWithTShirtSizeQuery.new(employees, params).call
      employees = EmployeesWithLegislationQuery.new(employees, params).call
      employees = EmployeesWithFacilityQuery.new(employees, params[:facility_ids]).call

      if params[:start_date_from].present? || params[:start_date_to].present? || notification.present?
        employees = EmployeesWithStartDateQuery.new(employees, params[:start_date_from], params[:start_date_to]).call
      end

      if params[:report_type] == Report::ReportTypes::PACFS
        employees = EmployeesWithPacfsQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::SINGLE_EMPLOYEE
        if params[:report_format] == 'avery' || params[:report_format] == 'dymo'
          employees = EmployeesWithDoNotMailQuery.new(employees, false).call
        end
      end

      if params[:report_type] == Report::ReportTypes::BENEFIT_COVERAGES_EXPIRATION
        employees = EmployeesWithBenefitCoveragesQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::BENEFICIARY
        employees = EmployeesWithBeneficiaryQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::BENEFITS
        employees = EmployeesWithBenefitsQuery.new(employees, params).call
        employees = EmployeesWithBenefitDisbursementsQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::BENEFIT_COVERAGES
        employees = EmployeesWithBenefitTypesQuery.new(employees, params).call
        # employees = EmployeesWithBenefitCoveragesQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::SICK_BANK
        employees = EmployeesWithLeavesQuery.new(employees, params.merge(leave_type: Leave::LeaveTypes::SICK)).call
      end

      employees = EmployeesWithLodisQuery.new(employees, params).call if params[:report_type] == Report::ReportTypes::LODI || params[:report_type] == Report::ReportTypes::WORKERS_COMP

      if params[:report_type] == Report::ReportTypes::UNION_MEETINGS
        employees = EmployeesWithUnionMeetingsQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::EMPLOYEE_DELEGATE_ASSIGNMENT
        employees = EmployeesWithDelegateAssignmentsQuery.new(employees, params).call unless params[:group_by_delegate] == "true"
        employees = EmployeesWithDelegateAssignmentsReverseLogicQuery.new(DelegateAssignment.kept, params).call if params[:group_by_delegate] == "true"
      end

      if params[:report_type] == Report::ReportTypes::DISCIPLINES
        employees = EmployeesWithDisciplineSettingsQuery.new(employees, params).call
        employees = EmployeesWithDisciplineChargeQuery.new(employees, params).call
        employees = EmployeesWithDisciplineStatusQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettingsDateRangeQuery.new(employees, params).call
        employees = EmployeesWithDisciplineOlrDateQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettledPendingQuery.new(employees, params).call
        employees = EmployeesWithDisciplineWinLossQuery.new(employees, params).call
        employees = EmployeesWithDisciplineStepDateRangeQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettingsCaseAbeyanceQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettingsAbandonmentHearingQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettingsEmployeePdsQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettingsTaImplementedQuery.new(employees, params).call
        employees = EmployeesWithDisciplineSettingsChargeAndDanNumberQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::LIFE_INSURANCE
        employees = EmployeesWithLifeInsuranceQuery.new(employees, params).call
      end

      if params[:report_type] == Report::ReportTypes::GRIEVANCES
        employees = EmployeesWithGrievancesQuery.new(employees, params).call
        employees = EmployeesWithGrievanceOlrDateQuery.new(employees, params).call
        employees = EmployeesWithGrievanceSettledPendingQuery.new(employees, params).call
        employees = EmployeesWithGrievanceWinLossQuery.new(employees, params).call
        employees = EmployeesWithGrievancesDateRangeQuery.new(employees, params).call
      end

      employees = EmployeesWithJanusQuery.new(employees, params).call if params[:report_type] == Report::ReportTypes::JANUS

      if params[:report_type] == Report::ReportTypes::CLASS_ACTION_GRIEVANCE
        employees = ClassActionGrievanceQuery.new(employees, params).call
      end

      # employees.reorder('employees.first_name ASC, employees.middle_name ASC, employees.last_name ASC')

      employees
    end

    private

    attr_reader :params, :current_account, :notification
  end
end
