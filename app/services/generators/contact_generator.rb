# frozen_string_literal: true

module Generators
  class ContactGenerator < ApplicationGenerator
    def generate
      return unless employee_exists?
      data.contact_for = params[:contact_for]
      data.contact_type = params[:contact_type]
      data.value = params[:value]
      data.employee = employee
      save_data
    end

    private

    def data
      @data ||= Contact.new
    end
  end
end
