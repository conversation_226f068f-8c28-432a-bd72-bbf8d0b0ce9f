# frozen_string_literal: true

class FileBuilder
  def self.process_file(object)
    new(object).process_file
  end

  def self.process_multi_files(object, column_name = 'files', upload_date_needed = nil)
    new(object).process_multi_files(column_name, upload_date_needed)
  end

  def initialize(object)
    @object = object
  end

  def process_file
    if file.present?
      return {
        name: file.filename,
        url: Rails.application.routes.url_helpers.rails_blob_url(file, subdomain: project_subdomain)
      }
    end
    {
      name: '',
      url: ''
    }
  end

  def project_subdomain
    if ENV['MANDATORY_SUBDOMAIN'].present?
      Apartment::Tenant.current + '.' + ENV['MANDATORY_SUBDOMAIN']
    else
      Apartment::Tenant.current
    end
  end

  def process_multi_files(column_name, upload_date_needed)
    array_of_hashes = []
    subdomain = project_subdomain
    files = send('multi_files', column_name)
    return array_of_hashes if files.nil?

    files.each do |file|
      file_hash = { name: file.filename,
                    url: Rails.application.routes.url_helpers.rails_blob_url(file, subdomain: subdomain),
                    id: file.signed_id }
      file_hash[:uploaded_at] = file.created_at if upload_date_needed
      array_of_hashes << file_hash
    end
    array_of_hashes
  end

  private

  attr_reader :object

  def file
    @file ||= object.file.attached? ? object.file : nil
  end

  def multi_files(column_name)
    object.send(column_name).attached? ? object.send(column_name + '_attachments').includes(:blob) : nil
  end
end
