class FileUploadMailer < ApplicationMailer
  def send_file_upload_fail_mail(vendor, error, time, domain, file = nil)
    @vendor = vendor.upcase
    @date_time = time.strftime("%Y/%m/%d %H:%M:%S")
    @error = error
    @account = domain
    @email = '<EMAIL>'

    # Domain based from email configuration
    domain = 'myfusesystems.com'
    from_email = 'no-reply@' + domain

    attachments[file] = File.read("#{Rails.root}/#{file}") if file.present?

    mail from: from_email, to: @email, subject: "File Upload failed in the #{vendor}"
  end
end
