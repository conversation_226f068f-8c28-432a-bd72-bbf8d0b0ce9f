# frozen_string_literal: true

class NotificationsMailer < ApplicationMailer
  def notifications(notification_id, employee_id, email)
    @notification = Notification.find(notification_id)
    headers['employee_id'] = employee_id
    headers['notification_id'] = @notification.id
    headers['subdomain'] = Apartment::Tenant.current

    # Domain based from email configuration
    account = Account.find_by_subdomain(Apartment::Tenant.current)
    domain = account.domain_name || 'myfusesystems.com'
    from_email = if account.subdomain == 'nassaucoba'
                   '<EMAIL>'
                 else
                   'no-reply@' + domain
                 end
    email = email

    notification_files = @notification.files

    notification_files.each do |file|
      attachments[file.blob.filename.to_s] = { mime_type: file.blob.content_type, content: file.blob.download }
    end

    @add_signature_for_all_mails = account.saas_json.dig('schema', 'notifications', 'default_email_signature')

    mail from: from_email, to: email, subject: @notification.subject
  end
end
