<table class="header">
  <tbody>
  <tr>
    <td>
      <div class="block-logo">
        <% Apartment::Tenant.switch('public') do %>
          <% account_logo = @current_account.logo %>

          <% account_logo_url = if Rails.env.production?
                                  account_logo.service_url
                                else
                                  Rails.application.routes.url_helpers.rails_blob_url(account_logo)
                                end %>
          <%= wicked_pdf_image_tag account_logo_url %>
        <% end %>
      </div>
    </td>
    <td style="vertical-align:middle !important">
      <div class="block-title">
        <%= title %>
      </div>
    </td>
  </tr>
  </tbody>
</table>
<hr/>
