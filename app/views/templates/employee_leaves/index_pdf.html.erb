<% index = 0 %>
<% total = 0 %>
<table class="table-regular">
  <thead>
  <tr>
    <th>&nbsp;</th>
    <% translate_report_columns(Report::ReportTypes::SICK_BANK).each do |column| %>
      <th><%= column %></th>
    <% end %>
  </tr>
  </thead>
  <tbody>
  <% employees.order_by_name.each do |employee| %>
    <% employee.leaves.each_with_index do |leave, ind| %>
      <% total += leave.hours_used %>
      <tr>
        <% if ind.zero? %>
          <% index += 1 %>
          <td><%= index %></td>
          <td>
            <h1><%= employee.name %></h1>
          </td>
        <% else %>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        <% end %>
        <%= render partial: '/templates/employee_leaves/leave_pdf', locals: {leave: leave} %>
      </tr>
    <% end %>
  <% end %>
  <tr class="block-total-summary">
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td class="text-right">
      <h3><%= "#{translation_for('reports.total_summary')}:" %></h3>
    </td>
    <td class="text-right">
      <h3><%= total %></h3>
    </td>
  </tr>
  </tbody>
</table>
