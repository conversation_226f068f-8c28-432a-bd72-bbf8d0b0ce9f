<% index = 0
   total_dependents_count = 0
   coverages = {}
   group_by_name = @current_account.saas_json.dig('schema', 'benefit_coverages', 'name').present?
%>
<% employee_columns = columns.present? ? columns : [] %>
<table class="table-regular">
  <thead>
  <tr>
    <th>&nbsp;</th>
    <% translated_columns = translate_single_employee_columns(employee_columns, nil, Report::ReportTypes::BENEFIT_COVERAGES, show_coverages) %>
    <% translated_columns.each do |translated_column| %>
      <th>
        <%= translated_column %>
      </th>
    <% end %>
  </tr>
  </thead>
  <tbody>
  <% Employee.includes(associations.uniq.flatten).where(id: employee_ids).find_each do |employee|
    data = []
    data << [employee.name, employee.shield_number || '-']
    empty_data = Array.new(employee_columns.count + 2)
    data << Generators::EmployeeDataGenerator.generate(employee, employee_columns, @current_account) if employee_columns.count > 0
    employee_coverages = employee.benefit_coverages

    if show_coverages == 'grouped' && employee_coverages.present?
      grouped_coverages = if group_by_name
                            employee_coverages.group_by { |record| [record.name, record.relationship, record.birthday, record.social_security_number] }
                          else
                            employee_coverages.group_by { |record| [record.first_name, record.last_name, record.relationship, record.birthday, record.social_security_number] }
                          end
      grouped_coverages.each_with_index do |(key, _grouped_coverage), indx|
        if indx != 0
          data = []
          data << empty_data
        end
        name = group_by_name ? "#{key[0]} #{key[1]}" : key[0]
        data << [name, key[-3], DateFormatterHelper.format_report_date(key[-2]), key[-1]]
        data.flatten! %>
        <tr>
          <% index += 1 %>
          <td><%= index %></td>
          <% data.each_with_index do |da, inx| %>
            <td>
              <% if indx == 0 && inx == 0 %>
                <h1><%= da %></h1>
              <% elsif da.blank? %>
                &nbsp;
              <% else %>
                <%= da %>
              <% end %>
            </td>
          <% end %>
        </tr>
      <% end %>
    <% elsif show_coverages != 'grouped' && (employee_benefits = employee.employee_benefits).present?
         employee_benefits.each_with_index do |employee_benefit, indx|
           if indx != 0
             data = []
             data << empty_data
           end
           employee_benefit_data = []
           employee_benefit_coverages = employee_benefit.benefit_coverages
           benefit_coverages = if @current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship') == true
                                 employee_coverages.order('relationship DESC, birthday ASC')
                               else
                                 employee_benefit_coverages
                               end

           employee_benefit_data << employee_benefit.benefit&.name || ''
           employee_benefit_data << DateFormatterHelper.format_report_date(employee_benefit.start_date)
           employee_benefit_data << DateFormatterHelper.format_report_date(employee_benefit.end_date)
           total_dependents_count += benefit_coverages.length
           benefit_coverages.group_by(&:relationship.downcase).each do |coverage, value|
             next if coverage.blank?

             coverages[coverage] ||= 0
             coverages[coverage] += value.length
           end
           data << [
             employee_benefit.name,
             DateFormatterHelper.format_report_date(employee_benefit.start_date),
             DateFormatterHelper.format_report_date(employee_benefit.end_date)
           ]

           if (show_coverages == 'all' || show_coverages == 'true') && benefit_coverages.present?
             benefit_coverages.each_with_index do |benefit_coverage, idx|
               if idx != 0
                 data = []
                 data << empty_data + [nil, nil, nil]
               end
               data << [
                 group_by_name ? benefit_coverage.name : "#{benefit_coverage.first_name} #{benefit_coverage.last_name}",
                 benefit_coverage.relationship&.humanize&.titleize,
                 DateFormatterHelper.format_report_date(benefit_coverage.birthday),
                 benefit_coverage.social_security_number
               ] %>
            <% data.flatten! %>
            <tr>
              <% index += 1 %>
              <td><%= index %></td>
              <% data.each_with_index do |da, inx| %>
                <td>
                  <% if indx == 0 && inx == 0 %>
                    <h1><%= da %></h1>
                  <% elsif da.blank? %>
                    &nbsp;
                  <% else %>
                    <%= da %>
                  <% end %>
                </td>
              <% end %>
            </tr>
          <% end %>
        <% else %>
          <% data.flatten! %>
          <tr>
            <% index += 1 %>
            <td><%= index %></td>
            <% data.each_with_index do |da, inx| %>
              <td>
                <% if indx == 0 && inx == 0 %>
                  <h1><%= da %></h1>
                <% elsif da.blank? %>
                  &nbsp;
                <% else %>
                  <%= da %>
                <% end %>
              </td>
            <% end %>
          </tr>
        <% end %>
      <% end %>
    <% else %>
      <% data << case show_coverages
                 when 'grouped'
                   Array.new(4)
                 when 'all', 'true'
                   Array.new(7)
                 when 'false'
                   Array.new(3)
                 end %>
      <% data.flatten! %>
      <tr>
        <% index += 1 %>
        <td><%= index %></td>
        <% data.each_with_index do |da, inx| %>
          <td>
            <% if inx == 0 %>
              <h1><%= da %></h1>
            <% elsif da.blank? %>
              &nbsp;
            <% else %>
              <%= da %>
            <% end %>
          </td>
        <% end %>
      </tr>
    <% end %>
  <% end; if show_coverages == 'true' || show_coverages == 'all' %>
    <% coverages.each do |coverage, value| %>
      <tr>
        <td colspan="<%= translated_columns.count + 1 %>" style="text-align: right;">
          <% if coverage.blank? %>
            <span class="header-span">Total dependents without a Relationship defined:</span>
          <% else %>
            <span class="header-span">Total <%= coverage.titleize %>:</span>
          <% end %>
          <span><%= value %></span>
        </td>
      </tr>
    <% end %>
    <br>
    <tr>
      <td colspan="<%= translated_columns.count + 1 %>" style="text-align: right;">
        <span class="header-span">Total Dependents :</span>
        <span><%= total_dependents_count %></span>
      </td>
    </tr>
  <% end; if show_coverages == 'true' || show_coverages == 'all'
  %>
    <% coverages.each do |coverage, value| %>
      <tr>
        <td class="block-total-summary">
          <ul>
            <li>
              <div class="w-20 float-left">&nbsp;
              </div>
              <div class="w-20 float-left">&nbsp;
              </div>
              <div class="w-20 float-left">&nbsp;
              </div>
              <div class="w-30  float-left text-right">
                <% if coverage.blank? %>
                  <h3 class="line-height-2"><%= "Total dependents without a Relationship defined :" %></h3>
                <% else %>
                  <h3 class="line-height-2"><%= "Total #{coverage.titleize} :" %></h3>
                <% end %>
              </div>
              <div class="w-5 float-left text-right">
                <h3 class="line-height-3"><%= value %></h3>
              </div>
            </li>
          </ul>
        </td>
      </tr>
    <% end %>
    <br>
    <tr>
      <td class="block-total-summary">
        <ul>
          <li>
            <div class="w-20 float-left">&nbsp;
            </div>
            <div class="w-20 float-left">&nbsp;
            </div>
            <div class="w-20 float-left">&nbsp;
            </div>
            <div class="w-30  float-left text-right">
              <h3 class="line-height-2"><%= "Total Dependents :" %></h3>
            </div>
            <div class="w-5 float-left text-right">
              <h3 class="line-height-3"><%=total_dependents_count%></h3>
            </div>
          </li>
        </ul>
      </td>
    </tr>
  <% end %>
  </tbody>
</table>
