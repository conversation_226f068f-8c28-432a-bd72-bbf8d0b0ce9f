<% index = 0 %>
<table class="table-regular">
  <thead>
  <tr>
    <th>&nbsp;</th>
    <% translate_report_columns(Report::ReportTypes::UNION_MEETINGS).each do |column| %>
      <th><%= column %></th>
    <% end %>
  </tr>
  </thead>
  <tbody>
  <% employees.order_by_name.each do |employee| %>
    <% employee.employee_meeting_types.each_with_index do |employee_meeting_type, ind| %>
      <tr>
        <% if ind.zero? %>
          <% index += 1 %>
          <td>
            <%= index %>
          </td>
          <td>
            <h1><%= employee.name %>
            <% if employee.gender.present? %>
              </h1><%= t("genders.#{employee.gender.name}") %>
            <% end %>
          </td>
        <% else %>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        <% end %>
        <%= (render partial: '/templates/employee_union_meetings/employee_union_meeting_pdf',
                                                locals: {employee_meeting_type: employee_meeting_type}) %>
      </tr>
    <% end %>
  <% end %>
  </tbody>
</table>
