# frozen_string_literal: true

class EmployeePacf < ApplicationRecord
  include AutoChangeMemberStatuses

  delegate :name, to: :pacf, allow_nil: true

  # ================== Associations ====================================================================================
  belongs_to :employee
  belongs_to :pacf, optional: true
  belongs_to :payment_type, optional: true

  # ================== Validations ====================================================================================
  validates :amount, numericality: {greater_than: -100000000 ,less_than: 100000000}, allow_nil: true

  # ================== Callbacks ======================================================================================
  after_save :member_status
end
