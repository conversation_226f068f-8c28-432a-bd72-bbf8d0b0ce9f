# frozen_string_literal: true

class EmployeeDisciplineSetting < ApplicationRecord
  delegate :name, to: :discipline_setting, allow_nil: true
  delegate :name, to: :discipline_charge, allow_nil: true
  include PgSearch::Model
  pg_search_scope :search_for_status, associated_against: {
    discipline_charge: [:id]
  }

  # ================== Associations ====================================================================================
  belongs_to :employee
  belongs_to :discipline_setting
  belongs_to :discipline_charge, optional: true
  belongs_to :discipline_status, optional: true
  has_many :employee_discipline_steps, -> { where(employee_discipline_steps: { discarded_at: nil }) }
  has_many :discipline_statuses, through: :employee_discipline_steps
  has_many_attached :files

  # ================== Validations =====================================================================================
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }

  # ============================= Callbacks ============================================================================
  before_save :reset_abandonment_hearing, unless: -> { ta_implemented }
  after_discard do
    employee_discipline_steps.update_all(discarded_at: Time.current)
  end

  # ================== Instance Methods ================================================================================
  def discipline_setting_name
    discipline_setting.name
  end

  def discipline_charge_name
    discipline_charge.name
  end

  def reset_abandonment_hearing
    self.abandonment_hearing = false
  end
end
