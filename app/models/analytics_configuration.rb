class AnalyticsConfiguration < ApplicationRecord
  include PgSearch::Model
  extend FriendlyId
  friendly_id :analytics_type, use: :slugged

  module AnalyticsTypes
    AVA = 'ava'.freeze
    PERSONAL = 'personal'.freeze
    SICK = 'sick'.freeze
    VACATION = 'vacation'.freeze
  end

  ANALYTICS_TYPES = [
    AnalyticsTypes::AVA,
    AnalyticsTypes::PERSONAL,
    AnalyticsTypes::SICK,
    AnalyticsTypes::VACATION
  ].freeze

  scope :ava_leaves, -> { kept.where(analytics_type: AnalyticsTypes::AVA) }
  scope :personal_leaves, -> { kept.where(analytics_type: AnalyticsTypes::PERSONAL) }
  scope :sick_leaves, -> { kept.where(analytics_type: AnalyticsTypes::SICK) }
  scope :vacation_leaves, -> { kept.where(analytics_type: AnalyticsTypes::VACATION) }
  pg_search_scope :search_by_analytics,
                  against: %i[analytics_type],
                  associated_against: {employee: %i[first_name last_name middle_name]},
                  using: {tsearch: {prefix: true}}

  # ============================= Validations ==========================================================================
  validates :days_earned, presence: true, numericality: {greater_than_or_equal_to: 0}
  validates :analytics_type, presence: true, inclusion: {in: ANALYTICS_TYPES}
  validates_uniqueness_of :analytics_type, scope: %i[employee_id duration_from],
                          conditions: -> { where(discarded_at: nil) },
                          message: ': Same member list & analytics type already exist'
  validates :employee, presence: true

# ============================= Associations =========================================================================
  belongs_to :employee

  # ============================= Methods ==============================================================================
  def self.analytic_current_year_range(analytics_type, year_start = false, year_end = false)
    year_range = if analytics_type == 'sick'
                   fiscal_year_range(Date.today)
                 else
                   [Date.today.beginning_of_year, Date.today.end_of_year]
                 end

    if year_start
      year_range.first
    elsif year_end
      year_range.second
    else
      year_range
    end
  end

  def self.analytic_current_year_start(analytics_type)
    analytic_current_year_range(analytics_type, true)
  end

  def self.analytic_config_year_end(analytics_type)
    analytic_current_year_range(analytics_type, false, true)
  end


  def self.fiscal_year_range(date)
    if date.month <= 4
      [date.change(year: date.year - 1, day: 1, month: 5),
       date.change(month: 4, day: 30)]
    else
      [date.change(month: 5, day: 1),
       date.change(year: date.year + 1, month: 4, day: 30)]
    end
  end
end
