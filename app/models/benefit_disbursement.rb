# frozen_string_literal: true

class BenefitDisbursement < ApplicationRecord
  delegate :payment_type_name, to: :benefit_disbursement_decorator

  # ============================= Associations =========================================================================
  belongs_to :employee
  belongs_to :employee_benefit, optional: true
  belongs_to :payment_type, optional: true
  belongs_to :benefit_coverage, optional: true
  has_one_attached :file

  # ============================= Validations ==========================================================================
  validates :amount, numericality: { greater_than: 0 }, if: -> { amount_changed? }
  validates :year, numericality: { only_integer: true, greater_than: 0 }, if: -> { year_changed? }
  validates :file, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }

  # ============================= CallBacks ==============================================================================

  before_save :update_coverage_expiration
  before_save :update_entry_date, if: -> { current_account.saas_json.dig('ui', 'employees', 'benefits', 'today_entry_date') == true }

  # ============================= Methods ==============================================================================

  attr_accessor :optical_coverage_expiration, :self_benefit_disbursement

  private

  def benefit_disbursement_decorator
    @benefit_disbursement_decorator ||= BenefitDisbursementDecorator.new(self)
  end

  def update_coverage_expiration
    coverage_expiration = current_account.saas_json.dig('schema', 'benefit_disbursements', 'optical_coverage_expiration')
    if coverage_expiration == true && self.optical_coverage_expiration == true
      employee = self.employee
      employee_benefit = self.employee_benefit
      return if (self.benefit_coverage_id.blank? && self.is_member? == false) || self.benefit_coverage&.expires_at.present? ||
        (self.date.present? && self.date.year < Date.today.year && current_account.saas_json.dig('schema', 'benefit_disbursements', 'calendar_year_expiration') == true) || (self.date.present? && (Date.today - self.date).to_i > 365 && current_account.saas_json.dig('schema', 'benefit_disbursements', 'calendar_year_expiration') != true)

      if employee_benefit && (employee_benefit.end_date.nil? || (employee_benefit.end_date.present? && employee_benefit.end_date > Date.today))

        if self.is_member == true
          employee_benefit&.update(expiration_type: 1, serviced_expiration: self.date) if employee_benefit&.serviced_expiration.blank?
        else
          self.benefit_coverage.update(serviced_expiration: self.date, expiration_type: 1) if self.benefit_coverage&.serviced_expiration.blank? && self&.benefit_coverage&.expires_at.blank?
        end
      end

    end
  end

  def update_entry_date
    self.entry_date = DateTime.now
  end
end
