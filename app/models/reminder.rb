# frozen_string_literal: true

class Reminder < ApplicationRecord
  serialize :schedule, IceCube::Schedule

  # Mappings for scheduling
  WEEK_MAPPING = {
    'first' => 1, 'second' => 2, 'third' => 3, 'fourth' => 4, 'fifth' => 5, 'last' => -1
  }.freeze

  DAY_MAPPING = {
    'monday' => :monday, 'tuesday' => :tuesday, 'wednesday' => :wednesday, 'thursday' => :thursday,
    'friday' => :friday, 'saturday' => :saturday, 'sunday' => :sunday
  }.freeze

  MONTH_MAPPING = {
    'january' => 1, 'february' => 2, 'march' => 3, 'april' => 4, 'may' => 5, 'june' => 6, 'july' => 7,
    'august' => 8, 'september' => 9, 'october' => 10, 'november' => 11, 'december' => 12
  }.freeze

  VALID_RECURRENCE_TYPES = %w[custom daily weekly monthly yearly every\ 3\ months every\ 6\ months].freeze

  enum status: %i[pending scheduled completed]
  #========================================Association=========================================
  belongs_to :creator, class_name: 'User', foreign_key: 'user_id'
  has_and_belongs_to_many :users, join_table: :reminders_users
  has_many :reminder_trackers
  belongs_to :employee_grievance, optional: true
  #========================================Call back============================================
  after_save :schedule_job, if: -> { saved_change_to_reminder_start_date? || saved_change_to_time? }
  #=========================================Validation=======================================
  validates :title, :reminder_start_date, :time, presence: true
  validate :end_date_after_start_date, if: -> { reminder_end_date.present? }

  def schedule_job
    current_time = Time.now.in_time_zone('Eastern Time (US & Canada)')
    if job_id.present? && status == 'scheduled'
      Sidekiq::ScheduledSet.new.find_job(job_id)&.delete
      update_column(:job_id, nil)
    end

    start_time = current_time - 2.days
    end_time = current_time + 2.days
    all_occurrences = schedule.occurrences_between(start_time, end_time)
    current_occurrence = all_occurrences.find do |occ|
      if occ.strftime('%Y-%m-%d') == current_time.strftime('%Y-%m-%d')
        occ
      end
    end

    if current_occurrence && current_occurrence >= current_time
      new_start_time = ActiveSupport::TimeZone['Eastern Time (US & Canada)'].parse("#{current_time.strftime('%Y-%m-%d')} #{time.strftime('%H:%M:%S')}")
      job = ReminderJob.set(wait_until: new_start_time).perform_later(id, Apartment::Tenant.current)
      update_columns(status: :scheduled, job_id: job.provider_job_id)
    elsif status == 'completed'
      update_columns(status: :pending, job_id: nil)
    end
  end

  def end_date_after_start_date
    errors.add(:reminder_end_date, 'must be greater than the start date') if reminder_end_date < reminder_start_date
  end

  def self.build_recurrence_rule(repeat_data)
    if VALID_RECURRENCE_TYPES.include?(repeat_data[:type])
      if repeat_data['daily'].present?
        IceCube::Rule.daily(repeat_data['daily']['every'].to_i)
      elsif repeat_data['weekly'].present?
        IceCube::Rule.weekly(repeat_data['weekly']['every'].to_i).day(*repeat_data['weekly']['days'].map(&:to_sym))
      elsif repeat_data['monthly'].present?
        rule = IceCube::Rule.monthly(repeat_data['monthly']['every'].to_i)
        if repeat_data['monthly']['dates'] || repeat_data['monthly']['date']
          monthly_dates = repeat_data['monthly']['dates'] || repeat_data['monthly']['date']
          rule.day_of_month(*monthly_dates.map(&:to_i))
        elsif repeat_data['monthly']['on_the'] && repeat_data['monthly']['days']
          weeks = [WEEK_MAPPING[repeat_data['monthly']['on_the']]].compact
          days_input = repeat_data['monthly']['days']
          days = case days_input
                 when 'weekday' then %i[monday tuesday wednesday thursday friday]
                 when 'weekend' then %i[saturday sunday]
                 else [DAY_MAPPING[days_input]].compact
                 end
          rule.day_of_week(days.map { |d| [d, weeks] }.to_h)
        end
      elsif repeat_data['yearly'].present?
        rule = IceCube::Rule.yearly(repeat_data['yearly']['every'].to_i)
        if repeat_data['yearly']['month']
          months = repeat_data['yearly']['month'].map { |m| MONTH_MAPPING[m.downcase] }.compact
          rule.month_of_year(*months)
        end
        if repeat_data['yearly']['dates'] || repeat_data['yearly']['date']
          yearly_dates = repeat_data['yearly']['dates'] ||= repeat_data['yearly']['date']
          rule.day_of_month(*yearly_dates.map(&:to_i))
        elsif repeat_data['yearly']['on_the'] && repeat_data['yearly']['days']
          weeks = repeat_data['yearly']['on_the'].map { |w| WEEK_MAPPING[w] }.compact
          days = repeat_data['yearly']['days'].flat_map { |day|
            case day
            when 'weekdays' then %i[monday tuesday wednesday thursday friday]
            when 'weekends' then %i[saturday sunday]
            else [DAY_MAPPING[day]].compact
            end
          }.compact
          rule.day_of_week(days.map { |d| [d, weeks] }.to_h)
        end
      end
    elsif repeat_data[:type] == 'weekdays'
      IceCube::Rule.weekly.day(:monday, :tuesday, :wednesday, :thursday, :friday)
    elsif repeat_data[:type] == 'weekends'
      IceCube::Rule.weekly.day(:saturday, :sunday)
    else
      return nil
    end
  end
end
