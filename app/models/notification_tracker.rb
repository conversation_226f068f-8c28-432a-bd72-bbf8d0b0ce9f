# frozen_string_literal: true

class NotificationTracker < ApplicationRecord
  include PgSearch::Model

  # ============================= Associations =========================================================================
  belongs_to :notification, optional: true
  belongs_to :employee, optional: true
  belongs_to :user, optional: true

  # ================================ Enums =============================================================================
  enum sms_error_code: { 'Invalid Message': 10,
                         'Network Error': 20,
                         'Spam Detected': 30,
                         'Invalid Source Number': 40,
                         'Invalid Destination Number': 50,
                         'Loop Detected': 60,
                         'Destination Permanently Unavailable': 70,
                         'Destination Temporarily Unavailable': 80,
                         'No Route Available': 90,
                         'Prohibited by Carrier': 100,
                         'Message Too Long': 110,
                         'Recipient Registered for India DND': 150,
                         'DLT Registration Issue': 160,
                         'Source Number Blocked by STOP from Destination Number': 200,
                         'Failed to Dispatch Message': 300,
                         'Message Expired': 420,
                         'Destination Country Disabled': 450,
                         'Unusual Activity': 451,
                         'Insufficient Credit': 900,
                         'Account Disabled': 910,
                         'Daily Messaging Limit Reached for Your 10DLC Brand': 950,
                         'Limit Reached or Messages Caught by Spam Filter on Unverified or Pending Verification Toll-Free Number': 960,
                         'Unknown Error': 1000,
                         'MMS Message Payload Too Large': 120,
                         'Unsupported Message Media': 130,
                         'Message Media Processing Failed': 140,
                         'HTTP Error Received From Message URL For Incoming SMS': 203 }

  # ============================= Validation =========================================================================

  validates :sms_error_code, presence: true, allow_blank: true

  # ============================= Scopes =========================================================================
  pg_search_scope :search_by_member,
                  associated_against: { employee: %i[first_name middle_name last_name] },
                  using: { tsearch: { prefix: true, normalization: 2 }, trigram: { word_similarity: true, threshold: 0.3 } },
                  ranked_by: ":trigram + :tsearch"

  # =========================== Email Status Scope =======================================================================
  scope :email_sent, -> { where(email: true, sent: true, delivered: false, rejected: false, bounced: false) }
  scope :email_delivered, -> { where(email: true, sent: true, delivered: true, opened: false, clicked: false) }
  scope :email_opened, -> { where(email: true, sent: true, delivered: true, opened: true, clicked: false) }
  scope :email_clicked, -> { where(email: true, sent: true, delivered: true, opened: true, clicked: true) }
  scope :email_rejected, -> { where(email: true, rejected: true) }
  scope :email_bounced, -> { where(email: true, bounced: true) }
  scope :email_failed, -> { where(email: true).where('rejected = ? OR bounced = ?', true, true) }
  scope :email_undelivered, -> { where(email: true, sent: true, delivered: false) }

  scope :email_filter, lambda { |status|
    case status
    when 'opened' then email_opened
    when 'sent' then email_sent
    when 'delivered' then email_delivered
    when 'clicked' then email_clicked
    when 'rejected' then email_rejected
    when 'un_delivered' then email_undelivered
    when 'failed' then email_failed
    when 'all' then where(email: true)
    else NotificationTracker.none
    end
  }

  scope :sms_filter, lambda { |status|
    result = where(sms: true)
    case status
    when 'sent'
      result.where('sms_sent = ? AND sms_delivered =? AND sms_failed = ?', true, false, false)
    when 'delivered'
      result.where(sms_delivered: true)
    when 'failed'
      result.where(sms_failed: true)
    when 'un_delivered'
      result.where(sms_undelivered: true)
    else
      result
    end
  }

  scope :push_filter, lambda { |status|
    result = where(push: true)
    case status
    when 'sent'
      result.where('push_sent = ?', true)
    when 'all'
      result
    else
      NotificationTracker.none
    end
  }

  scope :search_filter, lambda { |status, type|
    return NotificationTracker.none unless status.present? && type.present?

    case type
    when 'email' then email_filter(status)
    when 'sms' then sms_filter(status)
    when 'push' then push_filter(status)
    when 'all' then email_filter(status).or(sms_filter(status)).or(push_filter(status))
    else NotificationTracker.none
    end
  }

  def email_status
    return '-' unless email?

    if current_account.saas_json.dig('schema', 'notifications', 'sendgrid_templates') == true
      sendgrid_email_status
    else
      normal_email_status
    end
  end

  def sendgrid_email_status
    if delivered? && !sent? && !clicked? && !rejected? && !bounced?
      'Delivered'
    elsif sent? && !delivered? && !opened? && !clicked? && !rejected? && !bounced?
      'Sent'
    elsif opened? && !clicked? && !rejected? && !bounced?
      'Opened'
    elsif clicked? && !rejected? && !bounced?
      'Clicked'
    elsif email_rejected
      'Rejected'
    elsif email_bounced
      'Bounced'
    else
      '-'
    end
  end

  def normal_email_status
    if delivered? && !sent?
      'Delivered'
    elsif email_sent?
      'Sent'
    elsif email_failed?
      'Failed'
    else
      '-'
    end
  end

  def sms_status
    if sms?
      if sms_delivered?
        'Delivered'
      elsif sms_failed?
        'Failed'
      elsif sms_undelivered?
        'Undelivered'
      elsif sms_sent?
        'Sent'
      else
        '-'
      end
    else
      '-'
    end
  end
end
