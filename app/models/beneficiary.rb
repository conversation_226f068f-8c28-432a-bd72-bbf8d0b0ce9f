# frozen_string_literal: true

class Beneficiary < ApplicationRecord
  BENEFICIARY_TYPE = %w[Primary Secondary Contingent AnnuityPrimary AnnuitySecondary].freeze

  enum category: { default_beneficiary: 0, annuity_beneficiary: 1}

  # =============================Defaults scope ========================================================================
  default_scope -> { where(category: 'default_beneficiary') }

  # ============================= Associations =========================================================================
  has_one_attached :file
  belongs_to :employee
  belongs_to :gender, optional: true

  # ============================= Validations ==========================================================================
  validates :beneficiary_type, inclusion: { in: BENEFICIARY_TYPE }, if: -> { beneficiary_type_changed? }
  validates :file, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :percentage, numericality: { greater_than: 0 }, if: -> { percentage_changed? }, allow_blank: true
  validate :percentage_less_than_or_equal_100, if: -> { percentage_changed? }
  validates :name, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ }
  validates :address, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ }, allow_blank: true

  # ============================= Methods ==============================================================================
  def percentage_less_than_or_equal_100
    return if errors.any?

    hash = Beneficiary.unscoped.kept.where(employee_id: employee_id).reject { |b| b.id == id }
    hash << self
    hash = hash.group_by { |b| [b.beneficiary_type, b.category] }.map do |type_of_benefit, records|
        {
          type_of_benefit: type_of_benefit,
          percent: records.map { |b| b.percentage.try(:round, 2) }.compact.sum
        }
    end
    hash.each do |record|
      if record[:percent] > 100.0
        errors.add('Beneficiary', " % for #{record[:type_of_benefit][0]} exceeding a max limit of 100.")
      end
    end
  end
end
