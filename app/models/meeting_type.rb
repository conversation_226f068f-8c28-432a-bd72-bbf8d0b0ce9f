# frozen_string_literal: true

class MeetingType < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  has_many :employee_meeting_types

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false },
                   length: { maximum: 100 }, if: -> { name_changed? }

  # ============================= Callbacks ============================================================================
  after_discard do
    employee_meeting_types.update_all(discarded_at: Time.current)
  end
end
