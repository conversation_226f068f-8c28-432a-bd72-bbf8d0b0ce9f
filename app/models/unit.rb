# frozen_string_literal: true

class Unit < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  has_many :employees

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false },
                   length: { maximum: 100 }, if: -> { name_changed? }

  # ============================= Callbacks ============================================================================
  after_discard do
    Employee.unscoped.where(unit_id: self.id).update_all(unit_id: nil)
  end
end
