# frozen_string_literal: true

class Contact < ApplicationRecord
  module ContactFor
    HOME = 'home'
    PERSONAL = 'personal'
    WORK = 'work'
    COLLEAGUE = 'colleague'
  end

  module ContactType
    EMAIL = 'email'
    PHONE = 'phone'
    EMERGENCY = 'emergency'
  end

  CONTACT_FOR = [
    ContactFor::HOME,
    ContactFor::PERSONAL,
    ContactFor::WORK,
    ContactFor::COLLEAGUE
  ].freeze

  CONTACT_TYPES = [
    ContactType::EMAIL,
    ContactType::PHONE,
    ContactType::EMERGENCY
  ].freeze

  # ============================= Associations =========================================================================
  belongs_to :employee

  # ============================= Validations ==========================================================================
  validates :contact_for, inclusion: { in: CONTACT_FOR }
  validates :contact_type, inclusion: { in: CONTACT_TYPES }
  validates :employee, presence: true
  validates_format_of :value, with: /(\([0-9]{3}\)\s+[0-9]{3}\s+\-\s+[0-9]{4})\z/,
                              if: -> { validate_value?('phone') || validate_value?('emergency') }, message: ': Phone number format is invalid'
  validates_format_of :value, with: Devise.email_regexp, if: -> { validate_value?('email') },
                              message: ': Email format is invalid'

  def validate_value?(contact_type_value)
    value_changed? && value.present? && contact_type == contact_type_value
  end
end
