# frozen_string_literal: true

class ApplicationRecord < ActiveRecord::Base
  include Discard::Model
  include CommonValidator

  # attr_accessor :current_account

  has_paper_trail(
    meta: {
      employee_id: :get_employee_id,
      attachment_name: :attachment_added?
    })

  self.abstract_class = true

  ACCEPTABLE_CONTENT_TYPES = %w[application/msword application/pdf application/rtf application/text application/csv
                                application/vnd.ms-excel application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
                                application/vnd.openxmlformats-officedocument.wordprocessingml.document image/png
                                image/jpg image/jpeg text/plain text/csv image/tiff video/mp4].freeze

  # ============================= Validations ==========================================================================
  # Common dynamic validation based on accounts preference.
  validate :add_custom_validation

  # ============================= Associations =========================================================================
  before_validation :strip_whitespaces

  # ============================= Methods =========================================================================
  def strip_whitespaces
    attributes.each do |key, value|
      self[key] = value.strip if value.respond_to?('strip')
    end
  end

  def current_account
     @current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
  end

  def get_employee_id
    return unless PaperTrail.request.whodunnit.present?

    if self.class.name == 'Employee'
      id
    elsif self.class.name.downcase.include?('step')
      class_name = self.class.name.gsub('Step', '')
      class_name = class_name.underscore
      self.send(class_name)&.employee&.id
    elsif self.class.name.constantize._reflect_on_association(:employee).present?
      employee&.id
    end
  end

  def attachment_added?
    return "" if attachment_changes.blank?

    attachment_find_out
  end

  def attachment_find_out
    attachments_names = {}
    self._reflections.select do |association_name|
      next unless association_name.include?('attachment')

      attachment_name =  if association_name == "sms_attachments_blobs"
                           "sms_attachments"
                         else
                           association_name.gsub('sms_attachments_attachments', '').gsub('_attachments', '').gsub('_attachment', '')
                         end

      next if attachment_name.blank? ||  self.send(attachment_name).blank?

      before_attachment_names = versions.where("attachment_name IS NOT NULL").order(id: :asc)&.last&.attachment_name
      old_files = [before_attachment_names.present? ? eval(before_attachment_names)[attachment_name]&.last : nil]

      new_files = if attachment_name.pluralize == attachment_name
                    self.send(attachment_name)&.blobs&.map { |b| b.filename.to_s }
                  else
                    [self.send(attachment_name)&.blob&.filename.to_s]
                  end

      attachments_names[attachment_name] = [old_files, new_files]
    end
    attachments_names
  end
end
