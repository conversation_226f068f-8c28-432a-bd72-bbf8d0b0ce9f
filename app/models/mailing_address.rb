class MailingAddress < ApplicationRecord
  # ============================= Associations =========================================================================
  belongs_to :employee

  # ============================= Validations ==========================================================================
  validates :street, :city, :state, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ }, allow_blank: true
  validates_length_of :zipcode, maximum: 5, allow_blank: true

  def full_address
    address_parts = [street, apartment, city].reject(&:blank?)
    state_zip = [state, zipcode].reject(&:blank?).join(' ')
    address = address_parts.join(', ')
    address += ', ' + state_zip if state_zip.present?

    address
  end
end
