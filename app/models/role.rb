# frozen_string_literal: true

class Role < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  has_many :users
  has_and_belongs_to_many :rights

  validates :name, presence: true
  validates :name, length: { maximum: 100 },
                   uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false }

  def self.create_role_with_rights(role_name, rights = [], excluded_rights = nil)
    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    exclude_keys = current_account.saas_json.dig('schema','employees','permitted_editable_fields')&.keys
    role = Role.kept.where(name: role_name).first_or_create!
    role.rights = []
    rights = role_name == 'Admin' ? Right.kept.all : Right.kept.where(name: rights)
    rights = rights.where.not(name: exclude_keys) unless excluded_rights == 'true'
    role.rights << rights
  end
end
