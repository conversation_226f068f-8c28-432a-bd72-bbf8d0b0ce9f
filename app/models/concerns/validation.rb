# frozen_string_literal: true

module Validation
  def validate_date_range
    return unless (start_date_changed? || end_date_changed?) && start_date.present? && end_date.present?

    errors.add('Date range', ' is invalid - End date is greater than Start date') if start_date > end_date
  end

  def check_amount
    self.current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
    relationship_amount_validation = current_account.saas_json.dig('schema', 'dependents', 'relationship_amount_validation')

    unless relationship_amount_validation.nil?
      if relationship_amount_validation.dig(self.life_insurance.insurance_type.downcase).key?(self.relationship.downcase)
        unless relationship_amount_validation.dig(self.life_insurance.insurance_type.downcase, self.relationship.downcase).include?(self.amount)
          errors[:amount] << "Invalid amount."
          throw(:abort)
        end
      end
    end
  end

  def check_relationship
    current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
    dependent_relationship = current_account.saas_json.dig('schema', 'dependents', 'relationship_value')

    dependent_relationship_values = dependent_relationship.each_with_object({}) do |relationship_value, relationship_key|
      relationship_key[relationship_value["value"]] = relationship_value["key"]
    end

    life_insurance = LifeInsurance.kept.find(life_insurance_id).dependents.where(relationship: dependent_relationship_values["Spouse"])

    if self.relationship == dependent_relationship_values["Spouse"] && life_insurance.present? && !self.id || life_insurance.count != 0 && self.relationship_was == dependent_relationship_values["Child"]
      errors[:relationship] << " was already exist."
      throw(:abort)
    end
  end
end
