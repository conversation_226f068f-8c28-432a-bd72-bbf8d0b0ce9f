# frozen_string_literal: true

module EmployeeDeviseValidatorOverride
  extend ActiveSupport::Concern

  included do
    if Apartment::Tenant.current != 'public'
      current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
      current_model_schema = current_account.saas_json['schema']['employees']

      if current_model_schema['username'].present? && current_model_schema['password'].present? && current_model_schema['password_confirmation'].present?
        # Customised validations from devise validatable
        extend Devise::Models::Validatable::ClassMethods

        validates_presence_of :username, allow_blank: true, if: :username_required?
        validates_uniqueness_of :username,
                                allow_blank: true, case_sensitive: true,
                                conditions: -> { where(discarded_at: nil) }, if: :will_save_change_to_username?
        validates_length_of :username, minimum: 6, maximum: 50, allow_blank: true, if: :will_save_change_to_username?
        validates_format_of :username, with: /\A\S*\z/, allow_blank: true, if: :will_save_change_to_username?

        validates_presence_of :password, allow_blank: true, if: :password_required?
        validates_confirmation_of :password, if: :password_required?
        validates_length_of :password, within: password_length, allow_blank: true
      end

      def username_required?
        true
      end

      def password_required?
        !persisted? || !password.nil? || !password_confirmation.nil?
      end
    end
  end
end
