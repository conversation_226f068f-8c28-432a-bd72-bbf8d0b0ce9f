# frozen_string_literal: true

module CommonValidator
  # Skipping the common validation for the models which are used internally(not subjected to change based on account)
  SKIP_VALIDATION = %w[Account BlacklistedToken Report Right Role Totality UserContact].freeze

  # rubocop:disable Layout/LineLength
  def add_custom_validation
    return if SKIP_VALIDATION.include?(self.class.name)

    self.current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
    current_model_schema = if self.class.name == "Employee" && self.category.present? && self.category == "contact_person"
                             current_account.saas_json['schema']['contact_persons']
                           else
                             current_account.saas_json['schema'][self.class.name.underscore.pluralize]
                           end
    # TODO: Check and add logs/ notify in slack
    return if current_model_schema.blank?

    required_fields = current_model_schema['required_fields']
    required_fields = required_fields - current_model_schema['do_not_email_required_validation_fields'] if current_model_schema['do_not_email_required_validation_fields'].present? && self.do_not_mail == true
    required_fields &= self.class.column_names # Non-column keys are used in FE.
    validates_presence_of required_fields.map(&:to_sym) if required_fields.present?

    unique_fields = current_model_schema['unique_fields']
    if unique_fields.present?
      unique_fields = (self.current_account.subdomain == 'cobanc' ? unique_fields - ['a_number'] : unique_fields)
      unique_fields.push({ conditions: Proc.new { where(discarded_at: nil) }, allow_blank: true })
      validates_with ActiveRecord::Validations::UniquenessValidator, _merge_attributes(unique_fields) if unique_fields.size > 1
      if current_model_schema['unique_fields'].include?('a_number') && self.current_account.subdomain == "cobanc"
        validates_with ActiveRecord::Validations::UniquenessValidator, _merge_attributes(["a_number"]) if validate_a_number(attributes)
      end
    end

    starts_with = current_model_schema.dig('custom_validations', 'starts_with')
    starts_with&.delete("a_number") if self.current_account.subdomain == "cobanc" && attributes["a_number"].blank?
    validates_with validate_starts_with(starts_with) if starts_with.present?

    integer_fields = current_model_schema.dig('custom_validations', 'integer_fields')
    validates_numericality_of integer_fields.map(&:to_sym), numericality: { only_integer: true }, allow_blank: true if integer_fields.present?

    minimum_one_required_fields = current_model_schema.dig('custom_validations', 'minimum_one_required_fields')
    at_least_one_field_present(minimum_one_required_fields) if minimum_one_required_fields.present?
  end

  # rubocop:enable Layout/LineLength

  def validate_starts_with(attributes)
    attributes.each do |attribute|
      errors.add(attribute[0].to_sym, " should starts with #{attribute[1]}") unless send(attribute[0].to_sym).present? && send(attribute[0].to_sym).start_with?(attribute[1])
    end
  end

  def validate_a_number(attributes)
    ['COA', ''].exclude?(attributes['a_number'])
  end

  def at_least_one_field_present(attributes)
    attributes_present = []
    attributes.each do |attribute|
      if send(attribute).present?
        attributes_present << true
        break
      end
    end
    return if attributes_present.present?

    errors.add('', 'Please fill in at least one field before submitting')
  end
end
