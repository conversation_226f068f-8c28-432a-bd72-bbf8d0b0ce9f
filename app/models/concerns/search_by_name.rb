module SearchByName
  extend ActiveSupport::Concern
  include PgSearch::Model

  included do
    # ============================= Scopes ===============================================================================
    pg_search_scope :search_by_name,
                    against: %i[ name ],
                    using: { tsearch: { prefix: true, normalization: 2  }, trigram: { word_similarity: true, threshold: 0.3 } },
                    ranked_by: ":trigram + :tsearch"

  end
end