# frozen_string_literal: true

class Right < ApplicationRecord
  # ============================= Constants ============================================================================
  # Rights for the below models will be handled in a different way. Check 'ability.rb'
  IGNORED_MODELS = %w[Report EmployeeDepartment EmployeeSection EmployeeTitle Contact EmployeeEmploymentStatus
                      EmployeeOfficerStatus EmployeePosition EmployeeOffice EmployeeRank DelegateAssignment Contact
                      FirearmRangeScore Role Leave Dependent Lodi EmployeeDisciplineStep EmployeeGrievanceStep].freeze
  DEFAULT_RIGHTS = %w[read_user_profile write_user_profile].freeze
  REPORT_RIGHTS = %w[report_single_employee read_report_single_employee report_sick_bank read_report_sick_bank
                     report_grievances read_report_grievances report_disciplines read_report_disciplines
                     report_janus read_report_janus report_benefits read_report_benefits report_lodi read_report_lodi
                     report_union_meetings read_report_union_meetings
                     report_employee_delegate_assignment read_report_employee_delegate_assignment
                     report_life_insurances read_report_life_insurances report_pacfs read_report_pacfs report_workers_comp
                     read_report_workers_comp report_benefit_coverages read_report_benefit_coverages report_benefit_coverage_expiration
                     read_report_benefit_coverage_expiration report_beneficiary read_report_beneficiary report_member_count
                     read_report_member_count ].freeze
  SETTINGS_READ_RIGHTS = %w[read_gender read_grievance read_grievance_status read_marital_status read_office
                            read_paf read_payment_type read_delegate_series read_position read_meeting_type read_section
                            read_title read_unit read_department read_rank read_officer_status read_discipline_setting
                            read_discipline_charge read_employment_status read_analytics_configuration read_firearm_status
                            read_benefit read_affiliation read_tour_of_duty read_platoon read_discipline_status read_facility].freeze
  SETTINGS_WRITE_RIGHTS = %w[write_gender write_grievance write_grievance_status write_marital_status write_office
                             write_paf write_payment_type write_delegate_series write_position write_meeting_type
                             write_section write_title write_unit write_department write_rank write_officer_status
                             write_discipline_setting write_discipline_charge write_employment_status
                             write_analytics_configuration write_firearm_status write_benefit write_affiliation
                             write_tour_of_duty write_platoon write_discipline_status write_facility].freeze
  SETTINGS_RIGHTS = SETTINGS_READ_RIGHTS + SETTINGS_WRITE_RIGHTS
  CONTACT_PERSON_WRITE_RIGHTS = %w[write_contact_person]
  CONTACT_PERSON_READ_RIGHTS = %w[read_contact_person]
  CONTACT_PERSON_RIGHTS = CONTACT_PERSON_READ_RIGHTS + CONTACT_PERSON_WRITE_RIGHTS
  REMINDER_READ_RIGHTS = %w[read_reminder read_reminder_user]
  REMINDER_WRITE_RIGHTS = %w[write_reminder]
  REMINDER_RIGHTS = REMINDER_READ_RIGHTS + REMINDER_WRITE_RIGHTS
  EMPLOYEE_READ_RIGHTS = %w[read_employee read_employee_paf read_employee_analytics read_employee_award
                            read_employee_discipline_setting read_employee_meeting_type read_employee_firearm_status
                            read_employee_grievance read_employee_upload read_employee_benefit read_benefit_coverage
                            read_benefit_disbursement read_beneficiary read_disability read_life_insurance read_user_employee_analytics
                            read_legislative_address read_note read_pesh read_assault read_witness read_user_audit read_form read_hearing]
  EMPLOYEE_WRITE_RIGHTS = %w[write_employee write_employee_paf write_employee_analytics write_employee_award
                             write_employee_discipline_setting write_employee_meeting_type write_employee_firearm_status
                             write_employee_grievance write_employee_upload write_employee_benefit write_benefit_coverage
                             write_benefit_disbursement write_beneficiary write_disability write_life_insurance write_note write_pesh
                             write_assault write_witness write_hearing].freeze
  EMPLOYEE_RIGHTS = EMPLOYEE_READ_RIGHTS + EMPLOYEE_WRITE_RIGHTS
  NOTIFICATION_RIGHTS = %w[write_notification read_notification write_device read_device].freeze
  MEMBER_NOTIFICATION_RIGHTS = %W[read_notification write_device].freeze

  CHANGE_REQUEST_WRITE_RIGHTS = %w[write_change_request].freeze

  CHANGE_REQUEST_READ_RIGHTS = %w[read_change_request].freeze

  MAILLOG_RIGHTS = %w[read_maillog write_maillog]

  FORM_RIGHTS = %w[read_form]

  MEMBER_RIGHTS = SETTINGS_READ_RIGHTS + EMPLOYEE_READ_RIGHTS + CHANGE_REQUEST_WRITE_RIGHTS + MEMBER_NOTIFICATION_RIGHTS + FORM_RIGHTS

  VIEW_EMPLOYEE_WRITE_RIGHTS = %w[write_placard_only].freeze

  USER_RIGHTS = %w[write_user read_user write_user_profile read_user_profile].freeze
  SUPER_USER_RIGHTS = %w[write_super_user read_super_user].freeze

  RIGHTS = (SETTINGS_RIGHTS + REPORT_RIGHTS + EMPLOYEE_RIGHTS + USER_RIGHTS + SUPER_USER_RIGHTS + NOTIFICATION_RIGHTS + CHANGE_REQUEST_READ_RIGHTS + CHANGE_REQUEST_WRITE_RIGHTS + CONTACT_PERSON_RIGHTS + MAILLOG_RIGHTS + FORM_RIGHTS + VIEW_EMPLOYEE_WRITE_RIGHTS + REMINDER_RIGHTS).freeze

  # ============================= Associations =========================================================================
  has_and_belongs_to_many :roles

  # ============================= Validations ==========================================================================
  validates :name, presence: true
  validates :rights_type, presence: true

  # ============================= Methods ==============================================================================
  # This method will create 'rights' based on the account preference.
  # Create 'Admin role'
  def self.create_rights(tenant)
    Apartment::Tenant.switch!(tenant)
    @current_account = Account.find_by(subdomain: tenant)
    return if @current_account.blank?

    @ignored_tables = IGNORED_MODELS.map { |model| model.constantize.table_name }
    @schema_keys = @current_account.saas_json['schema'].keys
    is_super_account = @current_account.saas_json.dig('super_account')

    insert_rights(DEFAULT_RIGHTS) # Creates rights which is not based on the account preference(or which is not available in the account JSON schema)

    # Insert all rights if the account is super account
    if is_super_account
      insert_rights(RIGHTS)
    else
      create_rights_from_keys(@schema_keys - @ignored_tables)
      create_rights_from_selected_tabs
    end

    account_admin_rights = Right.all.pluck(:name) - %w[read_user write_user read_super_user write_super_user]
    account_admin_rights -= SETTINGS_RIGHTS if @current_account.subdomain == 'nyccoba'
    Role.create_role_with_rights('Admin')
    Role.create_role_with_rights('Account Admin', account_admin_rights)
    Role.create_role_with_rights('No Access')
    Apartment::Tenant.reset
  end

  def self.create_rights_from_keys(keys)
    keys.each do |key|
      right_type = modify_right_name(key.singularize)
      insert_rights(['read_' + right_type, 'write_' + right_type])
      insert_rights(['read_user_' + right_type]) if right_type == 'employee_analytics'
    end
  end

  def self.create_rights_from_selected_tabs
    all_tabs = []
    @current_account.saas_json['ui'].keys.each do |selected_module|
      next if selected_module.blank?
      next if %w[notes_disabled notes_disabled_fields notes_timestamps allow_single_timestamp is_read_view_allows].include?(selected_module)

      selected_tabs = @current_account.saas_json['ui'][selected_module]['tabs']
      next if selected_tabs.blank?

      if selected_module == 'reports'
        selected_tabs.each do |tab|
          insert_rights(['report_' + tab])
          insert_rights(['read_report_' + tab])
        end
      else
        all_tabs << selected_tabs
      end
    end
    create_rights_from_keys(all_tabs.flatten - @schema_keys - @ignored_tables)
  end

  def self.insert_rights(rights)
    rights.each do |right|
      if Right::RIGHTS.exclude?(right)
        p "Right is not available in predefined rights: #{right}"
        next
      end

      Right.where(name: right, rights_type: generate_right_type(right)).first_or_create!
    end
  end

  def self.generate_right_type(right)
    if right.starts_with?('write_')
      right.delete_prefix('write_')
    elsif right.starts_with?('read_')
      right.delete_prefix('read_')
    else
      right
    end
  end

  def self.modify_right_name(right_name)
    if %w[award upload].include?(right_name)
      'employee_' + right_name
    elsif right_name.include?('pacf')
      right_name.gsub('pacf', 'paf')
    elsif %w[employee_analytics_configuration employee_analytic].include?(right_name)
      right_name.pluralize
    else
      right_name
    end
  end
end
