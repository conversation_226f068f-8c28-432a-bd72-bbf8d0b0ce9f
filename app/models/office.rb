# frozen_string_literal: true

class Office < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  has_many :delegate_assignments, -> { where(delegate_assignments: { discarded_at: nil }) }
  has_many :employee_offices, -> { where(employee_offices: { discarded_at: nil }) }
  has_many :lodis, -> { where(lodis: { discarded_at: nil }) }
  has_many :peshes
  has_many :assaults
  has_many :employee_grievances
  has_many :improper_practices

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false },
                   length: { maximum: 100 }, if: -> { name_changed? }

  # ============================= Callbacks ============================================================================
  after_discard do
    delegate_assignments.update_all(discarded_at: Time.current)
    employee_offices.update_all(discarded_at: Time.current)
    lodis.update_all(discarded_at: Time.current)
  end
end
