# frozen_string_literal: true

class Lodi < ApplicationRecord
  module LodiTypes
    LODI = 'lodi'
    WORKERS_COMP = 'workers_comp'
  end

  LODI_TYPES = [
      LodiTypes::LODI,
      LodiTypes::WORKERS_COMP
  ].freeze

  delegate :total_hours_used, :totality, to: :lodi_decorator

  # ============================= Associations =========================================================================
  belongs_to :employee
  belongs_to :office, optional: true
  has_many :lodi_request_tabs
  has_and_belongs_to_many :denied_reasons, join_table: :lodi_denied_reasons
  has_many_attached :files

  #================================== Scopes ===========================================================================
  scope :lodi_leaves, -> { kept.where(lodi_type: LodiTypes::LODI) }
  scope :workers_comp_leaves, -> { kept.where(lodi_type: LodiTypes::WORKERS_COMP) }

  #========================================== Nested Attributes =======================================================
  accepts_nested_attributes_for :denied_reasons

  # ============================= Validations ==========================================================================
  validates :lodi_type, inclusion: {in: LODI_TYPES}
  validate :validate_date_range
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }

  # ============================= Callbacks ============================================================================
  after_save :compute_total_hours_used
  before_update :destroy_lodi_denied_reasons, if: -> { denied_changed? }

  after_discard do
    lodi_request_tabs.update_all(discarded_at: Time.current)
    denied_reasons.update_all(discarded_at: Time.current)
  end

  # ============================= Methods ==============================================================================

  private

  def lodi_decorator
    @lodi_decorator ||= LodiDecorator.new(self)
  end

  def compute_total_hours_used
    @totality = if totality.present?
                  totality.update(value: total_hours_used)
                else
                  Totality.create(employee_id: employee_id, totalable_type: 'lodi', value: total_hours_used)
                end
    # reload # REMOVING ```reload``` SINCE IT WILL RELOAD THE ATTRIBUTES AND DELETED THE FILES BEFORE ITS BEEN PROCESSED
  end

  def destroy_lodi_denied_reasons
    denied_reasons.destroy_all if denied_changed?(from: true, to: false)
  end

  def validate_date_range
    return unless (incident_date_changed? || return_date_changed?) && incident_date.present? && return_date.present?

    errors.add('Date range', ' is invalid') if incident_date > return_date
  end
end
