# frozen_string_literal: true

class EmployeeOfficerStatus < ApplicationRecord
  include Validation

  delegate :name, to: :officer_status, allow_nil: true

  after_save :update_expiry_status
  # ================== Associations ====================================================================================
  belongs_to :employee
  belongs_to :officer_status

  # ================== Validations ====================================================================================
  validate :validate_date_range
  validates :start_date, presence: true, if: :end_date?

  def update_expiry_status
    expiry_status_names = current_account.saas_json.dig('schema', 'employee_offices', 'employee_benefits', 'section_III')
    if expiry_status_names.present? && self.officer_status.present? && expiry_status_names.include?(self.officer_status.name.downcase.gsub(" ", "_").gsub("-", "_")) && self.start_date.present?
      self.employee.employee_benefits.where('end_date is null').update_all(end_date: self.start_date)
      if current_account.saas_json.dig('schema','employee_offices','auto_expire_coverages') == true
        self.employee.benefit_coverages.where('expires_at is null').update_all(expires_at: self.start_date)
      end
    end
  end
end
