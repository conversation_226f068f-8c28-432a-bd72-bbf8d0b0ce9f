# frozen_string_literal: true

class EmployeeBenefit < ApplicationRecord
  include Validation

  delegate(
      :name,
      to: :benefit
  )

  delegate(
      :benefit_disbursements_total_amount,
      to: :benefit_disbursements_decorator
  )
  # ============================= enums =========================================================================
  enum expiration_type: { default: 0, disbursement_expiration: 1 }

  # ============================= Associations =========================================================================
  belongs_to :benefit
  belongs_to :employee
  has_many :benefit_coverages, -> { where(benefit_coverages: { discarded_at: nil }) }
  has_many :benefit_disbursements, -> { where(benefit_disbursements: { discarded_at: nil }) }

  # ============================= Callbacks =========================================================================
  after_discard do
    benefit_coverages.update_all(discarded_at: Time.current)
    benefit_disbursements.update_all(discarded_at: Time.current)
  end

  after_save :set_expiration_for_all_benefits
  after_save :update_coverage_person_code, unless: -> { previous_changes.key?('discarded_at') }
  # ============================= Validations =========================================================================
  validate :validate_date_range
  validates :start_date, presence: true, if: :end_date?

  private

  def benefit_disbursements_decorator
    @benefit_disbursements_decorator ||= BenefitDisbursementsDecorator.new(benefit_disbursements)
  end

  def set_expiration_for_all_benefits
    if self.expire_all_benefits == true && current_account.saas_json.dig('schema','employee_benefits', 'expire_all_benefits').present?
      ExpireAllBenefitsJob.perform_later(self)
    end
  end

  def update_coverage_person_code
    if current_account.saas_json.dig('schema', 'benefit_coverages', 'cvs_person_code_autoupdate') == true
      return unless benefit.name&.downcase == 'prescription drug benefit'

      coverages = employee.benefit_coverages.order("DATE_PART('year', AGE(birthday)) DESC")
      BenefitCoverage.set_person_codes(coverages)
    elsif current_account.saas_json.dig('schema', 'benefit_coverages', 'araya_person_code_autoupdate') == true
      return unless benefit.name&.downcase == 'prescription'

      coverages = benefit_coverages.where('expires_at IS NULL OR expires_at > ?', Date.today)
      BenefitCoverage.set_person_codes(coverages, true)
    end
  end
end
