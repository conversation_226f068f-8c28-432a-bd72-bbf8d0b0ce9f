# frozen_string_literal: true

class UserContact < ApplicationRecord
  module ContactFor
    HOME = 'home'
    PERSONAL = 'personal'
    WORK = 'work'
    COLLEAGUE = 'colleague'
  end

  module ContactType
    EMAIL = 'email'
    PHONE = 'phone'
    EMERGENCY = 'emergency'
  end

  CONTACT_FOR = [
    ContactFor::HOME,
    ContactFor::PERSONAL,
    ContactFor::WORK,
    ContactFor::COLLEAGUE
  ].freeze

  CONTACT_TYPES = [
    ContactType::EMAIL,
    ContactType::PHONE,
    ContactType::EMERGENCY
  ].freeze

  # ============================= Associations =========================================================================
  belongs_to :user

  # ============================= Validations ==========================================================================
  validates :contact_for, inclusion: { in: CONTACT_FOR }
  validates :contact_type, inclusion: { in: CONTACT_TYPES }
  validates :user, presence: true
end
