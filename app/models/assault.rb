# frozen_string_literal: true

class Assault < ApplicationRecord
  #============================== Association ========================================
  has_many_attached :files
  has_many :witnesses
  belongs_to :employee, optional: true
  belongs_to :office, optional: true
  belongs_to :type_of_incident, optional: true
  accepts_nested_attributes_for :witnesses
  #============================== Validation ======================================
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :date, presence: true
  #============================= Methods ============================================
  after_discard do
    witnesses.update_all(discarded_at: Time.current)
  end
end
