# frozen_string_literal: true

class ChangeRequest < ApplicationRecord

  include PgSearch::Model

  # ============================= Scopes ===============================================================================

  pg_search_scope :search_by_change_request,
                  against: %i[request_type],
                  associated_against: { employee: %i[first_name middle_name last_name suffix] },
                  using: { tsearch: { prefix: true, normalization: 2 }, trigram: { word_similarity: true, threshold: 0.3 } },
                  ranked_by: ':trigram + :tsearch'

  # ============================= Enum ===========================================================================
  request_type_options = %w[employee contact award beneficiary benefit_coverage benefit_disbursement delegate_assignment
                            dependent employee_benefit employee_department employee_discipline_setting employee_discipline_step
                            employee_employment_status employee_firearm_status employee_grievance_step employee_grievance
                            employee_meeting_type employee_officer_status employee_office employee_pacf employee_position
                            employee_rank employee_section employee_title firearm_range_score leave life_insurance lodi
                            lodi_request_tab upload employee_facility assault]

  enum request_type: request_type_options
  enum status: %i[pending completed]

  # ============================= Associations =========================================================================
  belongs_to :employee
  has_many :change_request_uploads, dependent: :delete_all
  validates :request_type, presence: true, inclusion: { in: request_type_options }

  # ============================= CallBacks =============================================================================
  after_update :update_change_request_status
  after_create :create_analytics_for_leave_types, if: -> { request_type == 'leave' }
  after_create :push_notification_on_beneficiary_change, if: -> { request_type == 'beneficiary' && current_account.saas_json.dig('schema', 'beneficiaries', 'notify_before_beneficiary_change') == true }

  # ============================= Methods =============================================================================

  def get_requested_changes(requested_changes, change_request, content_type, request_type)
    object_changes = change_request

    requested_changes&.each do |change|
      object_changes_ids = []
      object_changes_timestamps = []
      object_changes.each do |x|
        object_changes_ids << x.dig("id") if x.dig("id") != nil
        object_changes_timestamps << x.dig("timestamp") if x.dig("timestamp") != nil
      end
      change.each { |value| change = value } if content_type == "multipart/form-data"

      if change.key?(:remove_all_files)
        change.delete(:remove_all_files)
        change.merge!({ files: [] })
      end

      if object_changes.empty? || request_type == 'employee' || (!object_changes_ids.include?(change.to_enum.to_h.dig("id")) && !object_changes_timestamps.include?(change.to_enum.to_h.dig("timestamp")))
        if !object_changes.empty? && request_type == 'employee'
          change.to_enum.to_h.each do |key1, value1|
            object_changes.each do |object_changes_hash|

              if object_changes_hash.keys.sort_by(&:length) == change.to_enum.to_h.keys.sort_by(&:length) && object_changes_hash.keys.include?(key1)
                if object_changes_hash.keys.include?(key1) && key1.include?('attributes') &&
                  change[key1].to_enum.to_h.keys.sort_by(&:length) != object_changes_hash[key1].keys.sort_by(&:length)
                  change[key1].each { |key, val| object_changes_hash[key1][key] = val }
                else
                  object_changes_hash[key1] = value1
                end
              elsif !object_changes.include?(change.to_enum.to_h)
                object_changes << change.to_enum.to_h
              end
            end
          end
        else
          object_changes.unshift(change.to_enum.to_h)
        end
      elsif !request_type == 'employee' || object_changes_ids.include?(change.to_enum.to_h.dig("id")) || object_changes_timestamps.include?(change.to_enum.to_h.dig("timestamp"))
        change.to_enum.to_h.each do |key1, value1|
          object_changes.each do |object_changes_hash|

            if object_changes_hash.keys.include?(key1) && ((object_changes_hash.dig("id") == change.to_enum.to_h.dig("id") && object_changes_hash.dig("id") != nil) ||
              (object_changes_hash.dig('timestamp') == change.to_enum.to_h.dig('timestamp') && object_changes_hash.dig('timestamp') != nil))
              if key1 == 'witnesses_attributes' && request_type == 'assault'
                get_witnesses_details(object_changes_hash, key1, change)
              else
                object_changes_hash[key1] = value1
              end
            elsif (object_changes_hash.dig("id") == change.to_enum.to_h.dig("id") && object_changes_hash.dig("id") != nil) ||
              (object_changes_hash.dig("timestamp") == change.to_enum.to_h.dig("timestamp") && object_changes_hash.dig("timestamp") != nil)
              object_changes_hash.merge!(key1 => value1)
            end
          end
        end
      end
    end

    object_changes.uniq
  end

  def update_status(requested_change, status, request_type)
    self.requested_changes.each do |change_request|
      if (requested_change.keys.include?('id') && change_request.keys.include?('id') && requested_change['id'] == change_request['id']) ||
          (requested_change.keys.include?('timestamp') && change_request.keys.include?('timestamp') && requested_change['timestamp'] == change_request['timestamp'])
        if request_type == "employee"
          change_request['status'] = status if requested_change == change_request
        else
          change_request['status'] = status
        end
      end
    end
  end

  def update_change_request_status
    array_of_requested_changes = self.requested_changes.select { |requested_change| requested_change['status'] == "pending" }
    self.update_column(:status, "completed") if array_of_requested_changes.size == 0
  end

  def beneficiary_percentage
    if self.request_type.downcase == 'beneficiary'
      primary_percentage = 0
      secondary_percentage = 0
      request_ids = []

      self.requested_changes.each do |requested_change|
        request_ids << requested_change['id'] if requested_change['id'].present? && requested_change['beneficiary_type'].present?

        if requested_change['id'].present? && requested_change['beneficiary_type'].present? && !requested_change['percentage'].present?
          percentage = Beneficiary.kept.where(employee_id: requested_change['employee_id'], id: requested_change['id'])
          primary_percentage += percentage.first.percentage if requested_change['beneficiary_type'].downcase == 'primary' && percentage.present?
          secondary_percentage += percentage.first.percentage if requested_change['beneficiary_type'].downcase == 'secondary' && percentage.present?
        end
        beneficiary_errors(primary_percentage, secondary_percentage)
      end

      # Existing - beneficiaries percentages
      hash = Beneficiary.kept.where(employee_id: self.employee_id).where.not(id: request_ids)

      hash = hash.group_by(&:beneficiary_type).map { |type_of_benefit, records| { type_of_benefit: type_of_benefit, percent: records.sum { |b| b.percentage.try(:round, 2) } } }
      hash.each do |beneficiary|
        if beneficiary[:type_of_benefit].present?
          if beneficiary[:type_of_benefit].downcase == 'primary'
            primary_percentage += beneficiary[:percent].to_f
          elsif beneficiary[:type_of_benefit].downcase == 'secondary'
            secondary_percentage += beneficiary[:percent].to_f
          end
        end
        beneficiary_errors(primary_percentage, secondary_percentage)
      end

      # Change request - beneficiaries percentages
      self.requested_changes.each do |requested_change|
        beneficiary_type = requested_change['beneficiary_type'].downcase if requested_change['beneficiary_type'].present?
        unless requested_change['status'] == 'rejected'
          if requested_change['percentage'].present? && beneficiary_type == 'primary'
            primary_percentage += requested_change['percentage'].to_f
          elsif requested_change['percentage'].present? && beneficiary_type == 'secondary'
            secondary_percentage += requested_change['percentage'].to_f
          end
        end
        beneficiary_errors(primary_percentage, secondary_percentage)
      end
    end
  end

  def beneficiary_errors(primary_percentage, secondary_percentage)
    beneficiary_type = self.requested_changes.first['beneficiary_type']

    if beneficiary_type.downcase == 'primary' && primary_percentage > 100
      errors.add('Beneficiary', " % for primary exceeding a max limit of 100.")
    elsif beneficiary_type.downcase == 'secondary' && secondary_percentage > 100
      errors.add('Beneficiary', " % for secondary exceeding a max limit of 100.")
    end
  end

  def get_witnesses_details(object_changes_hash, key1, change)
    change.to_enum.to_h[key1].each do |witness_details|
      object_arr = object_changes_hash[key1].select { |hash| hash['id'].present? && hash['id'] == witness_details['id'] }
      object_arr = object_changes_hash[key1].select { |hash| hash['timestamp'].present? && hash['timestamp'] == witness_details['timestamp'] } if object_arr.blank?
      object_hash = object_arr.first

      if object_hash.present?
        keys_include = witness_details.keys
        keys_include.each { |key2| object_hash[key2] = witness_details[key2] }
      else
        object_changes_hash[key1] << witness_details.to_enum.to_h
      end
    end
  end

  def create_analytics_for_leave_types
    return unless current_account.saas_json.dig('schema', 'employees', 'analytics_of_employee_user') == true

    requested_changes.each do |requested_changes|
      employee = self.employee
      leave_type = requested_changes['leave_type']
      duration_from, duration_to = Date.parse(requested_changes['started_at']), Date.parse(requested_changes['ended_at'])
      duration_from_changed, duration_to_changed = ChangeRequest.get_from_and_to_date(requested_changes['started_at'], leave_type)
      is_current_or_future_year = duration_from.year >= Date.today.year || duration_to.year >= Date.today.year
      days_earned = is_current_or_future_year ? Leave::EARNED_LEAVES[leave_type] : 0
      analytics_configuration = employee.analytics_configurations.kept.where(duration_from: duration_from_changed, duration_to: duration_to_changed, analytics_type: leave_type)&.first

      analytics_configuration_hash = { employee_id: employee.id, days_earned: days_earned, days_used: 0, notes: requested_changes['notes'] || '',
                                       analytics_type: leave_type, duration_from: duration_from_changed, duration_to: duration_to_changed }
      AnalyticsConfiguration.create!(analytics_configuration_hash) if analytics_configuration.blank?
    end
  end

  def push_notification_on_beneficiary_change
    push_message = 'Note, no beneficiary change is final until the union receives a signed dated beneficiary form.'
    notification = Notification.new(push: true, push_message: push_message, change_request_notification: true, filters: { 'employee_ids' => [employee.id.to_s] })
    NotificationJob.perform_later(notification.id) if notification.save
  end

  def self.get_from_and_to_date(date, type)
    if type == 'sick'
      AnalyticsConfiguration.fiscal_year_range(Date.parse(date))
    else
      [Date.parse(date).beginning_of_year, Date.parse(date).end_of_year]
    end
  end
end
