class DelegateSeries < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  has_many :employee_positions, -> { where(employee_positions: { discarded_at: nil }) }

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false },
            length: { maximum: 100 }, if: -> { name_changed? }

  # ============================= Callbacks ============================================================================
  after_discard do
    employee_positions.update_all(delegate_series_id: :nil)
  end
end
