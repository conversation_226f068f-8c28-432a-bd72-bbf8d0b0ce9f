class Form < ApplicationRecord
  include PgSearch::Model
  has_one_attached :file
  enum file_type: { form: 1, by_law: 2, useful_links: 3, useful_phone_numbers: 4, salary_and_overtime_charts: 5, ems_stations: 6, unit_charts: 7, mci_codes: 8 }

  # To import data in this model I have attached sample CSV link down here. Please check and follow the syntax in the csv and then fill out with your data. Then ran the rake task
  # S3 URL for CSV: s3://fuse-production-assets/NameAndNumbersWithHeadings.csv
  # rake command: #### bundle exec rake "add_number_or_links_with_heading[local2507, NameAndNumbersWithHeadings.csv, false, forms]"

  pg_search_scope :search_by_form_name, lambda { |query|
    {
      against: %w[name description],
      query: query,
      using: { tsearch: { prefix: true }}
    }
  }

end
