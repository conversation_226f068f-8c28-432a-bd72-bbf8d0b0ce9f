# frozen_string_literal: true

class DelegateAssignment < ApplicationRecord
  include Validation

  # ============================= Associations =========================================================================
  belongs_to :employee
  belongs_to :office, optional: true
  belongs_to :delegate_employee, class_name: 'Employee', foreign_key: 'delegate_employee_id'

  # ============================= Validations ==========================================================================
  validate :validate_date_range
  validates :start_date, presence: true, if: :end_date?

  # ============================= Scopes ===============================================================================
  scope :order_by_delegate_employee_id, -> { reorder('delegate_assignments.delegate_employee_id ASC') }

end
