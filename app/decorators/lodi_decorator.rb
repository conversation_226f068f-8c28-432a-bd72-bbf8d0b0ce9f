# frozen_string_literal: true

class LodiDecorator
  def initialize(lodi)
    @lodi = lodi
  end

  # rubocop:disable Lint/UselessAssignment
  def total_hours_used
    same_lodis.inject(0) do |sum, lodi|
      date = lodi.return_date || Date.today
      sum += (date - lodi.incident_date) * 24
    end.round(2)
  end
  # rubocop:enable Lint/UselessAssignment

  def totality
    @totality ||= Totality.kept.where(totalable_type: 'lodi', employee_id: employee_id).first
  end

  private

  attr_reader :lodi

  def employee_id
    lodi.employee_id
  end

  def same_lodis
    @same_lodis ||= Lodi.kept.where(employee_id: employee_id)
  end
end
