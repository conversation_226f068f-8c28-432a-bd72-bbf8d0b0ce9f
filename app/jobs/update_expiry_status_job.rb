# frozen_string_literal: true

class UpdateExpiryStatusJob < ApplicationJob
  queue_as :default

  def perform(employee_employment_status, only_end_date_updated)
    @employee = employee_employment_status.employee
    @status_name = employee_employment_status.employment_status.name
    @expiry_status_names = current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_III')
    expiry_relationships = current_account.saas_json.dig('schema', 'employee_benefits', 'expire_relationship_types')
    @employee_coverages = expiry_relationships ? @employee.benefit_coverages.where(relationship: expiry_relationships) : @employee.benefit_coverages
    @custom_expiration_logics = current_account.saas_json.dig('schema', 'employee_benefits', 'custom_expiration_logics')
    section_v_expiry_status_names = current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_V')&.first
    retire_benefit_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'retire_benefit_coverage_expire_age')
    if @custom_expiration_logics
      eligible_status_auto_update(employee_employment_status)
    end

    return unless (employee_employment_status.employment_status.present? && employee_employment_status.start_date.present?) && (check_status_names_include(employee_employment_status, @expiry_status_names) ||
      check_status_names_include(employee_employment_status, section_v_expiry_status_names) || retire_benefit_coverage_expire_age.present?)

    ineligible_status_auto_update(employee_employment_status, only_end_date_updated)

    if section_v_expiry_status_names.present? && section_v_expiry_status_names.include?(employee_employment_status.employment_status.name)
      expiry_benefit_names = current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_V')&.second
      expiry_benefit_names = expiry_benefit_names.map(&:downcase)
      employee_benefits = @employee.employee_benefits.joins(:benefit).where('employee_benefits.end_date is null and lower(benefits.name) in (?)', expiry_benefit_names)
      employee_benefits.where('employee_benefits.start_date is null').update_all(start_date: employee_employment_status.start_date)
      employee_benefits.update_all(end_date: employee_employment_status.start_date)
      @employee.benefit_coverages.where('expires_at is null and employee_benefit_id in (?)', employee_benefits.pluck[:id]).update_all(expires_at: employee_employment_status.start_date) if employee_benefits.present?
    end
    return unless retire_benefit_coverage_expire_age.present? && @status_name.downcase == 'retired'

    @employee.benefit_coverages.each do |benefit_coverage|
      now = Time.now
      age = now.year - benefit_coverage.birthday.to_time.year - (benefit_coverage.birthday.to_time.change(:year => now.year) > now ? 1 : 0) if benefit_coverage.birthday
      if age.present? && retire_benefit_coverage_expire_age.present? && age >= retire_benefit_coverage_expire_age && %w[child step_child].include?(benefit_coverage.relationship)
        expires_at = benefit_coverage.birthday + retire_benefit_coverage_expire_age.year
        benefit_coverage.update_columns(expires_at: expires_at)
      end
    end
  end

  def check_status_names_include(employee_employment_status, employment_status_names)
    employment_status_names.present? && employment_status_names.map(&:downcase).include?(employee_employment_status.employment_status.name.downcase)
  end

  def check_eligible_status(status_name, statuses = EmployeeEmploymentStatus::CVS_ELIGIBLE_STATUSES)
    statuses&.include?(status_name&.delete(' ')&.downcase)
  end

  def eligible_status_auto_update(employee_employment_status)
    eligible_status_included = check_eligible_status(@status_name&.downcase)
    auto_update_eligible_start_dates = eligible_status_included && employee_employment_status.discarded_at.nil? && !employee_employment_status.start_date&.future?
    last_two_statuses = @employee.employee_employment_statuses.where('id != ?', employee_employment_status.id).order(updated_at: :desc).limit(2)&.map(&:name)
    last_two_active_statuses = @employee.employee_employment_statuses.where('(end_date is null OR end_date > ?) AND id != ?', Date.today, employee_employment_status.id).order(updated_at: :desc).limit(2)&.map(&:name)
    cvs_active_statuses = %w[agency cobra union]
    eligible_to_eligible = (check_eligible_status(last_two_statuses&.first, cvs_active_statuses) || (@expiry_status_names&.exclude?(last_two_statuses&.first) && check_eligible_status(last_two_statuses&.last, cvs_active_statuses)))
    cvs_active_benefits_update = if (cvs_active_statuses & (last_two_active_statuses&.map(&:downcase) || [])).any? && check_status_names_include(employee_employment_status, cvs_active_statuses)
                                   true
                                   elsif eligible_status_included && eligible_to_eligible && check_eligible_status(@status_name, cvs_active_statuses)
                                   @employee.employee_benefits.update_all(end_date: employee_employment_status.end_date)
                                   @employee_coverages.update_all(expires_at: employee_employment_status.end_date)
                                   true
                                 else
                                   false
                                 end
    if auto_update_eligible_start_dates && !cvs_active_benefits_update
      @employee.employee_benefits.update_all(start_date: employee_employment_status.start_date, end_date: employee_employment_status.end_date)
      @employee_coverages.update_all(expires_at: employee_employment_status.end_date)
    end
  end

  def ineligible_status_auto_update(employee_employment_status, only_end_date_updated)
    deceased_dependents_expire_on_next_month = current_account.saas_json.dig('schema', 'employee_benefits', 'deceased_dependents_expire_on_next_month')
    unexpired_employee_benefits = @employee.employee_benefits.where('end_date is null')
    if @expiry_status_names.include?(@status_name) && !only_end_date_updated
      last_two_statuses = @employee.employee_employment_statuses.order(updated_at: :desc).limit(2)&.map(&:name)
      valid_excluded_status = (last_two_statuses & @expiry_status_names).one? && @custom_expiration_logics
      if @custom_expiration_logics && valid_excluded_status
        @employee_coverages.update_all(expires_at: employee_employment_status.start_date)
        @employee.employee_benefits.update_all(end_date: employee_employment_status.start_date)
      else
        coverage_expire_date = deceased_dependents_expire_on_next_month && @status_name&.downcase == 'deceased' ? employee_employment_status.start_date.at_beginning_of_month.next_month : employee_employment_status.start_date
        @employee_coverages.where(expires_at: nil).update_all(expires_at: coverage_expire_date)
        @employee_coverages.where(student: true).update_all(student: false) if current_account.saas_json.dig('schema', 'benefit_coverages', 'update_student_after_expiration')
        if current_account.saas_json.dig('schema', 'employee_benefits', 'auto_expire_benefits')
          unexpired_employee_benefits.where(start_date: nil).update_all(start_date: employee_employment_status.start_date) unless @custom_expiration_logics
          unexpired_employee_benefits.update_all(end_date: coverage_expire_date ? coverage_expire_date : employee_employment_status.start_date)
        end
      end
    end
  end
end
