# frozen_string_literal: true

class ReportGeneratorJob < ApplicationJob
  queue_as :report

  def perform(params, user_klass, user_id, subdomain = Apartment::Tenant.current)
    @params = params
    @user_id = user_id
    @user_klass = user_klass
    @origin_subdomain = subdomain

    begin
      if params['template_name'] == 'demand_for_arbitration'
        employee_grievance = EmployeeGrievance.find(params['employee_grievance_id']) if params['employee_grievance_id'].present?
        button_visible_on = employee_grievance&.employee_grievance_steps&.find_by(step: 'step_1')&.show_button_on
        unless button_visible_on && button_visible_on <= Date.today
          Rails.logger.info "This Report can only be generated after 60 working days."
          return ActionCable.server.broadcast admin_stream,
                                       JSON.parse(report_json).merge!(status_code: 500, message: 'This Report can only be generated after 60 working days.')
        end
      end
      set_paper_trail_whodunnit
      report_generator.generate

      ActionCable.server.broadcast admin_stream, JSON.parse(report_json).merge!(status_code: 200)
    rescue StandardError => e
      CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      Rails.logger.info "=============== Error in report generation ============ \n : #{e.message}"
      ActionCable.server.broadcast admin_stream,
                                   JSON.parse(report_json).merge!(status_code: 500, message: 'The report could not be generated')
    end
  end

  private

  attr_reader :params, :user_id, :user_klass, :origin_subdomain

  def report_generator
    report_class_name = %w[janus notification_analytics].include?(params[:report_type]) ? params[:report_type].titleize.delete(' ') : params[:report_type].classify

    @report_generator ||= "ReportGenerator::#{report_class_name}Report".constantize.send(:new, params)
  end

  def admin_stream
    "#{origin_subdomain}_notification_user_#{user_klass.underscore}_#{user_id}"
  end

  def report_json
    Api::ReportSerializer.new(report_generator.report).serialized_json
  end

  def set_paper_trail_whodunnit
    PaperTrail.request.whodunnit = @user_id
  end
end
