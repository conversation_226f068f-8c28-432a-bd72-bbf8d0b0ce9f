class AddDependentToAllBenefitsJob < ApplicationJob
  queue_as :default

  def perform (resource_params, benefit_coverage, action, first_name = nil, last_name = nil)
    set_expiration = current_account.saas_json.dig('schema', 'benefit_coverages', 'dependent_all_benefits_expiration') == true
    except_array = [:add_dependent_to_all_benefits]
    except_array << [:expires_at, :effective_date] if set_expiration == true
    employee = benefit_coverage.employee
    benefit_id = benefit_coverage.employee_benefit.benefit_id
    employee_benefits = employee.employee_benefits.where.not(benefit_id: benefit_id)
    if action == "create"
      employee_benefits.each do |benefit|
        benefit_coverage = employee.benefit_coverages.new(resource_params.except(*except_array.flatten))
        benefit_coverage.employee_benefit_id = benefit.id
        benefit_coverage.save!
      end
    elsif action == "update"
      benefit_coverages = employee.benefit_coverages.where("(first_name = ? and last_name = ?)", first_name, last_name)
      except_array << :employee_benefit_id
      benefit_coverages.each { |x| x.update(resource_params.except(*except_array.flatten)) }
    end
  end
end
