# frozen_string_literal: true

class ApplicationJob < ActiveJob::Base
  # https://github.com/flyerhzm/bullet#work-with-activejob
  include Bullet::ActiveJob if Rails.env.development? || ENV['APP_ENV'] == 'development'

  def ftp_file_upload(generator, account = nil, benefit = nil)
    time = Time.now.in_time_zone('Chennai')
    begin
      domain = account.present? ? account : 'btoba'
      if %w[vision_screening gvs uhc cps bcbs cvs aso].include?(generator)
        need_pluralization = %w[cps gvs bcbs cvs].include?(generator)
        file_name = "FileGenerator::#{need_pluralization ? generator.classify.pluralize : generator.classify}FileGenerator".constantize.new.generate(account, benefit) # It will return the filename
      elsif %w[davis_vision].include?(generator)
        if account == 'btoba'
          file_name = "FileGenerator::#{generator.classify}FileGenerator".constantize.new.generate(account, benefit) # It will return the filename
        elsif account == 'cobanc'
          file_name = 'FileGenerator::CobancDavisVisionFileGenerator'.constantize.new.generate(account, benefit) # It will return the filename
        end
      else
        file_name = "FileGenerator::#{generator.classify}FileGenerator".constantize.new.generate # It will return the filename
      end
      if file_name.present?
        data = Rails.application.credentials.dig(:"#{domain}", :ftp_servers)[:"#{generator}"]
        options = {}
        if generator == 'gvs'
          private_file_content = get_private_file_data_from_s3(data[:file_name])
          options[:key_data] = [private_file_content]
        else
          options[:password] = data[:password]
        end
        Timeout.timeout(300) do
          if ENV['CAN_SEND_FTP_FILES'] == 'true'
            Net::SFTP.start(data[:domain], data[:username], **options) do |ftp|
              ftp.upload!("#{Rails.root}/#{file_name}", "#{data[:upload_path]}/#{file_name}")
              ftp.channel.remote_closed!
            end
          end
        end
      end
    rescue Exception => e
      error_line = e.backtrace.select { |x| x.match("fuse_rails/app/services/file_generator/#{generator}_file_generator.rb") }
      error_line = e.backtrace.select { |x| x.match('fuse_rails/app/jobs/application_job.rb') } if error_line.blank?

      error = [e.class.to_s, error_line.first.to_s]
      CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      FileUploadMailer.send_file_upload_fail_mail(generator, error, time, domain, file_name).deliver_later
    end
  end

  # Automatically retry jobs that encountered a deadlock
  # retry_on ActiveRecord::Deadlocked

  # Most jobs are safe to ignore if the underlying records are no longer available
  # discard_on ActiveJob::DeserializationError
  def current_account
    Account.find_by(subdomain: Apartment::Tenant.current)
  end

  def get_private_file_data_from_s3(key)
    # s3 = Aws::S3::Client.new(
    #   region: ENV['AWS_S3_REGION'],
    #   access_key_id: ENV['AWS_ACCESS_KEY_ID'],
    #   secret_access_key: ENV['AWS_SECRET_ACCESS_KEY']
    # )
    # response = s3.get_object(bucket: ENV['AWS_GVS_KEY_S3_BUCKET'], key: key)
    # response.body.read
  end
end
