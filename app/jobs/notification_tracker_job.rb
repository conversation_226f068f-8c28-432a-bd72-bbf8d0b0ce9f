# frozen_string_literal: true

class NotificationTrackerJob < ActiveJob::Base

  queue_as :default

  def perform(events)
    events.each do |event|
      Apartment::Tenant.switch!(event['subdomain'])
      notification_tracker = NotificationTracker.find_by(message_id: event['message_id'])
      next unless notification_tracker

      update_notification_tracker(notification_tracker, event)

      notification_tracker.update(delivered: true)
    end
  end

  private

  def update_notification_tracker(notification_tracker, event)
    event_type = event['event_type']

    case event_type.downcase
    when 'processed'
      notification_tracker.update(sent: true)
    when 'delivered'
      notification_tracker.update(delivered: true)
    when 'open'
      if notification_tracker.delivered? && ((event["timestamp"].to_i - notification_tracker.updated_at.to_i) > 3)
        notification_tracker.update(opened: true)
      end
    when 'click'
      notification_tracker.update(clicked: true)
    when 'bounce'
      notification_tracker.update(bounced: true)
    when 'dropped', 'reject'
      notification_tracker.update(rejected: true)
    else
      Rails.logger.info "Unhandled event type: #{event_type}"
    end
  end
end
