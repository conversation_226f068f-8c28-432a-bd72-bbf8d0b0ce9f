class ExpireAllBenefitsJob < ApplicationJob
  queue_as :default

  def perform(current_benefit)
    employee = current_benefit.employee
    employee_benefits = employee.employee_benefits
    benefit_coverages = employee.benefit_coverages
    employee_benefits.where("start_date is NULL").update_all(start_date: current_benefit.start_date)
    employee_benefits.where("end_date is NULL").update_all(end_date: current_benefit.end_date)
    benefit_coverages.where("expires_at is NULL").update_all(expires_at: current_benefit.end_date)
  end
end

