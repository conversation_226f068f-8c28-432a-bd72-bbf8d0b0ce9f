# frozen_string_literal: true

module MultiTenantSupport
  extend ActiveSupport::Concern

  module ClassMethods
    def execute(job_data)
      # Perform the active job in the tenant data sent from job data.
      Apartment::Tenant.switch(job_data['tenant']) do
        super
      end
    end
  end

  # Attach the current tenant(schema) as a job param to perform job on the specific schema
  def serialize
    super.merge('tenant' => Apartment::Tenant.current)
  end
end
