class OpticalBenefitCoverageUnExpirationJob < ApplicationJob
  queue_as :default
  require 'net/sftp'
  require 'yaml'

  def perform(subdomain)
    if subdomain == 'btoba'
      btoba_optical_expiration
    elsif subdomain == 'cobanc'
      cobanc_optical_expiration
    end
  end

  private

  def cobanc_optical_expiration
    Apartment::Tenant.switch!('cobanc')
    employees = Employee.kept.includes(:benefit_coverages, :benefit_disbursements, :employee_benefits, :employee_employment_statuses).where('employee_employment_statuses.employment_status_id != ?
                         and (employee_benefits.end_date is NULL or employee_benefits.end_date > ?) and (employee_benefits.serviced_expiration < ?)
                         or (benefit_coverages.serviced_expiration < ?)', EmploymentStatus.find_by('lower(name) = ?', 'deceased').id, Date.today, Date.today, Date.today)
                        .references(:benefit_coverages, :benefit_disbursements, :employee_benefits, :employee_employment_statuses)
    employee_ids = employees.pluck(:id)
    coverages = BenefitCoverage.kept.where('serviced_expiration < ? and expires_at is NULL', Date.today)
    coverages_ids = coverages.pluck(:employee_id)
    common_ids = employee_ids & coverages_ids
    coverages.where(employee_id: common_ids).update_all(serviced_expiration: nil, expiration_type: 0) if common_ids.present?
    employees.each do |employee|
      employee_benefits = employee.employee_benefits.where('employee_benefits.benefit_id = ? and employee_benefits.serviced_expiration < ?', Benefit.find_by('lower(name) = ?', 'optical').id, Date.today)
      employee_benefits.update(serviced_expiration: nil, expiration_type: 0) if employee_benefits.present?
    end
  end

  def btoba_optical_expiration
    Apartment::Tenant.switch!('btoba')
    employees = Employee.kept.includes(:benefit_coverages, :benefit_disbursements, :employee_benefits, :employee_employment_statuses).where('employee_employment_statuses.employment_status_id != ?
                         and (employee_benefits.end_date is NULL or employee_benefits.end_date > ?) and (employee_benefits.serviced_expiration < ?)
                         or (benefit_coverages.serviced_expiration < ?)', EmploymentStatus.find_by('lower(name) = ?', 'deceased').id, Date.today, (Date.today - 365), (Date.today - 365))
                        .references(:benefit_coverages, :benefit_disbursements, :employee_benefits, :employee_employment_statuses)
    employee_ids = employees.pluck(:id)
    coverages = BenefitCoverage.kept.where('serviced_expiration < ? and expires_at is NULL ', (Date.today - 365))
    coverages_ids = coverages.pluck(:employee_id)
    common_ids = employee_ids & coverages_ids
    coverages.where(employee_id: common_ids).update_all(serviced_expiration: nil, expiration_type: 0) if common_ids.present?
    employees.each do |employee|
      employee_benefits = employee.employee_benefits.where('employee_benefits.benefit_id = ? and employee_benefits.serviced_expiration < ?', Benefit.find_by('lower(name) = ?', 'optical').id, (Date.today - 365))
      employee_benefits.update(serviced_expiration: nil, expiration_type: 0) if employee_benefits.present?
    end
  end
end