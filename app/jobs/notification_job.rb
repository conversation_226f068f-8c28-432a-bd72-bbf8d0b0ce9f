# frozen_string_literal: true

class NotificationJob < ApplicationJob
  queue_as :notification

  def perform(notification_id, scheduled_domain = nil)
    unless ENV['ENABLE_NOTIFICATION'] == 'yes'
      Rails.logger.info 'Notification sending is allowed only in the Production environment'
      return false
    end

    notification = Notification.find(notification_id)
    if notification.is_scheduled
      scheduled_tenant = scheduled_domain
    else
      origin_tenant = Apartment::Tenant.current
      target_tenant = notification.subdomain
    end
    current_account = Account.find_by(subdomain: target_tenant.presence || origin_tenant.presence || scheduled_tenant)

    Apartment::Tenant.switch!(target_tenant) if target_tenant.present?
    Apartment::Tenant.switch!(scheduled_tenant) if scheduled_tenant.present?

    employee_ids = Generators::EmployeesQueryGenerator.generate(ActionController::Parameters.new(notification.filters),
                                                                current_account, 'notification').ids.uniq
    default_receivers = current_account.saas_json.dig('ui', 'notification', 'default_notification_receivers')


    email_employee_ids = employee_ids - Employee.where(email_opt_out: true).ids
    sms_employee_ids = employee_ids - Employee.where(sms_opt_out: true).ids

    if default_receivers.present?
      default_receiver_ids = []
      default_receivers.each do |default_receiver|
        default_receiver = default_receiver.split(' ')
        default_receiver_ids << Employee.find_by(first_name: default_receiver.first, last_name: default_receiver.last)&.id
      end
      email_employee_ids += default_receiver_ids
      sms_employee_ids += default_receiver_ids
    end
    invalid_emails = []
    if notification.email
      bounced_emails = BounceAndComplaint.pluck(:email)

      contacts = if notification.email_to == 'work'
                   Contact.kept.where(employee_id: email_employee_ids, contact_for: 'work', contact_type: 'email')
                 elsif notification.email_to == 'personal'
                   Contact.kept.where(employee_id: email_employee_ids, contact_for: 'personal', contact_type: 'email')
                 elsif notification.email_to == 'both'
                   Contact.kept.where(employee_id: email_employee_ids, contact_type: 'email')
                 end
      @default_email_needed = contacts.none? { |contact| contact.value == '<EMAIL>' }

      contacts.each do |contact|
        next unless contact.value.present?

        is_bounced = bounced_emails.any?(contact.value)
        unless contact.value =~ URI::MailTo::EMAIL_REGEXP
          invalid_emails.push(contact.value)
          next
        end
        Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?
        notification_tracker = fetch_tracker(contact.employee_id, notification.id, origin_tenant, target_tenant, 'email', is_bounced)
        NotificationsMailer.notifications(notification.id, contact.employee_id, contact.value).deliver_later unless is_bounced
      end
      if invalid_emails.present?
        InvalidEmailFileJob.perform_later(Apartment::Tenant.current, invalid_emails, time = Time.now.in_time_zone('Chennai'))
      end
      Apartment::Tenant.switch!(target_tenant) if target_tenant.present?
    end

    if notification.sms
      contacts = if notification.sms_to == 'work'
                   Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'work', contact_type: 'phone')
                 elsif notification.sms_to == 'personal'
                   Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'personal', contact_type: 'phone')
                 elsif notification.sms_to == 'home'
                   Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'home', contact_type: 'phone')
                 elsif notification.sms_to == 'all'
                   Contact.kept.where(employee_id: sms_employee_ids, contact_type: 'phone')
                 else
                   Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'personal', contact_type: 'phone')
                 end

      @default_sms_needed = contacts.none? { |contact| contact.value&.gsub(/[\s\-\(\)]/, '') == '7184732135' }

      contacts.each_with_index do |contact, index|
        next unless contact.value.present?

        Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?

        notification_tracker = fetch_tracker(contact.employee_id, notification.id, origin_tenant, target_tenant, 'sms')
        phone_number = contact.value.gsub(/[' ']|[-]|[()]/, '')

        sleep(60.seconds) if (index % 200).zero?
        ## Handling the sleep for the 60 seconds, because of the rate limiting in the carrier's end.
        ## Todo: We have handled this logic as a temporary solution. We need to find a way to resolve this issue.
        SendSmsNotification.new("+1#{phone_number}", notification, notification_tracker.id).send_sms
      end
    end

    if notification.push
      employee_ids.each do |employee_id|
        Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?

        notification_tracker = fetch_tracker(employee_id, notification.id, origin_tenant, target_tenant, 'push')
        PushNotificationService.new.send_push_notification(notification_tracker.employee, notification, notification.push_message)
      end
    end
    default_notification_send(notification)
    notification.update_columns(status: 'completed')
  end

  def fetch_tracker(employee_id, notification_id, origin_tenant, target_tenant, type, is_bounced = false)
    notification_tracker = NotificationTracker.where(employee_id: employee_id, notification_id: notification_id).first_or_create!
    if type == 'sms'
      notification_tracker.update(notification_type: 'sms', sms: true)
    elsif type == 'push'
      notification_tracker.update(notification_type: 'push', push: true)
    else
      notification_tracker.update(notification_type: 'email', email: true, bounced: is_bounced)
    end

    notification_tracker
  end

  def default_notification_send(notification)
    default_client_email = '<EMAIL>'
    default_client_phone = '(718) 473 - 2135'
    default_client_phone = default_client_phone.gsub(/[' ']|[-]|[()]/, '')

    if notification.email && @default_email_needed
      employee = Contact.kept.where(value: default_client_email)&.first&.employee
      NotificationsMailer.notifications(notification.id, employee.id, default_client_email).deliver_later if employee.present?
    end

    return unless notification.sms && @default_sms_needed

    SendSmsNotification.new("+1#{default_client_phone}", notification, nil).send_sms
  end
end
