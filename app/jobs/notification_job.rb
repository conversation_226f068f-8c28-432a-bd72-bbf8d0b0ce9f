# frozen_string_literal: true

class NotificationJob < ApplicationJob
  queue_as :notification

  def perform(notification_id, scheduled_domain = nil)
    # unless ENV['ENABLE_NOTIFICATION'] == 'yes'
    #   Rails.logger.info 'Notification sending is allowed only in the Production environment'
    #   return false
    # end

    notification = Notification.find(notification_id)
    if notification.is_scheduled
      scheduled_tenant = scheduled_domain
    else
      origin_tenant = Apartment::Tenant.current
      target_tenant = notification.subdomain
    end
    current_account = Account.find_by(subdomain: target_tenant.presence || origin_tenant.presence || scheduled_tenant)

    Apartment::Tenant.switch!(target_tenant) if target_tenant.present?
    Apartment::Tenant.switch!(scheduled_tenant) if scheduled_tenant.present?

    employee_ids = Generators::EmployeesQueryGenerator.generate(ActionController::Parameters.new(notification.filters),
                                                                current_account, 'notification').ids.uniq
    default_receivers = current_account&.saas_json&.dig('ui', 'notification', 'default_notification_receivers')


    email_employee_ids = employee_ids - Employee.where(email_opt_out: true).ids
    sms_employee_ids = employee_ids - Employee.where(sms_opt_out: true).ids

    if default_receivers.present? && ENV['ENABLE_NOTIFICATION'] == 'yes' && ENV['APP_ENV'] == 'production'
      default_receiver_ids = []
      default_receivers.each do |default_receiver|
        default_receiver = default_receiver.split(' ')
        if current_account.subdomain == 'nyccoba'
          default_receiver_ids << get_nyccoba_default_receiver(default_receiver)
        else
          default_receiver_ids << Employee.find_by('lower(first_name) = ? and lower(last_name) = ?', default_receiver.first&.downcase, default_receiver.last&.downcase)&.id
        end
      end
      email_employee_ids += default_receiver_ids
      sms_employee_ids += default_receiver_ids
    end

    send_email_notification(notification, origin_tenant, target_tenant, email_employee_ids) if notification.email

    Apartment::Tenant.switch!(target_tenant) if target_tenant.present?

    send_sms_notification(notification, origin_tenant, target_tenant, sms_employee_ids) if notification.sms

    send_push_notification(notification, origin_tenant, target_tenant, employee_ids) if notification.push

    default_notification_send(notification) if ENV['ENABLE_NOTIFICATION'] == 'yes' && ENV['APP_ENV'] == 'production'
    notification.update_columns(status: 'completed')
  rescue StandardError => e
    Rails.logger.info e.message
    CustomAppsignalError.send_error(e, Apartment::Tenant.current)
  end

  def fetch_tracker(employee_id, notification_id, type, is_bounced=false)
    notification_tracker = NotificationTracker.where(employee_id: employee_id, notification_id: notification_id).first_or_create!
    if type == 'sms'
      notification_tracker.update(notification_type: 'sms', sms: true)
    elsif type == 'push'
      notification_tracker.update(notification_type: 'push', push: true)
    else
      is_pro_or_dev = ENV['APP_ENV'] == 'production' ? "production-#{employee_id}-#{notification_id}" : "development-#{employee_id}-#{notification_id}"
      notification_tracker.update(notification_type: 'email', email: true, bounced: is_bounced, message_id: is_pro_or_dev)
    end

    notification_tracker
  end

  # rubocop:disable Metrics/AbcSize, Metrics/PerceivedComplexity, Lint/DuplicateBranch
  def send_email_notification(notification, origin_tenant, target_tenant, email_employee_ids)
    invalid_emails = []
    bounced_emails = BounceAndComplaint.pluck(:email)

    email_to = notification.email_to
    contacts = case email_to
               when 'work'
                 Contact.kept.where(employee_id: email_employee_ids, contact_for: 'work', contact_type: 'email')
               when 'personal'
                 Contact.kept.where(employee_id: email_employee_ids, contact_for: 'personal', contact_type: 'email')
               when 'both'
                 Contact.kept.where(employee_id: email_employee_ids, contact_type: 'email')
               else
                 Contact.kept.where(employee_id: email_employee_ids, contact_for: 'personal', contact_type: 'email')
               end

    @default_email_needed = contacts.none? { |contact| contact.value == '<EMAIL>' }

    if notification.email_template_id.present? && current_account.saas_json.dig('schema', 'notifications', 'sendgrid_templates') == true
      recipients = []
      contacts.each do |contact|
        next unless contact.value.present?

        is_bounced = bounced_emails.any?(contact.value)
        unless contact.value =~ URI::MailTo::EMAIL_REGEXP
          invalid_emails.push(contact.value)
          next
        end
        Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?
        fetch_tracker(contact.employee_id, notification.id, 'email', is_bounced)
        recipients << { email: contact.value, employee_id: contact.employee_id } unless is_bounced
      rescue StandardError => e
          Rails.logger.error("Error while sending notification for employee #{contact.employee_id}: #{e.message}")
          CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      end

      sendgrid_service = SendgridService.new
      sendgrid_service.send_bulk_email(
        notification_id: notification.id,
        recipients: recipients,
        dynamic_contents: notification.email_content,
        template_id: notification.email_template_id,
        current_tenant: Apartment::Tenant.current
      )
    else
      ## Todo: remove ses logic after sendgrid integration
      contacts.each do |contact|
        next unless contact.value.present?

        is_bounced = bounced_emails.any?(contact.value)
        unless contact.value =~ URI::MailTo::EMAIL_REGEXP
          invalid_emails.push(contact.value)
          next
        end
        Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?
        fetch_tracker(contact.employee_id, notification.id, 'email', is_bounced)
        NotificationsMailer.notifications(notification.id, contact.employee_id, contact.value).deliver_later unless is_bounced
      rescue StandardError => e
          Rails.logger.error("Error while sending notification for employee #{contact.employee_id}: #{e.message}")
          CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      end
    end
    return unless invalid_emails.present?

    InvalidEmailFileJob.perform_later(Apartment::Tenant.current, invalid_emails, Time.now.in_time_zone('Chennai'))
  rescue StandardError => e
    CustomAppsignalError.send_error(e, Apartment::Tenant.current)
  end

  def send_sms_notification(notification, origin_tenant, target_tenant, sms_employee_ids)
    sms_to = notification.sms_to
    contacts = case sms_to
               when 'work'
                 Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'work', contact_type: 'phone')
               when 'personal'
                 Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'personal', contact_type: 'phone')
               when 'home'
                 Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'home', contact_type: 'phone')
               when 'all'
                 Contact.kept.where(employee_id: sms_employee_ids, contact_type: 'phone')
               else
                 Contact.kept.where(employee_id: sms_employee_ids, contact_for: 'personal', contact_type: 'phone')
               end
    @default_sms_needed = contacts.none? { |contact| contact.value&.gsub(/[ ()-]/, '') == '7184732135' }

    contacts.each_with_index do |contact, index|
      next unless contact.value.present?

      Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?

      notification_tracker = fetch_tracker(contact.employee_id, notification.id, 'sms')
      phone_number = contact.value&.gsub(/[ ()-]/, '')

      sleep(60.seconds) if (index % 200).zero?
      ## Handling the sleep for the 60 seconds, because of the rate limiting in the carrier's end.
      ## Todo: We have handled this logic as a temporary solution. We need to find a way to resolve this issue.
      SendSmsNotification.new("+1#{phone_number}", notification, notification_tracker.id).send_sms
    rescue StandardError => e
      Rails.logger.error("Error while sending notification for employee #{contact.employee_id}: #{e.message}")
      CustomAppsignalError.send_error(e, Apartment::Tenant.current)
    end
  rescue StandardError => e
    CustomAppsignalError.send_error(e, Apartment::Tenant.current)
  end

  def send_push_notification(notification, origin_tenant, target_tenant, employee_ids)
    employee_ids.each do |employee_id|
      Apartment::Tenant.switch!(origin_tenant) if target_tenant.present?

      notification_tracker = fetch_tracker(employee_id, notification.id, 'push')
      PushNotificationService.new.send_push_notification(notification_tracker.employee, notification, notification.push_message)
    rescue StandardError => e
      Rails.logger.error("Error while sending notification for employee #{employee_id}: #{e.message}")
      CustomAppsignalError.send_error(e, Apartment::Tenant.current)
    end
  end

  def default_notification_send(notification)
    default_client_email = '<EMAIL>'
    default_client_phone = '(718) 473 - 2135'
    default_client_phone = default_client_phone.gsub(/[ ()-]/, '')

    if notification.email && @default_email_needed
      employee = Contact.kept.where(value: default_client_email)&.first&.employee
      NotificationsMailer.notifications(notification.id, employee.id, default_client_email).deliver_later if employee.present?
    end

    return unless notification.sms && @default_sms_needed

    SendSmsNotification.new("+1#{default_client_phone}", notification, nil).send_sms
  end

  def get_nyccoba_default_receiver(default_receiver)
    if default_receiver.length >= 3
      first_name = default_receiver[0]
      last_name = default_receiver[1..].join(' ')
    else
      first_name = default_receiver.first
      last_name = default_receiver.last
    end
    Employee.find_by('lower(first_name) = ? AND lower(last_name) = ?', first_name&.downcase, last_name&.downcase)&.id
  end

  # rubocop:enable Metrics/AbcSize, Metrics/PerceivedComplexity, Lint/DuplicateBranch
end
