# frozen_string_literal: true

module Api
  class AccountsController < Api::BaseController
    include Api::ApiRescues
    before_action :find_account, only: %w[show app_version]
    before_action :set_app_versions, only: :app_version
    skip_before_action :authenticate_and_set_user, only: :app_version
    skip_load_and_authorize_resource only: :app_version
    before_action :doorkeeper_authorize!, only: :show, if: -> { @current_employee.present? }

    def show
      options = @account.subdomain == 'sssa' && @current_employee.present? ? { options: { staff_member: @current_employee&.staff_member } } : {}
      render_json(data: @account, **options)
    end

    def app_version
      if @app_version.present? && @force_update_version.present? && @app_version < @force_update_version # Force update needed
        render_update_status('force', 'New version available. Please update your app to continue')
      elsif @app_version.present? && @update_version.present? && @app_version < @update_version # Normal update needed
        render_update_status('update', 'New version available. Please update your app')
      else # No update needed
        render_update_status('none')
      end
    end

    private

    def set_app_versions
      if @account.present? && @account.app_version_details.present?
        @app_version = Gem::Version.new(params[:app_version]) if params[:app_version].present?
        app_version_detail = @account.app_version_details

        if params[:os_type].present?
          if params[:os_type] == 'android'
            @update_version = Gem::Version.new(app_version_detail['android_update_version']) if app_version_detail['android_update_version'].present?
            @force_update_version = Gem::Version.new(app_version_detail['android_force_update_version']) if app_version_detail['android_force_update_version'].present?
          elsif params[:os_type] == 'ios'
            @update_version = Gem::Version.new(app_version_detail['ios_update_version']) if app_version_detail['ios_update_version'].present?
            @force_update_version = Gem::Version.new(app_version_detail['ios_force_update_version']) if app_version_detail['ios_force_update_version'].present?
          end
        else
          render_error("OS type can't be blank")
        end
      end
    end

    def render_update_status(status, message = nil)
      render_success(data: {update_status: status}, message: message)
    end

    def find_account
      @account = current_account
    end
  end
end
