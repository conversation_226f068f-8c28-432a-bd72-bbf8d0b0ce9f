# frozen_string_literal: true

module Api
  class ReportsController < Api::BaseController
    skip_load_and_authorize_resource
    authorize_resource class: false
    before_action :validate_report_params, only: :create
    before_action :validate_report_column, only: :create

    def create
      # Logic to include all the report columns in the result
      if resource_params['report_type'] == 'single_employee' && resource_params['columns'] == ['0']
        current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        resource_params['columns'] = current_account.saas_json['ui'].dig('reports', 'single_employee', 'columns').map(&:singularize)
      end

      tenant = request.subdomain.split('.').first

      ReportGeneratorJob.perform_later(resource_params, @current_user.class.to_s, @current_user.id, tenant)
      render_success
    end

    private

    def resource_params
      params.require(:report).permit!
    end

    def current_ability
      @current_ability ||= Ability.new(@current_user, params: { report_type: params[:report][:report_type] })
    end

    def validate_report_params
      is_param_missing = false
      %w[report_type report_format].each do |required_param|
        is_param_missing = true unless params[:report][required_param.to_sym]
      end
      return render_error('Required param is missing') if is_param_missing

      render_error('Report type is not supported') unless Report::REPORT_TYPES.include?(params[:report][:report_type])
      validate_mailing_label if params[:report][:mailing_label].present?
    end

    def validate_mailing_label
      if Report::MAILING_LABEL_TYPES.exclude?(params[:report][:mailing_label])
        render_error('Mailing label is not supported')
      else
        return if params[:report][:report_format] == 'avery'

        render_error('Report type for mailing label is invalid')
      end
    end

    def validate_report_column
      if params[:report][:report_type] == 'single_employee' && %w[pdf xls].include?(params[:report][:report_format]) && !params[:report][:columns].present?
        render_error('Report column is not present')
      end
    end
  end
end
