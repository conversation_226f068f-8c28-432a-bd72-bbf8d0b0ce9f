# frozen_string_literal: true

module Api
  module Settings
    class RanksController < Api::BaseController
      include Api::ApiRescues
      before_action :set_rank, only: %w[update destroy]

      def index
        # In dropdown, 'per_page' option is used to get 'N' number of results.
        ranks = Rank.kept
        current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        ranks = ranks.where(ignore_rank: false).kept if current_account.saas_json.dig('schema', 'ranks', 'ignore_rank') == true
        pagy, ranks = if params[:search_text].present?
                        pagy(ranks.search_by_name(params[:search_text]), items: params[:per_page])
                      else
                        pagy(ranks.order('name ASC'), items: params[:per_page])
                      end
        render_success(data: ranks, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        rank = Rank.new(resource_params)
        rank.save
        render_json(data: rank)
      end

      def update
        @rank.update(resource_params)
        render_json(data: @rank)
      end

      def destroy
        @rank.discard
        render_json(data: @rank)
      end

      private

      def resource_params
        params.require(:rank).permit(:name, :description)
      end

      def set_rank
        @rank = Rank.kept.friendly.find(params[:id])
      end
    end
  end
end
