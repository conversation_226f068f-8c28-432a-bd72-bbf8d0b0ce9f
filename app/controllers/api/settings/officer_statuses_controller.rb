# frozen_string_literal: true

module Api
  module Settings
    class OfficerStatusesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_officer_status, only: %w[update destroy]

      def index
        officer_statuses = OfficerStatus.kept
        pagy, officer_statuses = if params[:search_text].present?
                                   pagy(officer_statuses.search_by_name(params[:search_text]), items: params[:per_page])
                                 else
                                   pagy(officer_statuses.order('name ASC'), items: params[:per_page])
                                 end
        render_success(data: officer_statuses, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        officer_status = OfficerStatus.new(resource_params)
        officer_status.save
        render_json(data: officer_status)
      end

      def update
        @officer_status.update(resource_params)
        render_json(data: @officer_status)
      end

      def destroy
        @officer_status.discard
        render_json(data: @officer_status)
      end

      private

      def resource_params
        params.require(:officer_status).permit(:name, :description)
      end

      def set_officer_status
        @officer_status = OfficerStatus.kept.friendly.find(params[:id])
      end
    end
  end
end
