# frozen_string_literal: true

module Api
  module Settings
    class DisciplineStatusesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_discipline_status, only: %w[update destroy show]

      def index
        # In dropdown, 'per_page' option is used to get 'N' number of results.
        discipline_statuss = DisciplineStatus.kept
        pagy, discipline_statuss = if params[:search_text].present?
                                     pagy(discipline_statuss.search_by_name(params[:search_text]), items: params[:per_page])
                                   else
                                     pagy(discipline_statuss.order('name ASC'), items: params[:per_page])
                                   end
        render_success(data: discipline_statuss, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        discipline_status = DisciplineStatus.new(resource_params)
        discipline_status.save
        render_json(data: discipline_status)
      end

      def update
        @discipline_status.update(resource_params)
        render_json(data: @discipline_status)
      end

      def destroy
        @discipline_status.discard
        render_json(data: @discipline_status)
      end

      def show
        render_json(data: @discipline_status)
      end

      private

      def resource_params
        params.require(:discipline_status).permit(:name, :description)
      end

      def set_discipline_status
        @discipline_status = DisciplineStatus.kept.friendly.find(params[:id])
      end
    end
  end
end
