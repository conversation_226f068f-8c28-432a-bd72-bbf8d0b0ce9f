# frozen_string_literal: true

module Api
  module Settings
    class EmploymentStatusesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employment_status, only: %w[update destroy]

      def index
        employment_statuses = EmploymentStatus.kept
        pagy, employment_statuses = if params[:search_text].present?
                                      pagy(employment_statuses.search_by_name(params[:search_text]), items: params[:per_page])
                                    else
                                      pagy(employment_statuses.order('name ASC'), items: params[:per_page])
                                    end
        render_success(data: employment_statuses, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        employment_status = EmploymentStatus.new(resource_params)
        employment_status.save
        render_json(data: employment_status)
      end

      def update
        @employment_status.update(resource_params)
        render_json(data: @employment_status)
      end

      def destroy
        @employment_status.discard
        render_json(data: @employment_status)
      end

      private

      def resource_params
        params.require(:employment_status).permit(:name, :description)
      end

      def set_employment_status
        @employment_status = EmploymentStatus.kept.friendly.find(params[:id])
      end
    end
  end
end
