# frozen_string_literal: true

module Api
  module Settings
    class PacfsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_pacf, only: %w[update destroy show]

      def index
        # In dropdown, 'per_page' option is used to get 'N' number of results.
        pacfs = Pacf.kept
        pagy, pacfs = if params[:search_text].present?
                        pagy(pacfs.search_by_name(params[:search_text]), items: params[:per_page])
                      else
                        pagy(pacfs.order('name ASC'), items: params[:per_page])
                      end
        render_success(data: pacfs, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        pacf = Pacf.new(resource_params)
        pacf.save
        render_json(data: pacf)
      end

      def update
        @pacf.update(resource_params)
        render_json(data: @pacf)
      end

      def destroy
        @pacf.discard
        render_json(data: @pacf)
      end

      def show
        render_json(data: @pacf)
      end

      private

      def resource_params
        params.require(:pacf).permit(:name, :description)
      end

      def set_pacf
        @pacf = Pacf.kept.friendly.find(params[:id])
      end
    end
  end
end
