# frozen_string_literal: true

module Api
  class NotificationsController < Api::BaseController
    include Api::ApiRescues
    before_action :set_scheduled_notification, only: %w[scheduled_notification_update scheduled_notification_destroy]

    def create
      # return render_error('Notification sending is allowed only in the Production environment') unless ENV['ENABLE_NOTIFICATION'] == 'yes'
      notification = Notification.new(resource_params)
      notification.user_id = current_user.id
      notification.subdomain = request.headers['HTTP_NATS_SUBDOMAIN'] if request.headers['HTTP_NATS_SUBDOMAIN'].present?

      email_content = params[:notification][:email_content]
      if email_content
        notification.email_content = email_content
        template = EmailTemplate.find(params[:notification][:email_template_id])
        notification.email_template_id = template.template_id
        html = template.generate_preview(email_content)
        notification.email_message = html
      end

      if notification.save
        schedule_or_enqueue_notification(notification)
        render_json(data: notification)
      else
        render_error(notification.errors.full_messages.uniq, 422)
      end
    end

    def index
      notifications = if request.headers['HTTP_NATS_SUBDOMAIN'].present?
                        Notification.includes(:user).where("change_request_notification = false AND (is_scheduled = false OR (is_scheduled = true AND status = 2))")
                                    .where(subdomain: request.headers['HTTP_NATS_SUBDOMAIN']).order('id DESC')
                      else
                        Notification.includes(:user).where("change_request_notification = false AND (is_scheduled = false OR (is_scheduled = true AND status = 2))").order('id DESC')
                      end
      pagy, notifications = if params[:from_date].present?
                              pagy(notifications.filter_by_date(params[:from_date], params[:to_date]))
                            else
                              pagy(notifications)
                            end

      render_success(data: notifications.includes(:sms_attachments_attachments, :files_attachments), options: { meta: pagy_headers_hash(pagy) })
    end

    def scheduled_notifications_index
      scheduled_notifications = if request.headers['HTTP_NATS_SUBDOMAIN'].present?
                                  Notification.kept.includes(:user).where(is_scheduled: true, status: [:pending, :scheduled], subdomain: request.headers['HTTP_NATS_SUBDOMAIN']).order('id DESC')
                                else
                                  Notification.kept.includes(:user).where(is_scheduled: true, status: [:pending, :scheduled]).order('id DESC')
                                end
      pagy, scheduled_notifications = pagy(scheduled_notifications)
      render_success(data: scheduled_notifications.includes(:sms_attachments_attachments, :files_attachments), options: { meta: pagy_headers_hash(pagy) })
    end

    def scheduled_notification_update
      return render_error('This Notification is not Scheduled') unless @notification.is_scheduled

      if @scheduled_notification.update(resource_params)
        schedule_or_enqueue_notification(@scheduled_notification, true)
        render_success(data: @scheduled_notification)
      else
        render_error(@scheduled_notification.errors.full_messages.uniq, 422)
      end
    end

    def scheduled_notification_destroy
      return render_error('This Notification is not Scheduled') unless @scheduled_notification.is_scheduled

      remove_scheduled_job(@scheduled_notification)
      @scheduled_notification.discard
      render_success(data: @scheduled_notification)
    end

    def push_notification_index
      notification_ids = NotificationTracker.where(employee_id: @current_employee.id).pluck(:notification_id)
      pagy, notifications = pagy(Notification.where(id: notification_ids).where(push: true).order('id DESC'))

      render_success(data: notifications.includes(:sms_attachments_attachments, :files_attachments), options: { meta: pagy_headers_hash(pagy) })
    end

    def push_notification_show
      notification = Notification.find(params[:notification_id])
      render_json(data: notification)
    end

    def notification_receiver_count
      notification = Notification.new(resource_params)
      target_tenant = Apartment::Tenant.current
      if request.headers['HTTP_NATS_SUBDOMAIN'].present?
        target_tenant = request.headers['HTTP_NATS_SUBDOMAIN']
        Apartment::Tenant.switch!(target_tenant)
        notification.subdomain = target_tenant
      end
      current_account = Account.find_by(subdomain: target_tenant)
      receiver_count = Generators::EmployeesQueryGenerator.generate(ActionController::Parameters.new(notification.filters),
                                                                    current_account, 'notification').ids.uniq.count
      scheduled_date = notification.scheduled_date&.strftime('%m/%d/%Y')
      scheduled_time = notification.scheduled_time&.strftime("%I:%M %p")
      message = notification.is_scheduled ? "The Notification will be Scheduled for #{scheduled_date} #{scheduled_time} for #{receiver_count} recipient(s)." : "Are you sure you want to send this to #{receiver_count} members?"
      render_success(data: nil, message: message)
    end

    private

    def resource_params
      params.require(:notification)
          .permit(:sms, :sms_to, :email, :email_to, :sms_message, :email_message, :subject, :push, :push_message,
                  :is_scheduled, :scheduled_date, :scheduled_time, :email_template_id, files: [], sms_attachments: [],
                  filters: {}, email_content: {})
    end

    def set_scheduled_notification
      @scheduled_notification = Notification.kept.find(params[:id])
    end

    def schedule_or_enqueue_notification(notification, reschedule = false)
      return NotificationJob.perform_later(notification.id) unless notification.is_scheduled?

      scheduled_at = Notification.utc_time(notification.scheduled_date, notification.scheduled_time)
      cron_time = (Time.current.in_time_zone('America/New_York').end_of_day - 55.minutes).utc

      remove_scheduled_job(notification) if reschedule

      unless scheduled_at <= cron_time
        notification.update_columns(status: 'pending')
        return
      end

      subdomain = Apartment::Tenant.current
      job = NotificationJob.set(wait_until: scheduled_at).perform_later(notification.id, subdomain)
      notification.update_columns(job_id: job.provider_job_id, status: 'scheduled') if job
    end

    def remove_scheduled_job(notification)
      job = Sidekiq::ScheduledSet.new.find_job(notification.job_id)
      job&.delete
    end
  end
end
