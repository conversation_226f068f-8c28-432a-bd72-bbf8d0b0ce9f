module Api
  module Employees
    class ImproperPracticePanelsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_improper_practice_panel, only: %w[update destroy]

      def index
        return render_error('Improper practice id is missing') if params[:improper_practice_id].blank?

        pagy, improper_practice_panels = pagy(ImproperPracticePanel.kept.where(improper_practice_id: params[:improper_practice_id]))
        render_success(data: improper_practice_panels, options: { change_request: "improper_practice_panel", meta: pagy_headers_hash(pagy) })
      end

      def create
        improper_practice_panel = ImproperPracticePanel.new(resource_params)
        improper_practice_panel.save
        render_json(data: improper_practice_panel)
      end

      def update
        @improper_practice_panel.update(resource_params)
        render_json(data: @improper_practice_panel)
      end

      def destroy
        @improper_practice_panel.discard
        render_json(data: @improper_practice_panel)
      end

      private

      def resource_params
        params.require(:improper_practice_panel).permit(:improper_practice_id, :hearing_date, :notes, :description, :brief_date, :brief, :report_description, :report_date, :tentative_decision_description, :tentative_decision_date, :final_decision_description, :final_decision_date)
      end

      def set_improper_practice_panel
        @improper_practice_panel = ImproperPracticePanel.kept.find(params[:id])
      end
    end
  end
end