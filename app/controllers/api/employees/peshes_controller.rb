# frozen_string_literal: true

module Api
  module Employees
    class PeshesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_pesh, only: %w[update destroy]

      def index
        peshes = if params[:employee_id].blank? || params[:employee_id] == 'null'
                   Pesh.includes(:files_attachments).kept.where(employee_id: nil).order(id: :desc)
                 else
                   Pesh.includes(:files_attachments).kept.where(employee_id: params[:employee_id])
                 end
        pagy, peshes = pagy(peshes)
        render_success(data: peshes, options: { change_request: 'pesh', meta: pagy_headers_hash(pagy) })
      end

      def show
        render_json(data: @pesh)
      end

      def create
        pesh = Pesh.new(resource_params)
        pesh.save
        render_json(data: pesh)
      end

      def update
        @pesh.update(resource_params)
        render_json(data: @pesh)
      end

      def destroy
        @pesh.discard
        render_json(data: @pesh)
      end

      private

      def set_pesh
        @pesh = Pesh.kept.find(params[:id])
      end

      def resource_params
        resource_param = params.require(:pesh).permit(:complaint, :date, :remarks, :office_id, :employee_id, files: [])
        resource_param[:employee_id] = nil if resource_param[:employee_id] == 'null'
        resource_param[:files] = [] if params[:pesh][:remove_all_files] == 'true'
        resource_param
      end
    end
  end
end
