# frozen_string_literal: true

module Api
  module Employees
    class EmployeeGrievanceStepsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_grievance_step, only: :update

      def create
        employee_grievance_step = EmployeeGrievanceStep.new(resource_params)
        employee_grievance_step.save
        render_json(data: employee_grievance_step, options: { include: [:hearings] })
      end

      def update
        hearings_data = params[:employee_grievance_step].delete(:hearings_attributes)

        if @employee_grievance_step.update(resource_params) && hearings_data
          hearings_data.each do |_key, hearing_param|
            if hearing_param[:id].blank?
              hearing = @employee_grievance_step.hearings.create(hearing_params(hearing_param))
            else
              hearing = @employee_grievance_step.hearings.find_by(id: hearing_param[:id])
              next unless hearing

              hearing.update(hearing_params(hearing_param))
            end

            hearing.transcripts&.purge if hearing.remove_all_transcripts == 'true'
            hearing.evidences&.purge if hearing.remove_all_evidences == 'true'
            hearing.briefs&.purge if hearing.remove_all_briefs == 'true'
          end
        end
        render_json(data: @employee_grievance_step, options: { include: [:hearings] })
      end

      private

      def resource_params
        resource_param = params.require(:employee_grievance_step).permit(:date, :recommended_notes, :is_settled, :olr, :arbitration, :is_pending, :step, :win, :loss, :employee_grievance_id, :grievance_status_id,
                                                                         :decision, files: [], hearings_attributes: [:id, :hearing_date, :notes, :description, :brief_date, :brief, transcripts: [],
                                                                                                                                  evidences: [], briefs: []])
        resource_param[:files] = [] if params[:employee_grievance_step][:remove_all_files] == 'true'
        resource_param
      end

      def hearing_params(params)
        params.permit([:id, :hearing_date, :notes, :description, :brief_date, :brief, :remove_all_transcripts, :remove_all_evidences, :remove_all_briefs, transcripts: [], evidences: [], briefs: []])
      end

      def set_employee_grievance_step
        @employee_grievance_step = EmployeeGrievanceStep.kept.find(params[:id])
      end
    end
  end
end
