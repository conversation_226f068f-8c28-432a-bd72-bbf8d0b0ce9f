# frozen_string_literal: true

module Api
  module Employees
    class EmployeeMeetingTypesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_meeting_type, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        pagy, employee_meeting_types = pagy(EmployeeMeetingType.includes(:meeting_type).kept
                                                .where(employee_id: params[:employee_id]).order('meeting_date DESC'))
        render_success(data: employee_meeting_types, options: { change_request: "employee_meeting_type", meta: pagy_headers_hash(pagy) })
      end

      def create
        employee_meeting_type = EmployeeMeetingType.new(resource_params)
        employee_meeting_type.save
        render_json(data: employee_meeting_type)
      end

      def update
        @employee_meeting_type.update(resource_params)
        render_json(data: @employee_meeting_type)
      end

      def destroy
        @employee_meeting_type.discard
        render_json(data: @employee_meeting_type)
      end

      private

      def resource_params
        params.require(:employee_meeting_type).permit(:attended, :employee_id, :meeting_date, :meeting_type_id,
                                                      :notes)
      end

      def set_employee_meeting_type
        @employee_meeting_type = EmployeeMeetingType.kept.find(params[:id])
      end
    end
  end
end
