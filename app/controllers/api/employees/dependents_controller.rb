# frozen_string_literal: true

module Api
  module Employees
    class DependentsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_dependent, only: %w[update destroy]

      def index
        return render_error('Required id is missing') if params[:employee_id].blank? || params[:life_insurance_id].blank?

        pagy, dependents = pagy(Dependent.kept.includes(:life_insurance)
                                    .where(employee_id: params[:employee_id], life_insurance_id: params[:life_insurance_id]))
        render_success(data: dependents, options: {change_request: "dependent", meta: pagy_headers_hash(pagy)})
      end

      def create
        dependent = Dependent.new(resource_params)
        dependent.save
        render_json(data: dependent)
      end

      def update
        @dependent.update(resource_params)
        render_json(data: @dependent)
      end

      def destroy
        @dependent.discard
        render_json(data: @dependent)
      end

      private

      def resource_params
        params.require(:dependent).permit(:name, :relationship, :amount, :address, :spouse_contribution,
                                          :date, :age, :employee_id, :life_insurance_id)
      end

      def set_dependent
        @dependent = Dependent.kept.find(params[:id])
      end
    end
  end
end
