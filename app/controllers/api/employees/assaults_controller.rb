# frozen_string_literal: true

module Api
  module Employees
    class AssaultsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_assault, only: %i[show update destroy]

      def index
        assaults = Assault.kept.where(employee_id: params[:employee_id]).order(id: :desc)
        pagy, assaults = pagy(assaults)
        render_success(data: assaults, options: { meta: pagy_headers_hash(pagy), change_request: 'assault', include: [:witnesses] })
      end

      def show
        render_json(data: @assault, options: { include: %i[witnesses command office], change_request: 'assault' })
      end

      def create
        assault = Assault.new(resource_params)
        assault.save
        render_json(data: assault, options: { include: %i[witnesses command office] })
      end

      def update
        @assault.update(resource_params)
        render_json(data: @assault, options: { include: %i[witnesses command office] })
      end

      def destroy
        @assault.discard
        render_json(data: @assault)
      end

      private

      def set_assault
        @assault = Assault.find(params[:id])
      end


      def resource_params
        resource_param = params.require(:assault).permit(:location,:date, :time, :physical, :verbal, :description, :incident_reported_to,
                                        :lodi_pack, :delegate,:employee_id, :cod_report_date, :cod, :inmate_name, :bookcase, :nysid,
                                        :was_inmate_rearrested, :charges, :suspension, :suspension_days_count, :type_of_incident_id, :office_id,
                                        :who_affected_suspension, witnesses_attributes: %i[name phone address id], files: [])
        resource_param[:files] = [] if params[:assault][:remove_all_files] == 'true'
        resource_param[:suspension_days_count] = nil if resource_param[:suspension] == 'false'
        resource_param
      end
    end
  end
end
