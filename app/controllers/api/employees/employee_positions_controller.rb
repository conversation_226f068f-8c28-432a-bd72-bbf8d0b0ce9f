# frozen_string_literal: true

module Api
  module Employees
    class EmployeePositionsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_position, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        employee_positions = EmployeePosition.includes(:position, :delegate_series).kept.where(employee_id: params[:employee_id])
                                             .order('start_date DESC')
        pagy, employee_positions = pagy(employee_positions)
        render_success(data: employee_positions, options: { change_request: "employee_position", meta: pagy_headers_hash(pagy) })
      end

      def create
        ActiveRecord::Base.transaction do
          @employee_position = EmployeePosition.new(resource_params)

          current_account = Account.find_by(subdomain: Apartment::Tenant.current)
          allow_multiple_present_status = current_account.saas_json.dig('schema', 'employees', 'allow_multiple_present_status')
          if allow_multiple_present_status == 'true'
            @employee_position.save
          else
            last_status = nil

            if @employee_position.valid? && @employee_position.end_date.blank?
              last_status = EmployeePosition.kept.where(employee_id: @employee_position.employee_id, end_date: nil).last
            end

            if @employee_position.save && last_status.present?
              last_status.update(end_date: @employee_position.start_date)

              if last_status.errors['Date range'].present?
                @employee_position.errors.add('Date range is invalid', " - could not update the end date of the status(#{last_status.name})")
                raise ActiveRecord::Rollback
              end
            end
          end
        end

        render_json(data: @employee_position)
      end

      def update
        @employee_position.update(resource_params)
        render_json(data: @employee_position)
      end

      def destroy
        @employee_position.discard
        render_json(data: @employee_position)
      end

      private

      def resource_params
        params.require(:employee_position).permit(:employee_id, :end_date, :notes, :position_id, :start_date, :delegate_series_id)
      end

      def set_employee_position
        @employee_position = EmployeePosition.kept.find(params[:id])
      end
    end
  end
end
