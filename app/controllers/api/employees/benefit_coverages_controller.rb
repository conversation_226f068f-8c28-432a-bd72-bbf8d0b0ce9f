# frozen_string_literal: true

module Api
  module Employees
    class BenefitCoveragesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_benefit_coverage, only: %w[update destroy]

      def index
        return render_error('Required id is missing') if params[:employee_id].blank?

        pagy, benefit_coverages = if order_by_relationship
                                    pagy(BenefitCoverage.kept.includes(:files_attachments, :gender, employee_benefit: :benefit).where(employee_id: params[:employee_id], employee_benefit_id: params[:employee_benefit_id]).order("relationship DESC, birthday ASC"))
                                  else
                                    pagy(BenefitCoverage.kept.includes(:files_attachments, :gender, employee_benefit: :benefit).where(employee_id: params[:employee_id], employee_benefit_id: params[:employee_benefit_id]))
                                  end
        render_success(data: benefit_coverages, options: { change_request: "benefit_coverage", meta: pagy_headers_hash(pagy) })
      end

      def create
        benefit_coverage = BenefitCoverage.new(resource_params)
        benefit_coverage.save
        AddDependentToAllBenefitsJob.perform_later(resource_params, benefit_coverage, "create") if resource_params['add_dependent_to_all_benefits'] == "true" && current_account.saas_json.dig('schema', 'benefit_coverages', 'add_dependent_to_all_benefits').present?
        render_json(data: benefit_coverage)
      end

      def update
        @benefit_coverage.update(resource_params)
        AddDependentToAllBenefitsJob.perform_later(resource_params, @benefit_coverage, "update", @first_name, @last_name) if resource_params['add_dependent_to_all_benefits'] == "true" && current_account.saas_json.dig('schema', 'benefit_coverages', 'add_dependent_to_all_benefits').present?
        render_json(data: @benefit_coverage)
      end

      def destroy
        @benefit_coverage.discard
        @benefit_coverage.files.purge_later
        render_json(data: @benefit_coverage)
      end

      private

      def resource_params
        resource_param = params.require(:benefit_coverage).permit(:address, :birthday, :employee_benefit_id, :employee_id,
                                                                  :expires_at, :name, :first_name, :last_name, :suffix, :relationship, :social_security_number, :phone, :age, :semester,
                                                                  :dependent, :effective_date, :gender_id, :serviced_expiration, :add_dependent_to_all_benefits, :school_status, :student, files: [])
        resource_param[:files] = [] if params[:benefit_coverage][:remove_all_files] == 'true'
        resource_param
      end

      def set_benefit_coverage
        @benefit_coverage = BenefitCoverage.kept.find(params[:id])
        @first_name = @benefit_coverage.first_name
        @last_name = @benefit_coverage.last_name
      end

      def order_by_relationship
        return nil unless current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship').present? &&
          current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship') == true

        current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship')
      end
    end
  end
end
