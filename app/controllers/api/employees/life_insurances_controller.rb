# frozen_string_literal: true

module Api
  module Employees
    class LifeInsurancesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_life_insurance, only: %w[update destroy]

      def index
        return render_error('Required id is missing') if params[:employee_id].blank?

        pagy, life_insurances = pagy(LifeInsurance.includes(:files_attachments).kept.where(employee_id: params[:employee_id]).order('id DESC'), items: params[:per_page])
        render_success(data: life_insurances, options: { change_request: "life_insurance", meta: pagy_headers_hash(pagy) })
      end

      def create
        life_insurance = LifeInsurance.new(resource_params)
        life_insurance.save
        render_json(data: life_insurance)
      end

      def update
        @life_insurance.update(resource_params)
        render_json(data: @life_insurance)
      end

      def destroy
        @life_insurance.discard
        @life_insurance.files.purge_later
        render_json(data: @life_insurance)
      end

      private

      def resource_params
        resource_param = params.require(:life_insurance).permit(:employee_id, :insurance_type, :amount, :start_date, :notes,
                                               files: [])
        resource_param[:files] = [] if params[:life_insurance][:remove_all_files] == 'true'
        resource_param
      end

      def set_life_insurance
        @life_insurance = LifeInsurance.kept.find(params[:id])
      end
    end
  end
end
