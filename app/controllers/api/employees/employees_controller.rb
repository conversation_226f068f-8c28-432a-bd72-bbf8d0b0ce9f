# frozen_string_literal: true

module Api
  module Employees
    class EmployeesController < Api::BaseController
      include Api::ApiRescues
      include Api::SetBenefitCoverageExpiration
      before_action :set_employee, only: %w[notes_section edit update update_allowed_fields destroy show title_code_update]
      before_action :old_email_value, only: :update

      def index
        employees = filter_by_category_and_associations
        employees = filter_by_search_and_exact_search_text(employees)

        pagy, employees = pagy(employees)
        render_success(data: employees, options: { include: [:contacts], category: params[:category].present? ? params[:category] : '', meta: pagy_headers_hash(pagy) })
      end

      def exact_search
        employees = filter_by_category_and_associations
        employees = filter_by_search_and_exact_search_text(employees, true)

        pagy, employees = pagy(employees)
        render_success(data: employees, options: { include: [:contacts], category: params[:category].present? ? params[:category] : '', meta: pagy_headers_hash(pagy) })
      end

      def profile
        render_json(data: @current_employee, options: { include: [:contacts, :gender, :affiliation, :unit, :marital_status, :tour_of_duty, :platoon], change_request: %w(employee contact)})
      end

      def notes_section
        render_json(data: @employee, options: { params: { notes_search_text: params[:search_text] } })
      end

      def employee_dropdown_data
        employees = params[:category].present? && params[:category] == 'contact_person' ? Employee.unscoped.kept.select(:first_name, :last_name, :middle_name, :suffix, :id ).filter_contact_persons : Employee.kept.select(:first_name, :last_name, :middle_name, :suffix, :id )
        employees = if params[:search_text].present? && params[:search_text].match(/^[0-9]*$/) && current_account.saas_json.dig('schema', 'reports', 'exact_search_results') == true
                      employees.employee_exact_search(params[:search_text], against: current_account.saas_json.dig('schema', 'reports', 'exact_search_fields') || [])
                    elsif params[:search_text].present?
                      employees.search_by_full_name(params[:search_text])
                    else
                      grievance_logics = current_account.saas_json.dig('schema', 'employee_grievances', 'grievances_specific_logics_for_papba') == true
                      priority_ids = Notification.kept.where(is_scheduled: true, status: [:pending, :scheduled]).pluck(:filters)&.map { |f| f["employee_ids"] }&.compact&.flatten&.uniq
                      priority_ids += EmployeeGrievance.kept.pluck(:employee_ids)&.compact&.flatten&.uniq if grievance_logics
                      if params[:employee_priority_order] == 'true' && priority_ids.present?
                        case_sql = priority_ids.each_with_index.map do |id, idx|
                          "WHEN '#{id}' THEN #{idx}"
                        end&.join(' ')
                        order_clause = "CASE id #{case_sql} ELSE #{priority_ids.size || 0} END"
                        employees.order(Arel.sql(order_clause)).order('employees.last_name, employees.first_name, employees.middle_name')
                      else
                        employees.order_by_name
                      end
                    end
        pagy, employees = pagy(employees, items: params[:per_page])
        render_success(data: employees, options: { meta: pagy_headers_hash(pagy), serializer: EmployeeObjectSerializer })
      end

      def contact_person_dropdown_data
        employees = Employee.unscoped.kept.select(:first_name, :last_name, :middle_name, :suffix, :id, :unit_id).filter_contact_persons
        unit_id = Unit.kept.where("lower(REPLACE(name, ' ', '')) ILIKE ?", "%#{params[:unit_name].downcase.gsub(' ', '')}%").pluck(:id) if params[:unit_name].present?
        pagy, employees = unit_id.present? ? pagy(employees.where('unit_id in (?)', unit_id).distinct, items: params[:per_page]) : pagy(employees, items: params[:per_page])
        render_success(data: employees, options: { meta: pagy_headers_hash(pagy), serializer: EmployeeObjectSerializer })
      end

      def title_code_update
        title = Title.kept.find_by(title_code: params[:title_code])
        return render_error('Title is not found', :not_found) if title.blank?

        ActiveRecord::Base.transaction do
          @employee.update(title_code: params[:title_code])
          title_code = {}
          title_code['employee_titles'] = @employee.employee_titles.where(title_id: title.id, department_id: title.department_id,
                                                                          section_id: title.section_id, end_date: nil).first_or_create!
          title_code['employee_departments'] = @employee.employee_departments.where(department_id: title.department_id,
                                                                                    end_date: nil).first_or_create!
          title_code['employee_sections'] = @employee.employee_sections.where(department_id: title.department_id,
                                                                              section_id: title.section_id, end_date: nil).first_or_create!
          render json: { data: title_code }
        rescue StandardError => e
          render_error(e)
        end
      end

      def show
        if @employee.present? && @employee.user_profile_id.present? && @current_user.present? && @employee.user_profile_id == @current_user.id
          render_json(data: @employee, options: { include: %i[contacts gender unit marital_status affiliation tour_of_duty platoon], current_user: @current_user, category: @employee.category.present? ? @employee.category : '', change_request: %w(employee contact) })
        else
          render_json(data: @employee, options: { include: %i[contacts gender unit marital_status affiliation tour_of_duty platoon], category: @employee.category.present? ? @employee.category : '', change_request: %w(employee contact) })
        end
      end

      def create
        employee = Employee.new(set_do_not_mail_true(resource_params))
        employee.is_auto_prescription = params[:employee][:is_auto_prescription]
        params[:employee][:same_as_mailing_address] == 'true' ? employee.same_as_mailing_address = true : employee.same_as_mailing_address = false

        ## Here reverting the auto user creation logics for the SSSA based on the StaffMember.
        ## Incase of any need in future, Please refer the below PR for further details.
        ## https://bitbucket.org/Mallowdev/fuse_rails/pull-requests/2955/diff

        if employee.errors.present?
          render_error(employee.errors.full_messages, 422)
        else
          employee.save
          custom_message_field = current_account.saas_json.dig('schema', 'employees', 'custom_messages')
          message = check_custom_message_fields(employee, custom_message_field, 'created') if custom_message_field.present?
          remove_avatar
          render_json(data: employee, options: { include: [:contacts], category: params[:category].present? ? params[:category] : '' },
                      message: message)
        end
      end

      def update
        ActiveRecord::Base.transaction do
          @employee.is_auto_prescription = params[:employee][:is_auto_prescription]
          address_confirmation = current_account.saas_json.dig('schema', 'employees', 'beneficiary_address_confirmation')
          @employee.update_beneficiary_address = address_confirmation ? params[:update_beneficiary_address] : true
          @employee.update(set_do_not_mail_true(resource_params))
          params[:employee][:same_as_mailing_address] == 'true' ? @employee.same_as_mailing_address = true : @employee.same_as_mailing_address = false

          ## Here reverting the auto user creation logics for the SSSA based on the StaffMember.
          ## Incase of any need in future, Please refer the below PR for further details.
          ## https://bitbucket.org/Mallowdev/fuse_rails/pull-requests/2955/diff
        end
        custom_message_field = current_account.saas_json.dig('schema', 'employees', 'custom_messages')
        message = check_custom_message_fields(@employee, custom_message_field, 'updated') if custom_message_field.present?
        remove_avatar
        render_json(data: @employee,
                    options: { include: [:contacts], category: params[:category].present? ? params[:category] : '' }, message: message)
      end

      def update_allowed_fields
        @employee.update(permitted_editable_resource_params)

        render_json(data: @employee)
      end

      def destroy
        @employee.discard
        render_json(data: @employee)
      end

      def delegate_employees
        pagy, employees = pagy(Employee.joins(employee_positions: :position)
                                 .where('employee_positions.end_date IS NULL AND positions.name = ?', 'Delegate').distinct,
                               items: params[:per_page])
        render_success(data: employees, options: { meta: pagy_headers_hash(pagy), serializer: EmployeeObjectSerializer })
      end

      def address_fields_data
        city = []
        zipcode = []
        state = []
        county = []
        Employee.kept.pluck('city', 'state', 'zipcode', 'county').collect do |detail|
          city << detail[0] unless detail[0].blank?
          state << detail[1] unless detail[1].blank?
          zipcode << detail[2] unless detail[2].blank?
          county << detail[3] unless detail[3].blank?
        end

        render_success(data: { city: city.compact.uniq, state: state.compact.uniq, zipcode: zipcode.compact.uniq, county: county.compact.uniq })
      end

      def login_credentials
        employee = Employee.kept.find_by(id: params[:employee_id]) if params[:employee_id].present?
        if employee.present?
          password = Devise.friendly_token(8)

          if employee.present? && employee.username == params[:data][:username]
            employee.update(password: password, password_confirmation: password)
          elsif employee.present? && employee.username != params[:data][:username]
            employee.update(username: params[:data][:username], password: password, password_confirmation: password)
          end

          if employee.errors.any?
            render_error(employee.errors.full_messages)
          else
            member_credential_mail(employee, password)
          end
        else
          render_error('Employee id is missing or invalid')
        end
      end

      def staff_member_employee
        employees = Employee.kept.where(staff_member: true).includes(:contacts, avatar_attachment: :blob, employee_officer_statuses: :officer_status)
        @employees = params[:query].present? ? employees.search_by_employee(params[:query]) : employees.order_by_name
        render json: staff_member_employee_json
      end

      def update_password
        if @current_employee.present? && resource_params[:password].present? && resource_params[:password_confirmation].present?
          if @current_employee.update(password: resource_params[:password], password_confirmation: resource_params[:password_confirmation])
            render_success(data: @current_employee, message: 'Password updated successfully!', options: { include: [:contacts] })
          else
            render_error(@current_employee.errors.full_messages, 422)
          end
        else
          render_error('Password or confirm password is missing', 422)
        end
      end

      def employee_status_detail
        employment_status_count = Employee.employee_status_detail
        render_success(data: employment_status_count)
      end

      private

      def resource_params
        params.require(:employee).permit(:a_number, :apartment, :avatar, :birthday, :t_shirt_size, :city, :first_name,
                                         :last_name, :marital_status_id, :marital_status_date, :middle_name, :notes, :placard_number, :county,
                                         :shield_number, :previous_shield_number, :slug, :social_security_number, :state, :street, :forgot_password_otp,
                                         :forgot_password_otp_send_at, :veteran_status, :zipcode, :county, :start_date, :gender_id, :unit_id, :prom_prov,
                                         :prom_perm, :title_code, :janus_card, :staff_member, :member_since, :prescription,
                                         :do_not_mail, :janus_card_opt_out_date, :email_opt_out, :sms_opt_out,
                                         :affiliation_id, :tour_of_duty_id, :rdo, :payroll_id, :longevity_date,
                                         :leave_progression_date, :ncc_date, :platoon_id, :primary_work_location,
                                         :username, :password, :password_confirmation, :member_start_date, :suffix,
                                         :maiden_name, :responder_911, :category, :enable_mobile_access, :department_id, :register_vote, :same_as_mailing_address,
                                         contacts_attributes: %i[contact_for contact_type contact_name id value contact_relationship],
                                         mailing_address_attributes: %i[id street zipcode apartment state city]
        )
      end

      def permitted_editable_resource_params
        only_edit_fields = Employee.only_edit_fields.map(&:to_sym)
        params.require(:employee).permit(only_edit_fields)
      end

      def set_employee
        @employee = Employee.unscoped.kept.friendly.includes(:contacts).find(params[:id])
      end

      def set_do_not_mail_true(params)
        params['do_not_mail'] = if resource_params['do_not_mail'] == 'true'
                                  true
                                else
                                  false
                                end
        janus_card_status = current_account.saas_json.dig('schema', 'employees', 'janus_card_status')
        params['do_not_mail'] = true if resource_params['janus_card'] == janus_card_status unless janus_card_status.nil?

        params
      end

      def remove_avatar
        return if params[:employee][:remove_avatar] != 'true' || !@employee.avatar.attached?

        @employee.avatar.purge
      end

      def member_credential_mail(employee, password)
        if password.present?
          work_or_personal = current_account.saas_json.dig('schema', 'employees', 'login_credentials', 'sent_credentials_to_work_email') == true ? 'work' : 'personal'
          mail_address = employee.contacts.where(contact_for: work_or_personal, contact_type: 'email').first
          if mail_address.present? && mail_address.value.present?
            MemberCredentialsMailer.details(employee.username, password, mail_address.value, Apartment::Tenant.current).deliver_later
            render_success(data: employee, message: 'Mail has been sent successfully')
          else
            render_error('Member’s Personal Email address is missing')
          end
        end
      end

      def staff_member_employee_json
        Api::EmployeeObjectSerializer.new(Employee.kept.where(staff_member: true))
      end

      def old_email_value
        @old_mail_value = nil
        mail_value = @employee.contacts.where(contact_type: 'email', contact_for: 'work')
        @old_mail_value = mail_value.first if mail_value.present?

        @old_mail_value
      end

      def analytics_of_employee_user
        employee_user = nil
        employee_user = current_account.saas_json.dig('schema', 'employees', 'analytics_of_employee_user') if current_account.present?

        employee_user
      end

      def employee_user
        mail_value = params[:employee][:contacts_attributes].fourth[:value] unless params[:employee][:contacts_attributes].fourth[:value].blank?
        user = User.kept.where(email: mail_value.downcase) unless mail_value.blank?
        if params[:employee][:staff_member] == 'true' && mail_value.present? && !user.present?
          username = get_user_name(params[:employee][:first_name], nil) if params[:employee][:first_name].present?
          @user = User.create(username: username&.downcase, email: mail_value,
                              access_role: 'Administrator', first_name: params[:employee][:first_name], last_name: params[:employee][:last_name],
                              role_id: Role.where(name: 'Admin').first.id, password: "#{params[:employee][:first_name]}@sssa2o2o", password_confirmation: "#{params[:employee][:first_name]}@sssa2o2o")
          if @user.present? && @user.id != nil
            @employee.update(user_profile_id: @user.id)
          elsif @user.errors.present?
            @employee.errors.add('Employee', 'First Name is already taken')
            raise ActiveRecord::Rollback
          end
        elsif user.present?
          @employee.update(user_profile_id: user.first.id)
        elsif mail_value.blank? && !user.present?
          @employee.errors.add('Member', "'s Work Email address is missing")
          raise ActiveRecord::Rollback
        end
      end

      def check_custom_message_fields(employee, custom_message_field, action)
        errors = []
        custom_message_field.each do |field|
          fields = {}
          next if employee.send(field).blank?

          fields[field] = employee.send(field)
          errors << "#{current_account.saas_json.dig('schema', 'employees', field)} is already in use" if Employee.where.not(id: employee.id)
                                                                                                                   .where(fields).present?
        end
        "#{errors.join(', ')}. Member #{action.titleize} Successfully!" if errors.present?
      end


      def get_user_name(username, user_id)
        index = 0
        user_name = username
        while (users = User.where('lower(username) = ?', user_name.downcase)).present?
          break if users.pluck(:id).include?(user_id) && user_id.present?
          index = index + 1
          user_name = "#{username}_#{index}"
        end
        user_name
      end

      def seach_text_parse_ssn(ssn)
        return nil unless ssn.present? && ssn.gsub('-', '').length == 9 && /^\d{9}$/ === ssn.gsub('-', '')

        ssn.remove!('-', ' ')
        ssn_number = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)
        ssn_number
      end

      def parse_phone_number(phone_number)
        return nil unless phone_number =~ /^\d{10}$/

        '(' + phone_number.first(3) + ') ' + phone_number[3..5] + ' - ' + phone_number.last(4)
      end

      def filter_by_category_and_associations
        employees = Employee.kept.includes(:mailing_address)
        if params[:staff_member_only].present?
          employees = employees.where(staff_member: true)
        elsif params[:category].present?
          employees = case params[:category]
                      when 'contact_person'
                        employees.unscoped.kept.filter_contact_persons
                      when 'legislative_contacts'
                        employees.unscoped.kept.filter_legislative_contacts
                      else
                        employees
                      end
        end

        employee_ids = nil

        [ [ :employment_status, EmployeeEmploymentStatus, :employment_status_id ], [ :officer_status, EmployeeOfficerStatus, :officer_status_id ], [ :search_case_number, EmployeeDisciplineSetting, :dan_number ],
          [:search_number, EmployeeGrievance, :number], [:employee_rank, EmployeeRank, :rank_id],
          [:employee_position, EmployeePosition, :position_id]].each do |param, model, column|
          next unless params[param].present?

          query = model.kept.where(column => params[param])
          query = query.where('end_date IS NULL OR end_date > ?', Date.today) if %i[employment_status officer_status].include?(param)
          ids = query.pluck(:employee_id)
          employee_ids = employee_ids.nil? ? ids : employee_ids & ids
        end

        employees = employees.where(id: employee_ids) unless employee_ids.nil?

        params[:birthday].present? ? employees.where(birthday: Date.parse(params[:birthday])) : employees
      end

      def filter_by_search_and_exact_search_text(employees, exact_search = false)
        if params[:search_contact_number].present?
          contact_number = parse_phone_number(params[:search_contact_number]) || params[:search_contact_number]
          employees = employees.joins(:contacts).where('contacts.value = ? AND contacts.contact_type = ?', "#{contact_number}", 'phone')
        end
        if (exact_search_text = params[:exact_search_text]).present?
          exact_search_fields = current_account.saas_json.dig('schema', 'employees', 'exact_search_fields')
          if current_account.saas_json.dig('schema', 'employees', 'ssn_unique_search') == true && (ssn = seach_text_parse_ssn(exact_search_text)).present?
            employees.where(social_security_number: ssn)
          elsif exact_search_fields.present?
            employees.employee_exact_search(exact_search_text, against: exact_search_fields || [])
          end
        elsif (search_text = params[:search_text]).present?
          search_columns = current_account.saas_json.dig('schema', 'employees', 'search_columns')
          same_model = search_columns&.dig('same_model') || []
          associated_model = search_columns&.dig('associated_model')&.symbolize_keys || {}
          contact_search = search_columns.dig('associated_model', 'contacts').present?
          search_value = contact_search ? parse_phone_number(search_text) || search_text : search_text

          if !exact_search
            employees.search_by_employee(same_model || [], associated_model || {}, search_value)
          elsif exact_search
            employees.employee_exact_search(search_value, against: same_model || [], associated_against: associated_model || {})
          end
        elsif params[:placard_number].present?
          employees.employee_exact_search(params[:placard_number], against: ['placard_number'])
        else
          employees.order_by_name
        end
      end
    end
  end
end
