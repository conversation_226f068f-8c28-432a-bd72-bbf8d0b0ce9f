# frozen_string_literal: true

module Api
  module Employees
    class BeneficiariesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_beneficiary, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        beneficiaries = if params[:category] == 'annuity_beneficiary'
                          Beneficiary.unscoped.kept.includes(:file_attachment).where(employee_id: params[:employee_id] , category: 'annuity_beneficiary').order(:beneficiary_type)
                        else
                          Beneficiary.kept.includes(:file_attachment).where(employee_id: params[:employee_id]).order(:beneficiary_type)
                        end
        pagy, beneficiaries = pagy(beneficiaries)

        beneficiary_type_values = if params[:category] == 'annuity_beneficiary'
                                    current_account.saas_json.dig('schema', 'annuity_beneficiaries', 'beneficiary_type_values')
                                  else
                                    current_account.saas_json.dig('schema', 'beneficiaries', 'beneficiary_type_values')
                                  end

        if beneficiary_type_values
          beneficiary_types = []
          beneficiary_type_values.each do |beneficiary_type|
            beneficiary_types << { attributes: { name: beneficiary_type, id: beneficiary_type } }
          end
        else
          beneficiary_types = [{ attributes: { name: 'Primary', id: 'Primary' } },
                               { attributes: { name: 'Secondary', id: 'Secondary' } }]
        end

        render_success(data: beneficiaries,
                       options: { change_request: 'beneficiary', meta: pagy_headers_hash(pagy)
                                          .merge!(beneficiary_type: beneficiary_types) })
      end

      def create
        beneficiary = Beneficiary.new(resource_params)
        beneficiary.save
        render_json(data: beneficiary)
      end

      def update
        @beneficiary.update(resource_params)
        render_json(data: @beneficiary)
      end

      def destroy
        @beneficiary.discard
        render_json(data: @beneficiary)
      end

      private

      def resource_params
        resource_param = params.require(:beneficiary).permit(:address, :beneficiary_type, :employee_id, :file, :name, :percentage,
                                            :relationship, :phone, :birthday, :gender_id, :social_security_number, :category)
        remove_file if params[:beneficiary][:remove_file] == 'true'
        resource_param
      end

      def set_beneficiary
        @beneficiary = Beneficiary.unscoped.kept.includes(:file_attachment).find(params[:id])
      end

      def remove_file
        return if params[:beneficiary][:remove_file] != 'true' || !@beneficiary.file.attached?

        @beneficiary.file.purge if @beneficiary.file.present?
      end
    end
  end
end
