# frozen_string_literal: true

module Api
  module Employees
    class EmployeeEmploymentStatusesController < Api::BaseController
      include Api::ApiRescues
      include Api::CreateEmployeeBenefits
      before_action :set_employee_employment_status, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        employee_employment_statuses = EmployeeEmploymentStatus.includes(:employment_status).kept
                                                               .where(employee_id: params[:employee_id])
                                                               .order('start_date DESC')
        pagy, employee_employment_statuses = pagy(employee_employment_statuses)

        render_success(data: employee_employment_statuses, options: { change_request: 'employee_employment_status', meta: pagy_headers_hash(pagy) })
      end

      # rubocop:disable Metrics/MethodLength
      def create
        employee_employment_status = EmployeeEmploymentStatus.new(resource_params)

        current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        close_active_status_presence = current_account.saas_json.dig('schema', 'employees', 'close_active_status')
        active_status_to_be_close = current_account.saas_json.dig('schema', 'employees', 'active_status_to_be_close')

        ActiveRecord::Base.transaction do
          last_status = nil

          if employee_employment_status.valid? && employee_employment_status.end_date.blank?
            last_status = EmployeeEmploymentStatus.kept
                                                  .where!(employee_id: employee_employment_status.employee_id, end_date: nil)
                                                  .last
          end

          allow_multiple_present_status = current_account.saas_json.dig('schema', 'employees', 'allow_multiple_present_status')

          if allow_multiple_present_status == 'true'
            employee_employment_status.save
          elsif employee_employment_status.save && last_status.present?
            last_status.update(end_date: employee_employment_status.start_date)

            if last_status.errors['Date range'].present?
              employee_employment_status.errors.add('Date range is invalid', " - could not update the end date of the status(#{last_status.name})")
              raise ActiveRecord::Rollback
            end

            break unless (last_status.status_name == 'active' && employee_employment_status.status_name != 'active' && close_active_status_presence == 'true' && employee_employment_status.start_date.present?) ||
              (close_active_status_presence == true && params[:employee_employment_status][:close_active_status_popup] == 'true')

            @employee = last_status.employee

            active_status_to_be_close.each do |status|
              close_active_status(status, employee_employment_status.start_date)
            end
          end
        end
        employee_benefits_create(current_account, employee_employment_status)

        render_json(data: employee_employment_status)
      end
      # rubocop:enable Metrics/MethodLength

      def update
        @employee_employment_status.update(resource_params)

        current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        employee_benefits_create(current_account, @employee_employment_status)
        close_active_status_presence = current_account.saas_json.dig('schema', 'employees', 'close_active_status')
        active_status_to_be_close = current_account.saas_json.dig('schema', 'employees', 'active_status_to_be_close')
        @employee = @employee_employment_status.employee
        if close_active_status_presence == true && params[:employee_employment_status][:close_active_status_popup] == 'true'
          active_status_to_be_close.each do |status|
            close_active_status(status, @employee_employment_status.start_date)
          end
        end

        render_json(data: @employee_employment_status)
      end

      def destroy
        @employee_employment_status.discard
        render_json(data: @employee_employment_status)
      end

      private

      def resource_params
        params.require(:employee_employment_status).permit(:employment_status_id, :employee_id, :end_date, :start_date)
      end

      def set_employee_employment_status
        @employee_employment_status = EmployeeEmploymentStatus.kept.find(params[:id])
      end

      def close_active_status(status, end_date)
        case status
        when 'employee_positions'
          @employee.employee_positions.where(end_date: nil).update_all(end_date: end_date) if @employee.employee_positions.present?
        when 'employee_offices'
          @employee.employee_offices.where(end_date: nil).update_all(end_date: end_date) if @employee.employee_offices.present?
        when 'employee_ranks'
          @employee.employee_ranks.where(end_date: nil).update_all(end_date: end_date) if @employee.employee_ranks.present?
        when 'employee_sections'
          @employee.employee_sections.where(end_date: nil).update_all(end_date: end_date) if @employee.employee_sections.present?
        end
      end

      def employee_benefits_create(current_account, employee_employment_status)
        employment_status_names = current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_I')
        employment_statuses_section_IV = current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_IV')&.first
        employee_employment_status_name = employee_employment_status.employment_status.name
        if employee_employment_status.employment_status.present? && (employee_employment_status.end_date == nil || employee_employment_status.end_date >= Date.today) &&
          (employee_employment_status.check_status_names_include(employee_employment_status, employment_status_names) || employee_employment_status.check_status_names_include(employee_employment_status, employment_statuses_section_IV))
          benefit_names = if employment_status_names.include?(employee_employment_status_name)
                            current_account.saas_json.dig('schema', 'benefits', 'benefits_name')
                          elsif employment_statuses_section_IV.include?(employee_employment_status_name)
                            current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_IV').last
                          end
          employee_status_start_date = employee_employment_status.start_date if current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status_start_date') == true
          create_benefits(benefit_names, employee_employment_status.employee, employee_status_start_date)
        end
      end
    end
  end
end
