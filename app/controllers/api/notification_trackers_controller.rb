# frozen_string_literal: true

module Api
  class NotificationTrackersController < Api::BaseController
    skip_load_and_authorize_resource only: :create
    skip_before_action :authenticate_and_set_user, only: :create
    before_action :set_request_data, only: [:create]

    def index
      return render_error('Notification id is missing') if params[:notification_id].blank?

      if request.headers['HTTP_NATS_SUBDOMAIN'].present?
        notification_tracker = NotificationTracker.where(notification_id: params[:notification_id])
      else
        notification_tracker = NotificationTracker.includes(:employee, :notification).where(notification_id: params[:notification_id])
      end

      if params[:status].present? && params[:type].present?
        notification_tracker = notification_tracker.search_filter(params[:status], params[:type])
      end
      
      if params[:search_text].present?
        pagy, notification_tracker = pagy(notification_tracker.search_by_member(params[:search_text]))
      else
        if request.headers['HTTP_NATS_SUBDOMAIN'].present?
          pagy, notification_tracker = pagy(notification_tracker.order('employee_id ASC'))
        else

          pagy, notification_tracker = pagy(notification_tracker.order('employees.first_name ASC, employees.middle_name ASC, employees.last_name ASC'))
        end
      end
      render_success(data: notification_tracker, options: { meta: pagy_headers_hash(pagy) })
    end

    def create
      if @request_data['Type'].casecmp('subscriptionconfirmation').zero?
        open(@request_data['SubscribeURL'])
      else
        create_notification
      end
    rescue StandardError => e
      logger.warn(e.message)
    end

    private

    def create_notification
      begin
        if @request_data['Message'].is_a?(String)
          message = JSON.parse(@request_data['Message'])
        else
          message = JSON.parse(@request_data['Message'].to_json)
        end
      rescue StandardError => e
        Appsignal.send_error(e)
        Rails.logger.info "=============== Error in notification creation ============ \n : #{e.message}"
      end

      hash = message['mail']['headers'].group_by { |h| h['name'] }
      Apartment::Tenant.switch!(hash['subdomain'][0]['value'])
      if hash.dig('is-reminder', 0, 'value') == 'true'
        reminder_tracker = ReminderTracker.where(id: hash['reminder-tracker-id'][0]['value']).first

        if message['notificationType'].casecmp('send').zero?
          reminder_tracker.update(email_delivery_status: :sent)
        elsif message['notificationType'].casecmp('delivery').zero?
          reminder_tracker.update(email_delivery_status: :delivered)
        elsif message['notificationType'].casecmp('open').zero?
          reminder_tracker.update(email_delivery_status: :opened)
        elsif message['notificationType'].casecmp('click').zero?
          reminder_tracker.update(email_delivery_status: :clicked)
        elsif message['notificationType'].casecmp('reject').zero?
          reminder_tracker.update(email_delivery_status: :rejected)
        else
          Rails.logger.info message['notificationType']
        end
      else
        notification_tracker = NotificationTracker.where(employee_id: hash['employee-id'][0]['value'],
                                                         notification_id: hash['notification-id'][0]['value']).first

        if message['notificationType'].casecmp('send').zero?
          notification_tracker.update(sent: true)
        elsif message['notificationType'].casecmp('delivery').zero?
          notification_tracker.update(delivered: true)
        elsif message['notificationType'].casecmp('open').zero?
          notification_tracker.update(opened: true)
        elsif message['notificationType'].casecmp('click').zero?
          notification_tracker.update(clicked: true)
        elsif message['notificationType'].casecmp('reject').zero?
          notification_tracker.update(rejected: true)
        else
          Rails.logger.info message['notificationType']
        end
      end
    end

    def set_request_data
      @request_data = JSON.parse(request.raw_post)
    end
  end
end
