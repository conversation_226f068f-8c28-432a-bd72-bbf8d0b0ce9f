# frozen_string_literal: true

module Api
  class NotificationTrackersController < Api::BaseController
    skip_load_and_authorize_resource only: :create
    skip_before_action :authenticate_and_set_user, only: :create
    before_action :set_request_data, only: [:create]

    def index
      return render_error('Notification id is missing') if params[:notification_id].blank?

      if request.headers['HTTP_NATS_SUBDOMAIN'].present?
        notification_tracker = NotificationTracker.where(notification_id: params[:notification_id])
      else
        notification_tracker = NotificationTracker.includes(:employee, :notification).where(notification_id: params[:notification_id])
      end

      if params[:status].present? && params[:type].present?
        notification_tracker = notification_tracker.search_filter(params[:status], params[:type])
      end
      
      if params[:search_text].present?
        pagy, notification_tracker = pagy(notification_tracker.search_by_member(params[:search_text]))
      else
        if request.headers['HTTP_NATS_SUBDOMAIN'].present?
          pagy, notification_tracker = pagy(notification_tracker.order('employee_id ASC'))
        else

          pagy, notification_tracker = pagy(notification_tracker.order('employees.first_name ASC, employees.middle_name ASC, employees.last_name ASC'))
        end
      end
      render_success(data: notification_tracker, options: { meta: pagy_headers_hash(pagy) })
    end

    def analytics
      return render_error('Notification is missing') if params[:notification_id].blank? || !Notification.where(id: params[:notification_id]).exists?

      analytics_data = fetch_notification_analytics(params[:notification_id])
      render_success(data: analytics_data)
    end

    def create
      if params[:_json].present?
        events = params[:_json].map { |e| e.permit!.to_h }
        NotificationTrackerJob.perform_later(events)
      elsif @request_data['Type'].casecmp('subscriptionconfirmation').zero?
        open(@request_data['SubscribeURL'])
      else
        create_notification
      end
    rescue StandardError => e
      logger.warn(e.message)
    end

    private

    def fetch_notification_analytics(notification_id)
      get_notification = Notification.kept.where(id: notification_id).first
      employee_ids = Generators::EmployeesQueryGenerator.generate(ActionController::Parameters.new(get_notification.filters),
                                                   current_account, 'notification').ids.uniq
      email_opt_out_count = Employee.kept.where(email_opt_out: true, id: employee_ids).count
      notification_analytics_data = NotificationTracker.where(notification_id: notification_id, email: true).select(
        'COUNT(*) AS total_count',
        'SUM(CASE WHEN sent = true THEN 1 ELSE 0 END) AS sent_count',
        'SUM(CASE WHEN sent = true AND delivered = true THEN 1 ELSE 0 END) AS delivered_count',
        'SUM(CASE WHEN sent = true AND delivered = true AND opened = true THEN 1 ELSE 0 END) AS opened_count',
        'SUM(CASE WHEN sent = true AND delivered = true AND opened = true AND clicked = true THEN 1 ELSE 0 END) AS clicked_count',
        'SUM(CASE WHEN sent = true AND delivered = false THEN 1 ELSE 0 END) AS undelivered_count',
        'SUM(CASE WHEN bounced = true THEN 1 ELSE 0 END) AS bounced_count',
        'SUM(CASE WHEN rejected = true THEN 1 ELSE 0 END) AS rejected_count'
      ).take

      total_count     = notification_analytics_data.total_count.to_i
      sent_count      = notification_analytics_data.sent_count.to_i
      delivered_count = notification_analytics_data.delivered_count.to_i
      undelivered_count = notification_analytics_data.undelivered_count.to_i
      opened_count    = notification_analytics_data.opened_count.to_i
      clicked_count   = notification_analytics_data.clicked_count.to_i
      bounced_count   = notification_analytics_data.bounced_count.to_i
      rejected_count  = notification_analytics_data.rejected_count.to_i

      {
        selected_count: employee_ids.count,
        notification_sent_from_us: total_count,
        email_opt_out: { count: email_opt_out_count, percentage: percentage(email_opt_out_count, employee_ids.count) },
        sent: { count: sent_count, percentage: percentage(sent_count, total_count) },
        undelivered: { count: undelivered_count, percentage: percentage(undelivered_count, total_count) },
        opened: { count: opened_count, percentage: percentage(opened_count, total_count) },
        bounced: { count: bounced_count, percentage: percentage(bounced_count, total_count) },
        delivered: { count: delivered_count, percentage: percentage(delivered_count, total_count) },
        clicked: { count: clicked_count, percentage: percentage(clicked_count, total_count) },
        rejected: { count: rejected_count, percentage: percentage(rejected_count, total_count) }
      }
    end

    def percentage(data, total)
      total.positive? ? ((data.to_f / total) * 100).round(2) : 0.0
    end

    def create_notification
      begin
        if @request_data['Message'].is_a?(String)
          message = JSON.parse(@request_data['Message'])
        else
          message = JSON.parse(@request_data['Message'].to_json)
        end
      rescue StandardError => e
        Appsignal.send_error(e)
        Rails.logger.info "=============== Error in notification creation ============ \n : #{e.message}"
      end

      hash = message['mail']['headers'].group_by { |h| h['name'] }
      Apartment::Tenant.switch!(hash['subdomain'][0]['value'])
      if hash.dig('is-reminder', 0, 'value') == 'true'
        reminder_tracker = ReminderTracker.where(id: hash['reminder-tracker-id'][0]['value']).first

        if message['notificationType'].casecmp('send').zero?
          reminder_tracker.update(email_delivery_status: :sent)
        elsif message['notificationType'].casecmp('delivery').zero?
          reminder_tracker.update(email_delivery_status: :delivered)
        elsif message['notificationType'].casecmp('open').zero?
          reminder_tracker.update(email_delivery_status: :opened)
        elsif message['notificationType'].casecmp('click').zero?
          reminder_tracker.update(email_delivery_status: :clicked)
        elsif message['notificationType'].casecmp('reject').zero?
          reminder_tracker.update(email_delivery_status: :rejected)
        else
          Rails.logger.info message['notificationType']
        end
      else
        notification_tracker = NotificationTracker.where(employee_id: hash['employee-id'][0]['value'],
                                                         notification_id: hash['notification-id'][0]['value']).first

        if message['notificationType'].casecmp('send').zero?
          notification_tracker.update(sent: true)
        elsif message['notificationType'].casecmp('delivery').zero?
          notification_tracker.update(delivered: true)
        elsif message['notificationType'].casecmp('open').zero?
          notification_tracker.update(opened: true)
        elsif message['notificationType'].casecmp('click').zero?
          notification_tracker.update(clicked: true)
        elsif message['notificationType'].casecmp('reject').zero?
          notification_tracker.update(rejected: true)
        else
          Rails.logger.info message['notificationType']
        end
      end
    end

    def set_request_data
      @request_data = JSON.parse(request.raw_post)
    end
  end
end
