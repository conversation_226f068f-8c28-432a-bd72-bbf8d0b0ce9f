# frozen_string_literal: true

module Api
  class ReminderTrackersController < Api::BaseController
    before_action :set_reminder_tracker, only: [:update ,:destroy]

    def index
      reminder_tracker = if current_user.role.name.downcase != 'admin'
                            ReminderTracker.includes(:user, :reminder).joins(user: :role).where('LOWER(roles.name) != ?', 'admin').joins(:reminder).where('reminders.admin_only = false')
                         else
                            ReminderTracker.includes(:user, :reminder)
                         end
      if params[:reminder_id].present?
        reminder_tracker = reminder_tracker.where(reminder_id: params[:reminder_id])
        reminder_tracker = reminder_tracker.search_by_user(params[:search_text]) if params[:search_text].present?
        reminder_tracker = reminder_tracker.where(email_delivery_status: params[:email_status]) if params[:email_status].present?
      else
        reminder_tracker = reminder_tracker.kept.where(user_id: current_user.id)
      end
      reminder_tracker = reminder_tracker.where(status: params[:status]) if params[:status].present?
      unread_count =  params[:status] == 'pending' ? reminder_tracker.count : nil
      pagy, reminder_tracker = pagy(reminder_tracker.order(created_at: :desc))

      render_success(data: reminder_tracker, options: { meta: pagy_headers_hash(pagy), unread_reminder_count: unread_count })
    end

    def update
      update_params = resource_params
      update_params[:web_unread_time] = Time.current + 30.minutes if params[:reminder_tracker][:web_unread_time] == 'true'
      if @reminder_tracker.update(update_params)
        render_success(message: 'Reminder status updated', data: @reminder_tracker)
      else
        render_error(@reminder_tracker.errors.full_messages)
      end
    end

    def destroy
      @reminder_tracker.discard
      render_success(message: 'Reminder tracker deleted', data: @reminder_tracker)
    end

    private

    def set_reminder_tracker
      @reminder_tracker = ReminderTracker.find(params[:id])
    end

    def resource_params
      params.require(:reminder_tracker).permit(:status, :web_unread_time)
    end
  end
end
