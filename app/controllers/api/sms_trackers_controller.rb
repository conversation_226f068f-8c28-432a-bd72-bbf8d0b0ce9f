# frozen_string_literal: true

module Api
  class SmsTrackersController < Api::BaseController
    skip_load_and_authorize_resource
    skip_before_action :authenticate_and_set_user

    def create
      notification_tracker = NotificationTracker.find_by(message_id: params[:MessageUUID])
      return if notification_tracker.nil?

      notification_tracker.sms_error_code = check_error_codes
      if params[:Status] == 'sent'
        notification_tracker.update(sms_sent: true)
      elsif params[:Status] == 'failed'
        notification_tracker.update(sms_failed: true)
      elsif params[:Status] == 'delivered'
        notification_tracker.update(sms_delivered: true)
      elsif params[:Status] == 'undelivered'
        notification_tracker.update(sms_undelivered: true)
      end
    end

    def check_error_codes
      if params[:ErrorCode].present?
        error_code = params[:ErrorCode].to_i
        error_code = 203 if error_code > 200 && error_code < 300
      else
        error_code = nil
      end
      error_code
    end
  end
end
