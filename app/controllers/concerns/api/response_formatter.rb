# frozen_string_literal: true

module Api
  module ResponseFormatter
    # Common method for responding with errors(if exists) or with the success response of an active record object.
    def render_json(data:, message: nil, options: {})
      return render_error(data.errors.messages) if data.errors.any?

      render_success(data: data, message: message, options: options)
    end

    # Common method for rendering errors.
    def render_error(message, status = :unprocessable_entity)
      message = message.is_a?(Array) ? message : [message]
      render json: { errors: message }, status: status
    end

    # Common method for rendering success response.
    def render_success(data: nil, message: nil, options: {})
      response_data = {}

      options[:params] ||= {}
      options[:params][:account] = defined?(current_account) ? current_account : Account.find_by(subdomain: Apartment::Tenant.current)
      response_data[:message] = message if message

      if data && active_record_object?(data)
        response_data.merge!(serialized_data(data: data, options: options))
      else
        response_data[:data] = data
      end

      if options.key?(:staff_member)
        response_data[:staff_member] = options[:staff_member]
      end

      if options.key?(:unread_reminder_count)
        response_data[:unread_reminder_count] = options[:unread_reminder_count]
      end

      if options.key?(:change_request) && @current_employee.present? || @token != nil && @token.resource_owner_id.present?
        change_requests = @current_employee.present? ? @current_employee.change_requests.kept : Employee.kept.find(@token.resource_owner_id).change_requests.kept

        change_requests = change_requests.includes(:employee).where(request_type: options[:change_request])
        if (duration_from = options.dig(:meta, :change_request_from_date)).present? && (duration_to = options.dig(:meta, :change_request_to_date)).present? && options.dig(:meta, :analytics_type).present?
          change_requests = change_requests.where('requested_changes::jsonb @> ?', [{ leave_type: options[:meta][:analytics_type] }].to_json)
          change_requests = change_requests.where(change_request_leave_range_query, duration_from, duration_to, duration_from, duration_to, options[:meta][:analytics_type])
        end
        response_data[:change_request] = Api::ChangeRequestSerializer.new(change_requests, build_fast_jsonapi_options({ params: { custom_keys: @current_employee ? "employee" : "user" } }))
      end

      response_data[:analytics_total] = analytics_total if options.key?(:analytics_total) && options[:analytics_total] == 'true'
      response_data[:analytics_configuration_total] = analytics_configuration_total if options.key?(:analytics_configuration_total) && options[:analytics_configuration_total] == 'true'

      response_data[:analytics_rights] = true if options[:current_user].present? && options[:current_user].role != 'Admin' && ([ 'read_user_employee_analytics', 'view_only_user_employee_analytics' ] & options[:current_user].role.rights.pluck(:name)).present?

      if @token != nil && @token.resource_owner_id.present?
        response_data
      else
        render json: response_data, status: :ok
      end
    end

    # rubocop:disable Layout/LineLength
    # Serialize data using 'fast_jsonapi' gem if the data is an 'active record object'.
    #
    # (*) By default the serializer will be of 'model name'. Fo eg: if the object is of class/model 'user', then this method
    # will look for 'user_serializer'.
    #
    # (*) If custom serializer is needed(serializer other than model name) for rendering API response, use 'serializer' option.
    # Eg: 'render_success(data: user, options: { serializer: "custom"})'
    # rubocop:enable Layout/LineLength
    def serialized_data(data:, options: {})
      if options[:category].present? && options[:category] == 'contact_person'
        serializer_class_name = Api::ContactPersonSerializer
      else
        serializer_key = options[:serializer] || compute_collection_type(data)
        serializer_class_name = compute_serializer_name(serializer_key)
      end
      serializer_class_name.new(data, build_fast_jsonapi_options(options))
    end

    def build_fast_jsonapi_options(options)
      if defined?(action_name)
        options[:params][:action_name] ||= action_name
        json_options = {
          params: options[:params],
          meta: options[:meta] || {}
        }
      else
        json_options = {
          params: options[:params] || {},
          meta: options[:meta] || {}
        }
      end

      include = options[:include]
      json_options[:include] = include.is_a?(Array) ? include : [include] if include.present?

      json_options
    end

    def compute_serializer_name(serializer_key)
      return serializer_key unless serializer_key.is_a? Symbol

      ('Api::' + serializer_key.to_s.classify + 'Serializer').constantize
    end

    def compute_collection_type(data)
      # ActiveRecord::Relation has a #klass or #model method to get the class of records it contains
      collection_class = data.is_a?(ActiveRecord::Relation) ? data.klass : data.class
      collection_class.base_class.name.to_sym
    end

    # Returns true if the data is an 'ApplicationRecord' object.
    def active_record_object?(data)
      if data.respond_to?(:any?)
        data.length.zero? || data.any? { |d| d.is_a?(ApplicationRecord) }
      else
        data.is_a?(ApplicationRecord)
      end
    end

    def analytics_total
      lodi_total = 0
      workers_comp_total = 0
      Lodi.kept.where(employee_id: params[:employee_id], lodi_type: 'lodi').each do |lodi|
        lodi_total += (Date.today - lodi.incident_date).to_i unless lodi.return_date != nil
        lodi_total += (lodi.return_date - lodi.incident_date).to_i if lodi.return_date != nil and lodi.return_date.present?
      end

      Lodi.kept.where(employee_id: params[:employee_id], lodi_type: 'workers_comp').each do |lodi|
        workers_comp_total += (Date.today - lodi.incident_date).to_i unless lodi.return_date != nil
        workers_comp_total += (lodi.return_date - lodi.incident_date).to_i if lodi.return_date != nil and lodi.return_date.present?
      end

      leave_sums = Leave.kept.where(employee_id: params[:employee_id]).group(:leave_type).sum(:hours_used)
      analytics = {
        ava: leave_sums["ava"] || 0,
        sick: leave_sums["sick"] || 0,
        vacation: leave_sums["vacation"] || 0,
        personal: leave_sums["personal"] || 0,
        comp_time: leave_sums["comp_time"] || 0,
        overtime: leave_sums["overtime"] || 0,
        lodis: lodi_total,
        workers_comp: workers_comp_total
      }
      analytics
    end

    def analytics_configuration_total
      ava = AnalyticsConfiguration.kept.where(employee_id: params[:employee_id], analytics_type: "ava")
      sick = AnalyticsConfiguration.kept.where(employee_id: params[:employee_id], analytics_type: "sick")
      personal = AnalyticsConfiguration.kept.where(employee_id: params[:employee_id], analytics_type: "personal")
      vacation = AnalyticsConfiguration.kept.where(employee_id: params[:employee_id], analytics_type: "vacation")

      ava_balance = ava.sum(:days_earned) - ava.sum(:days_used) if ava.present?
      sick_balance = sick.sum(:days_earned) - sick.sum(:days_used) if sick.present?
      personal_balance = personal.sum(:days_earned) - personal.sum(:days_used) if personal.present?
      vacation_balance = vacation.sum(:days_earned) - vacation.sum(:days_used) if vacation.present?

      analytics = {
        ava: ava_balance.present? && ava_balance >= 0 ? ava_balance : 0,
        sick: sick_balance.present? && sick_balance >= 0 ? sick_balance : 0,
        personal: personal_balance.present? && personal_balance >= 0 ? personal_balance : 0,
        vacation: vacation_balance.present? && vacation_balance >= 0 ? vacation_balance : 0
      }

      analytics
    end

    def change_request_leave_range_query
      "EXISTS (SELECT 1 FROM jsonb_array_elements(requested_changes) AS elem WHERE (elem->>'started_at')::date BETWEEN ? " +
        "AND ? AND (elem->>'ended_at')::date BETWEEN ? AND ? AND elem->>'leave_type' = ?)"
    end

  end
end
