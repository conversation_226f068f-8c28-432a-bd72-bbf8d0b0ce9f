# frozen_string_literal: true

module Api
  # Common module for JWT
  module Json<PERSON><PERSON><PERSON>oken
    def reference_time
      @reference_time ||= Time.now.utc
    end

    def token_expire_at(in_at_office = nil)
      if in_at_office == true
        reference_time_est = Time.now.in_time_zone('Eastern Time (US & Canada)')
        to_time = time_in_est(@current_user.allow_login_to_time)
        office_out_time_in_minutes = ((to_time - reference_time_est) / 60).round(0)

        expiration_minutes = if params[:remember_me] == 1 && @current_user.restrict_login_out_of_office == true
                               office_out_time_in_minutes.minutes
                             elsif @current_user.restrict_login_out_of_office == true
                               office_out_time_in_minutes > 31 ? 31.minutes : office_out_time_in_minutes.minutes
                             end
        @expire_at ||= reference_time + expiration_minutes
      else
        @expire_at ||= reference_time + (params[:remember_me] == 1 ? 1.day : 31.minutes) # rubocop:disable Naming/MemoizedInstanceVariableName
      end
    end

    def token_issued_at
      @issued_at ||= reference_time # rubocop:disable Naming/MemoizedInstanceVariableName
    end

    # Decode the JWT token and return the payload
    # Ref: https://github.com/jwt/ruby-jwt#issued-at-claim
    def decode(token)
      JWT.decode(token, Rails.application.credentials.secret_key_base, true, verify_iat: true)[0].with_indifferent_access
    end

    # Create a JWT token with user detail in payload and set token details in 'HttpOnly cookie' on successful authentication
    # Ref: https://www.thegreatcodeadventure.com/jwt-storage-in-rails-the-right-way/
    def create_token_and_set_cookie(in_at_office = nil)
      response.set_cookie(:fusesystems_session, value: generate_jwt_token(in_at_office), expires: token_expire_at(in_at_office), path: '/',
                          secure: ENV['ACTIVE_DOMAIN_PROTOCOL'] == 'https', httponly: true)
    end

    def generate_jwt_token(in_at_office = nil)
      payload = {
        user_id: @current_user.id,
        exp: token_expire_at(in_at_office).to_i,
        iat: token_issued_at.to_i
      }
      JWT.encode(payload, Rails.application.credentials.secret_key_base)
    end

    def time_in_est(time1 = nil, time2 = nil)
      return nil if time1.blank? && time2.blank?

      from_time = Time.find_zone("Eastern Time (US & Canada)").parse("#{time1.hour}:#{time1.min}") if time1.present?
      to_time = Time.find_zone("Eastern Time (US & Canada)").parse("#{time2.hour}:#{time2.min}") if time2.present?

      from_time.present? && to_time.present? ? [from_time, to_time] : from_time || to_time
    end
  end
end
