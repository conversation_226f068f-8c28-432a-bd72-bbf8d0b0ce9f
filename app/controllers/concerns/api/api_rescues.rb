# frozen_string_literal: true

module Api
  module Api<PERSON><PERSON>es
    extend ActiveSupport::Concern

    included do
      rescue_from Doorkeeper::Errors::InvalidToken, with: :handle_doorkeeper_errors
      rescue_from Doorkeeper::Errors::TokenForbidden, with: :handle_doorkeeper_errors
      rescue_from Doorkeeper::Errors::TokenExpired, with: :handle_doorkeeper_errors
      rescue_from Doorkeeper::Errors::TokenRevoked, with: :handle_doorkeeper_errors
      rescue_from Doorkeeper::Errors::TokenUnknown, with: :handle_doorkeeper_errors
    end

    private

    def handle_doorkeeper_errors exception
      case exception
      when Doorkeeper::Errors::TokenExpired
        error_description = I18n.t('doorkeeper.errors.messages.invalid_token.expired')
      when Doorkeeper::Errors::TokenRevoked
        error_description = I18n.t('doorkeeper.errors.messages.invalid_token.revoked')
      when Doorkeeper::Errors::TokenUnknown
        error_description = I18n.t('doorkeeper.errors.messages.invalid_token.unknown')

        # TODO how to handle these?
      when Doorkeeper::Errors::InvalidToken, Doorkeeper::Errors::TokenForbidden
        error_description = I18n.t('doorkeeper.errors.messages.invalid_token.unknown')
      end

      render json: {
          error: 'Invalid token',
          error_description: error_description
      }, status: 401
    end
  end
end