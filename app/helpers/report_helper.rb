# frozen_string_literal: true

module <PERSON><PERSON><PERSON><PERSON>
  def report_title_translation(report_type)
    if report_type == Report::ReportTypes::PACFS && current_account.saas_json.dig('schema', 'reports', 'pacfs') == 'Annual Dues'
      'Member Annual Dues Report'
    else
      keys = public_send(report_type + '_title')
      (translate_elements(keys) + ['Report']).join(' ')
    end
  end

  def translate_report_columns(report_type, columns = [])
    if report_type == Report::ReportTypes::SINGLE_EMPLOYEE
      translate_single_employee_columns(columns)
    elsif report_type == Report::ReportTypes::BENEFIT_COVERAGES
      translate_single_employee_columns(columns, nil, report_type, show_coverages)
    elsif report_type == Report::ReportTypes::BENEFIT_COVERAGES_EXPIRATION
      translate_benefit_coverage_expiration_columns
    elsif report_type == Report::ReportTypes::BENEFICIARY
      translate_beneficiary_columns
    elsif report_type == Report::ReportTypes::NOTIFICATION_ANALYTICS
      translate_notification_analytics_columns
    elsif report_type == Report::ReportTypes::PACFS && current_account.saas_json.dig('schema', 'reports', 'pacfs') == 'Annual Dues'
      pacfs_amount_columns(columns)
    elsif report_type == Report::ReportTypes::CLASS_ACTION_GRIEVANCE
      translate_single_employee_columns(columns, nil, report_type)
    else
      report_columns = public_send(report_type + '_columns')
      translate_elements(report_columns)
    end
  end

  def benefits_title
    %w[employee benefits]
  end

  def benefit_coverages_title
    %w[employee reports.benefit_coverages]
  end

  def benefit_coverage_expiration_title
    %w[employee reports.benefit_coverages_expiration]
  end

  def beneficiary_title
    %w[employee reports.beneficiary]
  end

  def employee_delegate_assignment_title
    %w[reports.employee_delegate_assignment]
  end

  def lodi_title
    %w[employee reports.lodi]
  end

  def workers_comp_title
    %w[employee reports.workers_comp]
  end

  def sick_bank_title
    %w[employee reports.sick_bank]
  end

  def life_insurances_title
    %w[life_insurances]
  end

  def single_employee_title
    %w[employees]
  end

  def union_meetings_title
    %w[employee reports.union_meetings]
  end

  def pacfs_title
    %w[employee pacf]
  end

  def disciplines_title
    %w[employee discipline_setting]
  end

  def grievances_title
    %w[employee grievance]
  end

  def class_action_grievance_title
    %w[employee grievance]
  end
  def janus_title
    %w[employee reports.janus]
  end

  def notification_analytics_title
    %w[employee notifications.email_message notifications.sms_message]
  end

  def employee_delegate_assignment_columns
    %w[office reports.date_from reports.date_to]
  end

  def lodi_columns
    lodi = %w[reports.name lodis.incident_date lodis.return_date office reports.lodi_return_to_work_status]
    lodi << 'lodis.injury' if current_account.saas_json.dig('ui', 'reports', 'lodi', 'report_columns')&.include?('injury')
    lodi
  end

  def benefit_coverages_columns
    %w[reports.name benefit_coverages.name benefit_coverages.relationship benefit_coverages.social_security_number benefit_coverages.birthday benefit_coverages.expires_at]
  end

  def workers_comp_columns
    %w[reports.name lodis.incident_date lodis.return_date office reports.lodi_return_to_work_status]
  end

  def sick_bank_columns
    %w[reports.name reports.date_in reports.date_out reports.used_hours]
  end

  def life_insurances_columns
    %w[life_insurances.age_group_type life_insurances.member_contributions]
  end

  def union_meetings_columns
    %w[reports.name meeting_type employee_meeting_types.meeting_date employee_meeting_types.attended]
  end

  def pacfs_columns
    %w[reports.name pacf employee_pacfs.date]
  end

  def pacfs_amount_columns(columns)
    (['Name', 'Annual Dues', 'Date'] << columns).flatten
  end

  def disciplines_columns
    headers = %w[reports.name discipline_setting]
    if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').present?
      headers.push('employee_discipline_settings.charge') if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').include?('charge')
      headers.push('employee_discipline_settings.dan_number') if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').include?('dan_number')
    else
      headers
    end
    headers.push('employee_discipline_settings.date')

    headers
  end

  def grievances_columns
    headers = %w[reports.name grievance]
    if current_account.saas_json.dig('ui', 'reports', 'grievances', 'report_columns').present?
      headers.push('employee_grievances.charge') if current_account.saas_json.dig('ui', 'reports', 'grievances', 'report_columns').include?('charge')
      headers.push('employee_grievances.number') if current_account.saas_json.dig('ui', 'reports', 'grievances', 'report_columns').include?('number')
    else
      headers
    end
    headers.push('employee_grievances.date')

    headers
  end

  def janus_columns
    %w[reports.name employees.janus_card employees.janus_card_opt_out_date]
  end

  def translate_benefit_coverages_columns(show_coverages = nil)
    if show_coverages == 'grouped'
      ['Dependent Name', 'Relationship', 'Dependent Birthday', 'Dependent SSN#']
    elsif %w[all true].include?(show_coverages)
      ['Benefits', 'Start Date', 'End Date', 'Dependent Name', 'Relationship', 'Dependent Birthday', 'Dependent SSN#']
    else
      ['Benefits', 'Start Date', 'End Date']
    end
  end

  def translate_benefit_coverage_expiration_columns
    ['Member Name', 'Social Security Number', 'Dependent Name', 'Relationship', 'Dependent SSN#', 'Birthday', 'Age', 'Expiration']
  end

  def translate_beneficiary_columns
    ['Member Last Name', 'Member First Name', 'AMERITAS policy #', 'Primary Benfeficiary Full Name', 'Percentage',
     'Address', 'Relationship', 'DOB', 'Contigent Beneficiary', 'Percentage', 'Address', 'Relationship', 'DOB']
  end

  def translate_single_employee_columns(columns, with_name_index = false, report_type = nil, show_coverages = nil)
    translated_columns = []
    employee_columns = columns.deep_dup
    if report_type == Report::ReportTypes::CLASS_ACTION_GRIEVANCE
      translated_columns.concat(['Grievance #', 'Date', 'Grievance Type'])
      translated_columns << 'Grievance Status' if employee_columns&.delete('employee_grievances.status') == 'employee_grievances.status'
      translated_columns << 'Arbitrator' if employee_columns&.delete('reports.arbitrator_detail') == 'reports.arbitrator_detail'
      translated_columns << 'PA Attorney' if employee_columns&.delete('reports.attorney_detail') == 'reports.attorney_detail'
    end
    name_index = -1
    employee_columns.each_with_index do |column, ind|
      year = Date.today.year

      if (column == "placard_#{year}" || column == "placard_#{year + 1}" || column == "placard_#{year-1}" || column == "placard_#{year-2}") ||
         (column == "placard_#{year - 1} - #{year}" || column == "placard_#{year} - #{year + 1}") &&
        current_account.saas_json.dig('schema', 'employees', "placard_multiple") == true
        case column

        when "placard_#{year - 2}"
          name_index = ind + 1
          translated_columns << "Placard #{year - 2}"
        when "placard_#{year - 1}"
          name_index = ind + 1
          translated_columns << "Placard #{year - 1}"
        when "placard_#{year}"
          name_index = ind + 1
          translated_columns << "Placard #{year}"
        when "placard_#{year + 1}"
          name_index = ind + 1
          translated_columns << "Placard #{year + 1}"
        when "placard_#{year - 1} - #{year}"
          name_index = ind + 1
          translated_columns << "Placard #{year - 1}- #{year}"
        when "placard_#{year} - #{year + 1}"
          name_index = ind + 1
          translated_columns << "Placard #{year} - #{year + 1}"
        end
      else
        translated_columns << if column == 'employee'
                                name_index = ind
                                translation_for(column) + ' Name'
                              else
                                translation_for(column)
                              end
      end
    end
    if report_type == Report::ReportTypes::BENEFIT_COVERAGES
      translated_columns << translate_benefit_coverages_columns(show_coverages)
      translated_columns.flatten!
      translated_columns.prepend('Member Name', 'Shield Number')
    end
    with_name_index ? [translated_columns, name_index] : translated_columns
  end

  def translate_notification_analytics_columns
    ['Member Name', 'Email', 'SMS', 'SMS Error Code']
  end

  private

  def current_account
    Account.find_by(subdomain: Apartment::Tenant.current)
  end
end
