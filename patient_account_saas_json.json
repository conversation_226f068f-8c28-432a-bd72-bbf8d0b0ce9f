{"schema": {"employees": {"key_name": "Patients", "avatar": "Patient Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "<PERSON><PERSON><PERSON><PERSON>", "social_security_number_format": "9", "veteran_status": "Organ donated", "a_number": "A Number", "shield_number": "Patient ID Number", "placard_number": "Salary Account Number", "start_date": "Last visited date", "notes": "Notes", "cellphone": "Personal phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "allow_multiple_present_status": "false", "required_fields": ["name", "address", "first_name", "last_name", "city", "shield_number", "social_security_number", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "placard_number", "street", "start_date", "birthday"]}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}}, "departments": {"key_name": "Hospital Name", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Specialist", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "discipline_settings": {"key_name": "Treatment for", "name": "Name", "description": "Description", "required_fields": ["name"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "grievances": {"key_name": "Grievance", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Unions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "leaves": {"started_at": "From", "ended_at": "To", "notes": "Notes", "required_fields": ["started_at", "leave_type"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "genders": {"key_name": "Gender", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Patient status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_grievances": {"date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "grievance_id"]}, "users": {"key_name": "<PERSON>", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["username", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single patient report", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From date", "ended_at": "To date", "name": "Name", "date_to": "Date to", "date_from": "Date from", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "delegates": "Delegates", "meetings": "Meetings"}, "common_terms": {"employee_analytics": "Admission dates", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}}, "ui": {"employees": {"key_name": "Patient List", "is_search": ["employees"], "table_headers": ["name", "shield_number", "birthday", "start_date", "address"], "tabs": ["profile", "employee_analytics", "awards", "discipline_settings", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "shield_number", "social_security_number", "genders", "marital_statuses", "address", "birthday", "do_not_mail", "notes", "veteran_status", "start_date"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_departments", "employee_sections"]}}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "employment_statuses", "discipline_settings"]}, "users": {"key_name": "<PERSON>", "is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "sick_bank", "lodi", "grievances", "disciplines"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employment_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.cellphone", "employees.home_phone"], ["employees.email", ""]], "columns": ["employees", "employees.social_security_number", "employees.city", "employees.state", "employees.zipcode", "employees.birthday", "genders", "marital_statuses", "employees.start_date", "employment_statuses", "departments", "sections", "meeting_types", "grievances"], "default_columns": ["employees", "employment_statuses", "marital_statuses", "departments", "sections"], "actions": ["single_mailing_label", "multiple_mailing_label", "excel_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employment_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.cellphone", "employees.home_phone"], ["employees.email", ""]], "actions": ["excel_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employment_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.cellphone", "employees.home_phone"], ["employees.email", ""]], "actions": ["excel_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employment_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.cellphone", "employees.home_phone"], ["employees.email", ""]], "actions": ["pdf_report", "excel_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employment_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.cellphone", "employees.home_phone"], ["employees.email", ""]], "actions": ["pdf_report"]}}}}