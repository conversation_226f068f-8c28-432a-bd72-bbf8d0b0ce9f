# Use this file to configure the Overcommit hooks you wish to use. This will
# extend the default configuration defined in:
# https://github.com/brigade/overcommit/blob/master/config/default.yml
#
# At the topmost level of this YAML file is a key representing type of hook
# being run (e.g. pre-commit, commit-msg, etc.). Within each type you can
# customize each hook, such as whether to only run it on certain files (via
# `include`), whether to only display output if it fails (via `quiet`), etc.
#
# For a complete list of hooks, see:
# https://github.com/brigade/overcommit/tree/master/lib/overcommit/hook
#
# For a complete list of options that you can use to customize hooks, see:
# https://github.com/brigade/overcommit#configuration
#
# Uncomment the following lines to make the configuration take effect.

#PreCommit:
#  RuboCop:
#    enabled: true
#    on_warn: fail # Treat all warnings as failures
#
#  TrailingWhitespace:
#    enabled: true
#    exclude:
#      - '**/db/structure.sql' # Ignore trailing whitespace in generated files
#
#PostCheckout:
#  ALL: # Special hook name that customizes all hooks of this type
#    quiet: true # Change all post-checkout hooks to only display output on failure
#
#  IndexTags:
#    enabled: true # Generate a tags file with `ctags` each time HEAD changes

#CommitMsg:
#  CapitalizedSubject:
#    enabled: false
#
#  EmptyMessage:
#    enabled: false
#
#  TrailingPeriod:
#    enabled: true
#
#  TextWidth:
#    enabled: false
#
#PreCommit:
#  ALL:
#    on_warn: fail
#
#  AuthorEmail:
#    enabled: true
#
#  AuthorName:
#    enabled: true
#
#  MergeConflicts:
#    enabled: true
#
#  YamlSyntax:
#    enabled: true
#
#  BundleCheck:
#    enabled: true
#
#  RuboCop:
#    enabled: true
#    flags: ['--format=emacs', '--force-exclusion', '--display-cop-names', '--auto-correct']
#    exclude:
#      - 'db/**/*'
#
#  RubyLint:
#    enabled: false
#    exclude:
#      - 'spec/**/*'
#
#  Reek:
#    enabled: true
#    flags: ['--single-line', '--no-color', '--force-exclusion']
#
#  Fasterer:
#    enabled: true
#
#PrePush:
#  Brakeman:
#    enabled: true
#    flags: ['--exit-on-warn', '--quiet']
#
#  RSpec:
#    enabled: false


verify_signatures: false
PreCommit:
  RuboCop:
    enabled: true
    on_warn: fail
    problem_on_unmodified_line: ignore

  #need to uncomment the below code,It will checks the vulnerabilities on gem.
  BundleAudit:
    enabled: true
    on_warn: fail
    problem_on_unmodified_line: ignore
    command: [ 'bundle', 'audit' ]

#need to uncomment the below code.
PrePush:
  Brakeman:
    enabled: true
    command: [ 'brakeman', '-i', 'brakeman.ignore' ]