# frozen_string_literal: true

desc 'Update Users For the Notification from the Version table'
task :update_user_to_notification_from_version, [:account] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}

  Notification.all.find_each do |notification|
    @row_number = notification.id
    version = UserAudit.where(item_type: 'Notification', item_id: notification.id).first
    next if version.blank? || version.whodunnit.blank?

    notification.update_column(:user_id, version.whodunnit)
  rescue StandardError => e
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_user_to_notification_from_version_errors.csv", 'w') do |csv|
    p csv << ['Row Number', 'Errors']

    @errors.each do |error|
      p csv << error
    end
  end
end

def member_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
