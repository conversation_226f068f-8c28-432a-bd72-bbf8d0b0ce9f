# frozen_string_literal: true

desc 'Format social security number'
task format_ssn: [:environment] do
  ssn_data = {}
  Account.all.each do |account|
    # Skip formatting for SSSA as we save last 4 digits
    next if %w[sssa msssa].include?(account.subdomain)

    account_ssn_data = {}
    p "Formatting data for account - #{account.subdomain}"
    Apartment::Tenant.switch(account.subdomain) do
      Employee.kept.all.find_each do |employee|
        ssn = employee.social_security_number
        ssn = ssn.gsub(' ', '') # Remove whitespaces
        ssn = ssn.gsub('-', '')
        formatted_ssn = []
        formatted_ssn << ssn[0, 3] if ssn[0, 3].present?
        formatted_ssn << ssn[3, 2] if ssn[3, 2].present?
        formatted_ssn << ssn[5, 4] if ssn[5, 4].present?
        formatted_ssn = formatted_ssn.join('-')
        account_ssn_data[employee.id.to_s] = { old_ssn: employee.social_security_number, formatted_ssn: formatted_ssn }
        employee.update_columns(social_security_number: formatted_ssn)
      end
    end
    ssn_data[account.subdomain] = account_ssn_data
  rescue StandardError => e
    p 'Error -' + e.message
  end
  p ssn_data
end
