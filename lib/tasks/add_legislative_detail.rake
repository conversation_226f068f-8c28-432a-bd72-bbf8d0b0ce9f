desc 'Adding the legilstive detail for all the members'
task :add_legislative_detail, [:account, :is_continue] => [:environment] do |_t, args|
  # legislation_domains = []
  # Account.pluck(:subdomain).each do |domain|
  #   legislation_domains << domain if Account.find_by(subdomain: domain).saas_json&.dig("schema", "legislative_details")&.present?
  # end

  ### we should pass the argument with a whitespace like rake 'add_legislative_detail[btoba papba iuoe211, true]'
  # Second Argument is to continue where it is stopped, If you want to continue, need to set true.
  # Here we can single or mutiple domains if needed.
  subdomains = args[:account].split
  subdomains = Account.pluck(:subdomain) if subdomains == 'all'
  subdomains.each do |domain|
    Apartment::Tenant.switch!(domain)
    if args[:is_continue] == 'false'
      Employee.kept.update_all(legislation_rake_update: false)
      Employee.kept.find_each do |employee|
        add_legislative_address(employee)
      end
    else
      Employee.kept.where(legislation_rake_update: false).find_each do |employee|
        add_legislative_address(employee)
      end
    end
  end
end
desc 'Adding the legilstive detail for the active members'
task :add_legislative_detail_to_active_members, [:account, :is_continue] => [:environment] do |_t, args|
  subdomains = args[:account].split
  subdomains.each do |domain|
    Apartment::Tenant.switch!(domain)
    if args[:is_continue] == 'false'
      Employee.kept.includes(:employee_employment_statuses).where(employee_employment_statuses: { employment_status_id: EmploymentStatus.kept.where("lower(name) = ?", 'active').first.id }).find_each do |employee|
        add_legislative_address(employee)
      end
    else
      Employee.kept.includes(:employee_employment_statuses).where(employee_employment_statuses: { employment_status_id: EmploymentStatus.kept.where("lower(name) = ?", 'active').first.id }).where(legislation_rake_update: false).find_each do |employee|
        add_legislative_address(employee)
      end
    end
  end
end

task update_default_other_details_json: :environment do
  ## Here we are updating the other details for the existing legislative details, This JSON will update as default for the new legsilative details.

  legislation_details_json = { senate_member_details: { name: '', website: '', district: '' },
                               congress_member_details: { name: '', website: '', district: '' },
                               assembly_member_details: { name: '', website: '', district: '' },
                               council_member_details: { name: '', website: '', district: '' },
                               comptroller_member_details: { name: '', website: '' },
                               attorney_member_details: { name: '', website: '' },
                               executive_member_details: { name: '', website: '' },
                               county_details: { county_name: '' } }
  LegislativeAddress.where('legislation_details IS NULL').update_all(legislation_details: legislation_details_json)
end

def add_legislative_address(employee)
  legislative_address = get_legislative_address_from_service(employee)
  employee.update_columns(legislative_address_id: legislative_address&.id, legislation_rake_update: true)
  sleep(1)
end

# rubocop:disable Metrics/PerceivedComplexity, Metrics/AbcSize
def get_legislative_address_from_service(employee)
  street = employee.street&.downcase || ''
  city = employee.city&.downcase || ''
  state = employee.state&.downcase || ''
  zipcode = employee.zipcode
  legislative_address = LegislativeAddress.where('lower(street) = ? and lower(city) = ? and lower(state) = ? and
                                  zipcode = ?', street, city, state, zipcode).order(updated_at: :desc)&.first

  return legislative_address if legislative_address.present? && legislative_address.response_code == 200 && legislative_address.updated_at.today?

  LegislativeDetailService.new(employee).fetch
end

# rubocop:enable Metrics/PerceivedComplexity, Metrics/AbcSize

# bundle exec rake 'add_legislative_detail[nyccoba, false]'
# bundle exec rake 'add_legislative_detail_to_active_members[nyccoba, false]'
