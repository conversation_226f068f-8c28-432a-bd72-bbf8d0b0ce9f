# frozen_string_literal: true

require 'csv'
desc 'Send Member Credentials'
task :utlo_send_member_credential_mailer, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  employment_statuses = ['Active - Dues Paying', 'Active - Non-Dues Paying', 'Active- Not Dues Eligible',
                         'Active - Submitted to BSC', 'Benevolent - Dues Paying', 'Benevolent - Non-Dues Paying',
                         'Recruit- Bridges and Tunnels', 'Recruit- Director/Manager', 'Recruit- LIRR', 'Retiree - Dues Paying',
                         'Retiree - Non-Dues Paying']
  employees = Employee.includes(:employee_employment_statuses).where('employee_employment_statuses.employment_status_id in (?) and
                                  (employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?) and
                                  employees.email_opt_out = ? and employees.enable_mobile_access = ?',
                                                                     EmploymentStatus.where('name in (?)', employment_statuses).pluck(:id), Date.today, false, true).references(:employee_employment_statuses)
  @mail_sented_members = []
  @errors = {}

  employees.each do |employee|
    @row_number = employee.full_name
    employment_status_name = []
    employee.employee_employment_statuses.where('end_date is NULL or end_date > ?', Date.today).each do |employment_status|
      employment_status_name << employment_status.employment_status.name
    end
    excluded_status = employment_status_name - employment_statuses
    if excluded_status.present?
      member_feed_errors('Other statuses are also active, not among the given statuses.')
      next
    end

    personal_email = employee.contacts.where(contact_type: 'email', contact_for: 'personal').first
    if personal_email&.value.blank?
      member_feed_errors('Mail is blank')
      next
    end
    password = Devise.friendly_token(8)
    username = (employee.username.present? ? employee.username : personal_email.value)
    employee.assign_attributes(username: username, password: password, password_confirmation: password)
    employee.save!(validate: false)
    if MemberCredentialsMailer.details(username, password, personal_email.value, 'utlo').deliver_later
      @mail_sented_members << [employee.full_name, employment_status_name.join(', ')]
    end
  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_utlo_send_member_credential_mailer_errors.csv", 'w') do |csv|
    csv << ['Errors']

    @errors.each do |error|
      csv << error
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_mail_succeed_members.csv", 'w') do |csv|
    csv << ['Member Full Name', 'Member Status']

    @mail_sented_members.each do |name|
      csv << [name.first, name.second]
    end
  end
end
