# frozen_string_literal: true

require 'csv'

desc 'import data'
task :utlo_import_member_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # Member Union Membership Type,Unit,Member Department,Member Title,Member First Name,Member Last Name,Member Middle Initial,Member Suffix,Member Pass Number,Member BSC ID,Member SS Number,Division,Sub-Division,Member Tour of Duty,Primary Work Location,Member Street 1,Apt,Member City,Member State,Member Zip,Personal Phone,Personal Email,Member Date Of Birth 2,Member Gender,field-2,Member RDO,Member MTA Start Date

  csv_file.each do |row|

    shield_number = row['Member BSC ID']

    @row_number = shield_number

    membership_type = check_membership_type(row['Member Union Membership Type'])
    unit = check_unit(row['Unit'])
    office = check_office(row['Member Department'])
    title = check_rank(row['Member Title'])
    department = check_department(row['Division'])
    section = check_section(row['Sub-Division'], department.id) if department
    tour_of_duty = check_tour_of_duty(row['Member Tour of Duty'])
    gender = check_gender(row['Member Gender'])
    marital_status = check_marital_status(row['field-2'])
    dob = parse_date(row['Member Date Of Birth 2'], 'DOB')
    start_date = parse_date(row['Member MTA Start Date'], 'Member Start Date')
    cell_phone = parse_phone(row['Personal Phone'])
    zip_code = row['Member Zip']
    zip_code = zip_code.rjust(5, '0') if zip_code.present?

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = if shield_number.present?
                 Employee.where(shield_number: shield_number).first_or_initialize
               else
                 Employee.new
               end

    last_name = row['Member Last Name']
    last_name += ' ' + row['Member Suffix'].strip if row['Member Suffix'].present?

    employee.first_name = row['Member First Name']
    employee.last_name = last_name
    employee.middle_name = row['Member Middle Initial'] || ''
    employee.a_number = row['Member Pass Number'] || ''
    employee.shield_number = shield_number || ''
    employee.social_security_number = row['Member SS Number'] || ''
    employee.primary_work_location = row['Primary Work Location'] || ''
    employee.street = row['Member Street 1'] || ''
    employee.apartment = row['Apt'] || ''
    employee.city = row['Member City'] || ''
    employee.state = row['Member State'] || ''
    employee.zipcode = zip_code || ''
    employee.rdo = row['Member RDO'] || ''
    employee.birthday = dob
    employee.start_date = start_date
    employee.unit = unit if unit
    employee.gender_id = gender.id if gender
    employee.marital_status_id = marital_status.id if marital_status
    employee.tour_of_duty_id = tour_of_duty.id if tour_of_duty
    employee.platoon_id = membership_type.id if membership_type

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: cell_phone).save! if cell_phone
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: row['Personal Email']).save! if row['Personal Email'].present?

      employee.employee_offices.new(office_id: office.id).save(validate: false) if office
      employee.employee_departments.new(department_id: department.id).save(validate: false) if department
      employee.employee_sections.new(department_id: department.id, section_id: section.id).save(validate: false) if section
      employee.employee_ranks.new(rank_id: title.id).save(validate: false) if title
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def check_unit(unit_name)
  return nil unless unit_name.present?

  unit = Unit.where(name: unit_name).first_or_create

  unit

rescue => e
  feed_errors('UNIT ' + unit.errors.full_messages)
end

def check_marital_status(marital_status_name)
  return nil unless marital_status_name.present?

  marital_status = MaritalStatus.where(name: marital_status_name).first_or_create

  marital_status

rescue => e
  feed_errors('MARITAL STATUS ' + marital_status.errors.full_messages)
end

def check_membership_type(membership_type_name)
  return nil unless membership_type_name.present?

  membership_type = Platoon.where(name: membership_type_name).first_or_create

  membership_type

rescue => e
  feed_errors('MEMBERSHIP TYPE ' + membership_type.errors.full_messages)
end

def check_office(office_name)
  return nil unless office_name.present?

  office = Office.where(name: office_name).first_or_create

  office

rescue => e
  feed_errors('OFFICE ' + office.errors.full_messages)
end

def check_department(department_name)
  return nil unless department_name.present?

  department = Department.where(name: department_name).first_or_create

  department

rescue => e
  feed_errors('DEPARTMENT ' + department.errors.full_messages)
end

def check_section(section_name, department_id)
  return nil unless section_name.present?

  section = Section.where(department_id: department_id, name: section_name).first_or_create

  section

rescue => e
  feed_errors('SECTION ' + section.errors.full_messages)
end

def check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create

  rank

rescue => e
  feed_errors('RANK ' + rank.errors.full_messages)
end

def check_tour_of_duty(tod_name)
  return nil unless tod_name.present?

  tod = TourOfDuty.where(name: tod_name).first_or_create

  tod

rescue => e
  feed_errors('TOUR OF DUTY ' + tod.errors.full_messages)
end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
