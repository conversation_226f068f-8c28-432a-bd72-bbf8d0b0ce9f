# frozen_string_literal: true

require 'csv'

desc 'import data'
task :utlo_update_status_by_dues_status, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    @row_number = row['BSC ID']
    shield_number = @row_number
    emp_pacf_status = row['Dues Payments'].downcase

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = if shield_number.present?
                 Employee.where(shield_number: shield_number).first
               else
                 feed_errors('Employee not found')
               end

    employment_statuses = employee.employee_employment_statuses.where('end_date is NUll OR end_date >= ?', Date.today)
    name = employment_statuses.first.name.split(' - ') if employment_statuses.present?

    due_id = EmploymentStatus.kept.where(name: "#{name[0]} - Dues paying").first.id if name.present?
    non_due_id = EmploymentStatus.kept.where(name: "#{name[0]} - Non-Dues paying").first.id if name.present?

    if emp_pacf_status == "dues paying"
      employee_employment_status = employment_statuses.where('employment_status_id = ?', non_due_id)
      employee_employment_status.update(employment_status_id: due_id) if employee_employment_status.present?
    elsif emp_pacf_status == "non-dues paying"
      employee_employment_status = employment_statuses.where('employment_status_id = ?', due_id)
      employee_employment_status.update(employment_status_id: non_due_id) if employee_employment_status.present?
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_status_by_dues_status_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
