# frozen_string_literal: true

require 'csv'

desc 'import data'
task :scpc_add_and_update_contacts, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail

  csv_file.each do |row|
    cell_number = parse_phone(row['Phone Number'])
    first_name = row['First Name']&.strip || ''
    last_name = row['Last Name']&.strip || ''
    email = row['Email']
    @row_name = "#{first_name}_#{last_name}"

    employees = Employee.kept.where('LOWER(first_name) = ? AND LOWER(last_name) = ?', first_name.downcase, last_name.downcase)
    if employees.blank?
      create_new_employee(first_name, last_name, cell_number, email)
    elsif employees.count == 1
      employee_instance = employees.first
      if employee_instance.contacts.blank?
        scpc_create_contacts(employee_instance, cell_number, email)
      else
        employee_instance.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).first.update!(value: email)
        employee_instance.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).first.update!(value: cell_number)
      end
    else
      store_errors('Multiple member found same name')
    end
  end
  p '******************************Completed**********************************'
  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.to_i}_#{file_name}_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |row_number, error_messages|
      csv << [row_number, error_messages.join(';')]
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
end

task :scpc_sms_optout_checkbox_checked, %i[account file_path] => :environment do |_task, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  csv_file.each do |row|
    name = row['Member Name']
    first_name, last_name = split_by_name(name)
    @row_name = "#{first_name} #{last_name}"

    employees = Employee.kept.where("LOWER(REPLACE(REPLACE(first_name, ',', ''), '.', '')) = ? AND LOWER(REPLACE(REPLACE(last_name, ',', ''), '.', '')) = ?", first_name.gsub(/[.,]/, '').downcase, last_name.gsub(/[.,]/, '').downcase)
    if employees.count == 1
      begin
        employees.first.update_column(:sms_opt_out, true)
      rescue StandardError => e
        store_errors("Failed to update SMS opt-out for #{@row_name}: #{e.message}")
      end
    elsif employees.count > 1
      store_errors('Multiple Employee Found')
    else
      store_errors('Employee not found')
    end
  end
  p '******************************Completed**************************************'

  file_name = args[:file_path].split('.').first
  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.to_i}_#{file_name}_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |row_number, error_messages|
      csv << [row_number, error_messages.join(';')] # Join errors into a single string
    end
  end
end

task :scpc_new_member_import, %i[account file_path] => :environment do |_task, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  # I add union status outside of loop because it is same for all employees
  union_status = EmploymentStatus.kept.where('lower(name) = ?', 'active').first_or_create!(name: 'Active')

  csv_file.each do |row|
    first_name = row['First Name']&.strip || ''
    last_name = row['Last Name']&.strip || ''
    email = row['Email']&.strip || ''
    cell_number = parse_phone(row['Cell']) || ''
    affiliation = row['Union Affiliation']&.strip || ''
    union_affiliation = Position.kept.where('lower(name) = ?', affiliation.downcase).first_or_create!(name: affiliation) unless affiliation.blank?
    @row_name = "#{first_name} #{last_name}"
    next if first_name.blank? || last_name.blank?

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
    if employees.count == 1
      employee = employees.first
      employee.employee_positions.find_or_create_by(position_id: union_affiliation.id) unless union_affiliation.blank?
      employee.employee_employment_statuses.find_or_create_by(employment_status_id: union_status.id)

      if email.present?
        email_contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
        email_contact.value = email
        email_contact.save!
      end

      if cell_number.present?
        phone_contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
        phone_contact.value = cell_number
        phone_contact.save!
      end
    elsif employees.count > 1
      store_errors('Multiple Employee Found')
    else
      create_new_employee(first_name, last_name, cell_number, email, union_affiliation, union_status)
    end
  end
  p '******************************Completed**********************************'
  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.to_i}_#{file_name}_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |row_number, error_messages|
      csv << [row_number, error_messages.join(';')]
    end
  end
  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
end

def split_by_name(name)
  name = name.split
  if name.length == 2
    first_name = name.first
    last_name = name.last
  elsif name.length == 3
    if name[1].length <=3
      first_name = name[0..1].join(' ')
      last_name = name.last
    else
      first_name = name.first
      last_name = name[1..].join(' ')
    end
  else
    first_name = name[0..1].join(' ')
    last_name = name[2..-1].join(' ')
  end
  [first_name, last_name]
end

def create_new_employee(first_name, last_name, cell_number, email, union_affiliation, union_status)
  new_employee = Employee.new(first_name: first_name, last_name: last_name)
  scpc_create_contacts(new_employee, cell_number, email) if new_employee.save

  new_employee.employee_employment_statuses.create!(employment_status: union_status)
  new_employee.employee_positions.create!(position: union_affiliation)
rescue StandardError => e
  store_errors("Failed to create new employee: #{e.message}")
end

def scpc_create_contacts(employee, cell_number, email)
  employee.contacts.create!(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '')

  employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).first.update!(value: cell_number)
  employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).first.update!(value: email)
rescue StandardError => e
  p "#{@row_name} #{e.message}"
  store_error(e.message)
end

def store_errors(message)
  if @errors[@row_name]
    @errors[@row_name] << message
  else
    @errors[@row_name] = [message]
  end
end

# bundle exec rake 'scpc_sms_optout_checkbox_checked[scpc, scpc_data.csv]'
# bundle exec rake 'scpc_new_member_import[scpc,scpc_new_member_import.csv]'
