# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nassaucoba_update_retiree_dob, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    dob = parse_date(row['D.O.B.'], 'DOB') if row['D.O.B.']

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    @first_name = row['First Name']
    @last_name = row['Last Name']

    employee = Employee.where(first_name: @first_name, last_name: @last_name).first

    if employee.present?
      employee.birthday = dob
      if employee.save
        @errors[@first_name] = employee.errors.full_messages
      end
    end


  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_retirees_members_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nassucoba_update_personal_email, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @row_number = row['Member Name']

    employee = Employee.where('lower(first_name) LIKE ? and lower(last_name) LIKE ?', "%#{row['Member Name'].split.first.downcase}%", "%#{row['Member Name'].split.last.downcase}%")
    if employee&.blank?
      nassaucoba_feed_errors('Member Not Found')
      next
    elsif employee&.present? && employee.count > 1
      nassaucoba_feed_errors('More than One Employee Found')
      next
    elsif employee&.present? && employee.count == 1
        personal_email = employee.first.contacts.where(contact_for: "personal", contact_type: "email").first_or_initialize
        personal_email.value = row['Email']
        personal_email.save!
    end
  rescue => e
    p @row_number, e.message
    nassaucoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_personal_email_erros.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def feed_errors(message)
  @errors[@first_name] = [@last_name, message]
end

def nassaucoba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

