# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nassaucoba_update_emp_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @first_name = row['First Name']
    @last_name = row['Last Name']
    @last_name += ' ' + row['Suffix'].strip if row['Suffix'].present?
    @mi = row['Middle Initial'] || ''

    emp_id_no = row['EmpIdNo'] || ''
    lpd = row['Leave Progession Date']
    unit_name = row['Unit/Platoon']
    shield_num = row['ShieldNum'] || ''
    unit = check_unit(unit_name)
    leave_progression_date = parse_string_date(lpd, 'Leave Progression Date')


    employees = Employee.where('first_name ilike ? and last_name ilike ? and middle_name ilike ?', @first_name, @last_name, @mi)

    if employees.present?
      if employees.count > 1
        feed_errors('Multiple occurrences for name')
      else

        employee = employees.first
        employee.payroll_id = emp_id_no
        employee.leave_progression_date = leave_progression_date
        employee.shield_number = shield_num
        employee.unit = unit if unit
        unless employee.save
          feed_errors(employee.errors.full_messages)
        end
      end
    else
      feed_errors("Employee doesn't exist")
    end

  rescue => e
    p @first_name, @last_name, @mi, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_details_import_errors.csv", 'w') do |csv|
    csv << ["First Name", "Last Name", "Middle Name", "Error"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_unit(unit_name)
  return nil unless unit_name.present?

  unit = Unit.where(name: unit_name).first_or_create

  unit

rescue => e
  feed_errors('UNIT ' + unit.errors.full_messages)
end

def parse_string_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('')
    year = date_array[0..1].join.to_i
    month = date_array[2..3].join.to_i
    day = date_array[4..5].join.to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def feed_errors(message)
  @errors[@first_name] = [@last_name, @mi, message]
end
