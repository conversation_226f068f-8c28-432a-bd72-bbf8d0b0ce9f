# frozen_string_literal: true

require 'csv' ''

desc 'import data'
task :coba_supplemental_insurance, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @row_number = row['NAME']
    name = @row_number.split(' ')
    first_name = if name[1][-1, 1] == ',' || name[1][-1, 1] == '.'
                    name[1][0..-2]
                  else
                    name[1]
                  end
    last_name = if name[0][-1, 1] == ',' || name[0][-1, 1] == '.'
                   name[0][0..-2]
                 else
                   name[0]
                 end
    gender = coba_gender(row['SEX']) if row['SEX']
    amount = amount_filter(row['SUPPLEMENTAL LIFE AMOUNT']) if row['SUPPLEMENTAL LIFE AMOUNT']
    employee_id = row['EMPLOYEE ID'].rjust(10, '0')

    employees = Employee.where(payroll_id: employee_id)

    if employees.present?

      employee = if employees.count > 1
                   employees.where("first_name ilike ? and last_name ilike ? and gender_id = ?", (first_name + '%'), (last_name + '%'), gender.id).first
                 else
                   employees.first
                 end

      life_insurances = employee.life_insurances.where(insurance_type: 'supplemental')

      if life_insurances.present?
        life_insurances.first.update(amount: amount, start_date: '05/08/2021')
      else
        employee.life_insurances.create(insurance_type: 'supplemental', amount: amount, start_date: '05/08/2021')
      end
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_coba_supplemental_insurance.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def coba_gender(gender_type)
  return nil unless gender_type.present?

  gender_type = if gender_type == 'M'
                  ['Male', 'M']
                elsif gender_type == 'F'
                  ['Female', 'F']
                else
                  gender_type
                end

  gender = Gender.where(name: gender_type).first

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def amount_filter(amount)
  return 0.00 unless amount.present?

  parsed_amount = amount.gsub(/\W+/, "").delete('US')[0..-3].to_f

  parsed_amount
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
