# frozen_string_literal: true

require 'csv' ''

desc 'import data'
task :coba_basic_insurance, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @row_number = row['NAME']
    name = @row_number.split(' ')

    first_name = if name[1][-1, 1] == ',' || name[1][-1, 1] == '.'
                    name[1][0..-2]
                  else
                    name[1]
                  end
    last_name = if name[0][-1, 1] == ',' || name[0][-1, 1] == '.'
                   name[0][0..-2]
                 else
                   name[0]
                 end
    gender = coba_gender(row['SEX']) if row['SEX']
    ssn = row['SOCIAL SECURITY'].present? ? row['SOCIAL SECURITY'].split('-') : nil

    employees = Employee.where("first_name ilike ? and last_name ilike ?", (first_name + '%'), (last_name + '%'))

    if employees.present?
      employee = if employees.count > 1
                   employees.where(gender_id: gender.id, social_security_number: ssn[2]).first
                 else
                   employees.first
                 end

      life_insurances = employee.life_insurances.where(insurance_type: 'basic')
      if life_insurances.present?
        life_insurances.first.update(amount: 15000.0, start_date: '05/08/2021')
      else
        employee.life_insurances.create(insurance_type: 'basic', amount: 15000.0, start_date: '05/08/2021')
      end
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_coba_basic_insurance.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def coba_gender(gender_type)
  return nil unless gender_type.present?

  gender_type = if gender_type == 'M'
                  ['Male', 'M']
                elsif gender_type == 'F'
                  ['Female', 'F']
                else
                  gender_type
                end

  gender = Gender.where(name: gender_type).first

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
