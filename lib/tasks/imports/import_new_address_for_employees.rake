# frozen_string_literal: true

require 'csv'

desc 'Import Employee Address'
task :import_employee_address, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  Employee.skip_callback :commit, :after, :update_legislative_detail

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    next if first_name.blank? || last_name.blank?

    street = row['Home Address'] || ''
    apartment = row['APT'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zipcode = row['Zip Code'] || ''
    @employee_name = "#{first_name} #{last_name}"

    if street.blank? || city.blank? || state.blank? || zipcode.blank?
      employee_feed_errors('Mandatory Details missing in the file')
      next

    end

    employees = Employee.kept.where('lower(first_name) LIKE ? and lower(last_name) LIKE ?', "%#{first_name.downcase}%", "%#{last_name.downcase}%")

    valid_employee_count = nyccoba_validate_employee_counts(employees)

    next unless valid_employee_count

    employee = employees.first

    ActiveRecord::Base.transaction do
      employee.update(street: street, apartment: apartment, city: city, state: state, zipcode: zipcode)
    end

  rescue StandardError => e
    p @employee_name, e.message
    employee_feed_errors(e.message)
  end

  nyccoba_generate_csv_report_errors(args[:file_path])

  Employee.set_callback :commit, :after, :update_legislative_detail
end

def nyccoba_validate_employee_counts(employees)
  if employees.count == 1
    true
  elsif employees.count > 1
    employee_feed_errors('More than one Employee Found')
    false
  else
    employee_feed_errors('Invalid Employee or Employee was missing')
    false
  end
end

def employee_feed_errors(message)
  if @errors[@employee_name].present?
    @errors[@employee_name] << message
  else
    @errors[@employee_name] = [message]
  end
end

def nyccoba_generate_csv_report_errors(file_name)
  CSV.open("#{Rails.root}/#{file_name.gsub('.csv', '')}_errors.csv", 'w') do |csv|
    csv << ['Employee Name', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

# bundle exec rake "import_employee_address[nyccoba, nyccoba_employee_address_import.csv]"
# bundle exec rake "import_employee_address[nyccoba, COBA New Addresses 1.csv]"
# bundle exec rake "import_employee_address[nyccoba, COBA New Addresses 2.csv]"
# bundle exec rake "import_employee_address[nyccoba, COBA New Addresses 3.csv]"
