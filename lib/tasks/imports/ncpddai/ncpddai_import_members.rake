# frozen_string_literal: true

require 'csv'

# bundle exec rails 'ncpddai_import_member[ncpddai,ncpddai_members.csv]'
# bundle exec rails 'ncpddai_import_contact_list[ncpddai,ncpddai_contact_list.csv]'

desc 'import data'
task :ncpddai_import_member, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # STATUS,Grade,LAST NAME,FIRST NAME,MIDDLE INITIAL,GENDER,LM,DOB,SS#,Job Date,DD Date,RETIREMENT DATE,SERIAL #,SHEILD #,COMMAND,RANK,DELEGATE,ADDRESS,CITY,STATE,POSTAL CODE,INSURANCE,SUPPLEMENTAL,HOME PHONE,CELL,PCNY,SPOUSE,LEN INVITE,EMAIL ADDRESS

  csv_file.each do |row|
    last_name = row['LAST NAME']
    first_name = row['FIRST NAME']
    middle_name = row['MIDDLE INITIAL'] || ''

    @row_number = [first_name, last_name, middle_name]

    employment_status = check_employment_status(row['STATUS'])
    grade = ncppdai_check_grade(row['Grade'])
    gender = ncppdai_check_gender(row['GENDER'])
    payment_type = ncppdai_check_payment_type(row['LM']) # Payment Type
    dob = ncppdai_parse_date(row['DOB'], 'DOB')
    ssn = ncppdai_parse_ssn(row['SS#'])
    job_date = ncppdai_parse_date(row['Job Date'], 'Job Date') # Date of Appointment
    dd_date = ncppdai_parse_date(row['DD Date'], 'DD Date')
    retirement_date = ncppdai_parse_date(row['RETIREMENT DATE'], 'Retirement Date') # Member with the Employment Status of Retired
    serial_number = row['SERIAL #']
    shild_number = row['SHEILD #']
    office = ncppdai_check_office(row['COMMAND'])
    rank = ncppdai_check_rank(row['RANK'])
    zip_code = row['POSTAL CODE']
    zip_code = zip_code.length > 5 ? zip_code[0..4] : zip_code.rjust(5, '0') if zip_code.present?
    home_phone = ncppdai_parse_phone(row['HOME PHONE'])
    cell_phone = ncppdai_parse_phone(row['CELL'])
    benefit_type = ncppdai_check_benefit_type(row['PCNY'])
    spouse = row['SPOUSE']
    personal_email = row['EMAIL ADDRESS'] || ''

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = middle_name
    employee.birthday = dob || ''
    employee.social_security_number = ssn || ''
    employee.member_start_date = job_date || ''
    employee.start_date = dd_date || ''
    employee.a_number = serial_number || ''
    employee.shield_number = shild_number || ''
    employee.street = row['ADDRESS'] || ''
    employee.apartment = ''
    employee.city = row['CITY'] || ''
    employee.state = row['STATE'] || ''
    employee.zipcode = zip_code || ''
    employee.title_code = spouse || ''

    employee.gender_id = gender.id if gender
    employee.tour_of_duty_id = grade.id if grade

    if employee.save(validate: false)
      home_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE)
      home_phone_contact.value = home_phone if home_phone
      home_phone_contact.save!

      personal_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
      personal_phone_contact.value = cell_phone if cell_phone
      personal_phone_contact.save!

      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save(validate: false)

      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: personal_email).save! if personal_email
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save(validate: false)

      if employment_status.present?
        employee_employment_statuses = employee.employee_employment_statuses.new(employment_status_id: employment_status.id)
        employee_employment_statuses.start_date = retirement_date if employment_status.name.downcase == 'retired' && retirement_date.present?
        employee_employment_statuses.save(validate: false)
      end
      employee.employee_ranks.new(rank_id: rank.id).save(validate: false) if rank
      employee.employee_offices.new(office_id: office.id).save(validate: false) if office
      employee.employee_pacfs.new(payment_type_id: payment_type.id).save(validation: false) if payment_type
      employee.employee_benefits.new(benefit_id: benefit_type.id, start_date: ncppdai_parse_date('1/1/22', 'Start Date'), end_date: ncppdai_parse_date('12/31/22', 'End Date')).save(validate: false) if benefit_type
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    ncppdai_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end

end

task :ncpddai_import_contact_list, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  # Mailing list Category,Company,Title,First Name,Last Name,Address,City,State,Postal Code,Work Number,Email Address

  csv_file.each do |row|
    last_name = row['Last Name']
    first_name = row['First Name']

    @row_number = [first_name, last_name]

    department = ncppdai_check_department(row['Mailing list Category']) # departments
    unit = ncppdai_check_unit(row['Title']) # units
    company = row['Company'] # primary_work_location
    address = row['Address']
    city = row['City'] || ''
    state = row['State'] || ''
    zip_code = row['Postal Code']
    zip_code = zip_code.length > 5 ? zip_code[0..4] : zip_code.rjust(5, '0') if zip_code.present?
    work_phone = ncppdai_parse_phone(row['Work Number'])
    work_email = row['Email Address']

    employee = Employee.new
    employee.first_name = first_name
    employee.last_name = last_name
    employee.street = address
    employee.apartment = ''
    employee.city = city
    employee.state = state
    employee.zipcode = zip_code || ''
    employee.category = 'contact_person'
    employee.primary_work_location = company
    employee.department = department if department
    employee.unit = unit if unit

    if employee.save(validate: false)
      employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save(validate: false)
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).save(validate: false)

      work_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE)
      work_phone_contact.value = work_phone if work_phone
      work_phone_contact.save!

      personal_email_contact = employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL)
      personal_email_contact.value = work_email if work_email.present?
      personal_email_contact.save!

      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).save(validate: false)
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    ncppdai_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_contact_list_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end
end

task :update_county_for_employees, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('ncpddai')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = []
  county = ""
  counties = ["BRONX","BROOKLYN","MANHATTAN","QUEENS","STATEN ISLAND"]
  csv_file.each do |row|
    zip_code = row['Zip_code']
    county_1 = row['County_1']
    county_2 = row['County_2']
    employees = Employee.where(zipcode: zip_code)
    if counties.include?(county_1)
      county = county_1
    end
    if zip_code.present? && (county_1.present? || county_2.present?)
      employees.update(county: ("#{county} County").titleize)
    end

  rescue => e
    p @row_number, e.message
    ncppdai_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_update_county_for_employees_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :update_county_for_suffolk_and_nassau, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('ncpddai')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = []
  csv_file.each do |row|

    zip_codes = row['Zip_code'].split(',').map(&:strip)
    file_name = File.basename(args[:file_path], ".*").downcase
    county = file_name.include?('nassau') ? "Nassau County" : 'Suffolk County'

    zip_codes.each do |zip_code|
      employees = Employee.where(zipcode: zip_code)
      employees.update_all(county: county)
    end
  rescue => e
    p @row_number, e.message
    ncppdai_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_update_county_for_suffolk_and_nassau_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end
def ncppdai_check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender
end

def ncppdai_check_office(command)
  return nil unless command.present?

  office = Office.where(name: command).first
  unless office.present?
    office = Office.new(name: command)
    office.save(validate: false)
  end

  office
end

def ncppdai_check_grade(grade_name)
  grade = if grade_name.present?
            TourOfDuty.where(name: grade_name).first_or_create
          else
            nil
          end

  grade
end

def ncppdai_check_benefit_type(benefit_type_name)
  benefit_type = if benefit_type_name.present? && benefit_type_name.downcase == "yes"
                   Benefit.where(name: 'PCNY').first_or_create
                 else
                   nil
                 end

  benefit_type
end

def ncppdai_check_rank(rank_name)
  rank = if rank_name.present?
           Rank.where(name: rank_name).first_or_create
         else
           nil
         end

  rank
end

def check_employment_status(employment_status_name)
  employment_status_name = if employment_status_name.present?
                             EmploymentStatus.where(name: employment_status_name).first_or_create
                           else
                             nil
                           end

  employment_status_name
end

def ncppdai_check_unit(unit_name)
  return nil unless unit_name.present?

  unit = Unit.where(name: unit_name).first_or_create

  unit

rescue => e
  feed_errors('UNIT ' + unit.errors.full_messages)
end

def ncppdai_check_department(department_name)
  return nil unless department_name.present?

  department = Department.where(name: department_name).first_or_create

  department

rescue => e
  feed_errors('DEPARTMENT ' + department.errors.full_messages)
end

def ncppdai_check_payment_type(type)
  if type.present?
    if type == 'DW'
      payment_type = PaymentType.where(name: 'Annual').first_or_create
    elsif type == "DW100"
      payment_type = PaymentType.where(name: 'Annual $100').first_or_create
    elsif type == "Y"
      payment_type = PaymentType.where(name: 'Lifetime Member').first_or_create
    elsif type == "N"
      payment_type = PaymentType.where(name: 'Pays Dues').first_or_create
    elsif type == "O"
      payment_type = PaymentType.where(name: 'Over 90').first_or_create
    else
      payment_type = PaymentType.where(name: type).first_or_create
    end
  else
    payment_type = nil
  end

  payment_type
end

def ncppdai_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  ncppdai_feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def ncppdai_parse_phone(phone)
  return nil unless phone.present?

  phone.remove!(/\D/).first(10)
  phone.length == 7 ? phone.prepend('516') : phone
  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  ncppdai_feed_errors('PHONE ' + e.message)
end

def ncppdai_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('(', ')', ' ', '-')

  ssn = ssn.rjust(9, '0')

  social_security = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)

  social_security
rescue => e
  ncppdai_feed_errors('SSN ' + e.message)
end

def ncppdai_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
