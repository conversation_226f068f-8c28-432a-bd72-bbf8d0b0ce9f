# frozen_string_literal: true

require 'csv'

desc 'import data'

task :local342_members_import, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['FIRST NAME']&.strip || ''
    last_name = row['LAST NAME']&.strip || ''
    ssn = local342_parse_ssn(row['SS#']) || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['SS#']).blank?

      local342_feed_errors('Mandatory details not present')
      next
    end
    employee = Employee.find_or_initialize_by(social_security_number: ssn)
    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = row['M.I.'] || ''
    employee.street = row['ADDRESS LINE 1'] || ''
    employee.apartment = row['ADDRESS LINE 2'] || ''
    employee.city = row['CITY'] || ''
    employee.state = row['ST'] || ''
    employee.zipcode = row['ZIP CODE']&.slice(0, 5) || ''

    if employee.save
      contacts_hash.last[:value] = row['EMAIL ADDRESS']
      contacts_hash.last(2).first[:value] = local342_parse_phone(row['CELL NO.']) || ''

      employee.contacts.import contacts_hash
    else
      @errors[@row_number] = employee.errors.full_messages
    end
  rescue StandardError => e
    p @row_number, e.message
    local342_feed_errors(e.message)
  end

  generate_local342_error_report_csv(t.name)

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :local342_member_update, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors ||= {}
  gender_hash = {}
  marital_status_hash = {}
  employment_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'active').pluck(:id).first
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['FIRST NAME'] || ''
    last_name = row['LAST NAME'] || ''
    suffix = row['SUFFIX'] || ''
    @row_number = [first_name, last_name]
    if first_name.blank? || last_name.blank?
      local342_feed_errors('Mandatory details not present')
      next
    end
    middle_name = row['MIDDLE INITIAL'] || ''
    ssn_suffix = row['SOC SEC NBR']&.slice(-4..-1) || ''

    employees = get_employees_with_suffix(first_name, last_name, suffix) if suffix.present?
    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase) if employees.blank?
    employees = Employee.kept.where('lower(first_name) LIKE ?  and lower(last_name) LIKE ?', "%#{first_name.downcase}%", "%#{last_name.downcase}%") if employees.blank?
    employees = Employee.kept.where('last_name = ? AND RIGHT(social_security_number, 4) = ?', last_name, ssn_suffix) if employees.blank?
    employees = employees.where('lower(middle_name) = ?', middle_name.downcase) if employees.count > 1

    ActiveRecord::Base.transaction do
      if employees.blank?
        employee = Employee.new
        employee.first_name = first_name
        employee.last_name = suffix.present? ? "#{last_name} #{suffix}" : last_name
        employee.middle_name = row['MIDDLE INITIAL'] || ''
        # employee.zipcode = row['ZIP CODE']&.slice(0, 5) || ''

        if employee.save!
          employee.employee_employment_statuses.new(employment_status_id: employment_status_id).save(validate: false)
          local342_feed_errors('Member Was Imported from Census File')
        else
          @errors[@row_number] = employee.errors.full_messages
        end
      elsif employees.count > 1
        local342_feed_errors('More than one employee found')
        next
      end

      employee = employees.first if employee.blank?
      birthday = local342_parse_date(row['BIRTH DATE'])
      gender_id = if row['SEX'].present? && gender_hash[@value_downcase.call(row['SEX']).to_sym].present?
                    gender_hash[@value_downcase.call(row['SEX']).to_sym]
                  elsif row['SEX'].present?
                    create_associated_model_and_values('Gender', row['SEX'], gender_hash)&.id
                  end
      marital_status_id = if row['MARITAL STATUS'].present? && marital_status_hash[@value_downcase.call(row['MARITAL STATUS']).to_sym]
                            marital_status_hash[@value_downcase.call(row['MARITAL STATUS']).to_sym]
                          elsif row['MARITAL STATUS'].present?
                            create_associated_model_and_values('MaritalStatus', row['MARITAL STATUS'], marital_status_hash)&.id
                          end
      employee.update_columns(gender_id: gender_id, marital_status_id: marital_status_id, birthday: birthday)
    rescue StandardError => e
      p @row_number, e.message
      local342_feed_errors(e.message)
    end
  rescue StandardError => e
    p @row_number, e.message
    local342_feed_errors(e.message)
  end

  generate_local342_error_report_csv(t.name)

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :local342_union_member_import, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  employment_status = EmploymentStatus.kept.where('lower(name) = ?', 'union').first_or_create!(name: 'Union')
  employment_status_id = employment_status.id

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['FIRST NAME']&.strip || ''
    last_name = row['LAST NAME']&.strip || ''
    ssn = local342_parse_ssn(row['SS #']) || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if row['SS #'].blank?

      local342_feed_errors('Mandatory details not present')
      next
    end
    middle_name = row['M.I.'] || ''
    office = local342_generate_offices(row['LOCAL 342 LIPSE #']) || ''
    employee = Employee.kept.where(social_security_number: ssn) if ssn.present?
    employee = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase) if employee.blank?
    employee = employee.where('lower(middle_name) = ?', middle_name.downcase) if employee.count > 1

    if employee.blank?
      employee = Employee.new(first_name: first_name, last_name: last_name, social_security_number: ssn)

      employee.middle_name = middle_name
      employee.street = row['ADDRESS LINE 1  '] || ''
      employee.apartment = row['ADDRESS LINE 2'] || ''
      employee.city = row['CITY'] || ''
      employee.state = row['ST'] || ''
      employee.zipcode = row['ZIP']&.slice(0, 5) || ''
    elsif employee.count > 1
      local342_feed_errors('More than one employee found')
      next
    else
      employee = employee.first
    end
    is_new_record = employee.new_record?

    if (is_new_record && employee.save!) || (employee.present? && is_new_record == false)
      # local342_feed_errors('Member Already Imported.') unless is_new_record

      employee.employee_employment_statuses.where(employment_status_id: employment_status_id).first_or_initialize.save(validate: false)
      employee.employee_offices.where(office_id: office.id).first_or_create if office.present?
    else
      @errors[@row_number] = employee.errors.full_messages if employee.errors.present?
    end
  rescue StandardError => e
    p @row_number, e.message
    local342_feed_errors(e.message)
  end

  generate_local342_error_report_csv(t.name)

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :local342_employer_import, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors ||= {}
  csv_file.each do |row|
    first_name = row['FIRST NAME']&.strip || ''
    last_name = row['LAST NAME']&.strip || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if row['SS#'].blank?

      local342_feed_errors('Mandatory details not present')
      next
    end
    middle_name = row['M.I.'] || ''
    office = local342_generate_offices(row['LOCAL 342 LIPSE #']) || ''
    ssn = local342_parse_ssn(row['SS#']) || ''

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
    employees = Employee.kept.where(social_security_number: ssn) if employees.blank?
    employees = employees.where('lower(middle_name) = ?', middle_name.downcase) if employees.count > 1

    if employees.blank?
      local342_feed_errors('Member Not Found')
      next
    elsif employees.count > 1
      local342_feed_errors('More than one employee found')
      next
    end

    employee = employees.first
    employee.employee_offices.where(office_id: office.id).first_or_create if office.present?
  rescue StandardError => e
    p @row_number, e.message
    local342_feed_errors(e.message)
  end

  generate_local342_error_report_csv(t.name)
end

task :local342_contact_list_import, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  is_contacts_import = csv_file.headers.include?('Business Phone')

  @errors ||= {}

  department_name = is_contacts_import ? 'Contacts' : 'Golf Outing'
  department = Department.kept.where('LOWER(name) = ?', department_name.downcase).first_or_create!(name: department_name)

  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    unit = local342_check_unit(row['Job Title']) if is_contacts_import
    phone = row['Business Phone'] || ''
    email = row['E-mail Address'] || ''

    @row_number = [first_name, last_name]

    employee = Employee.new
    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = row['Middle Name'] || ''
    employee.street = row['Address']&.gsub(/[\r\n:]/, '') || ''
    employee.city = row['City'] || ''
    employee.state = row['State'] || ''
    employee.zipcode = row['Zip']&.slice(0, 5) || ''
    employee.category = 'contact_person'
    employee.department_id = department.id
    employee.primary_work_location = is_contacts_import ? row['Company'] || '' : row['Company Name'] || ''
    employee.unit_id = unit.id if unit

    if employee.save
      if is_contacts_import
        contacts_hash[0][:value] = local342_parse_phone(phone.gsub(/\D/, '')[0, 10]) || ''
        contacts_hash[-1][:value] = email

        employee.contacts.import contacts_hash
      end

      employee.employee_departments.where(department_id: department.id).first_or_initialize.save(validate: false)
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue StandardError => e
    p @row_number, e.message
    local342_feed_errors(e.message)
  end

  generate_local342_error_report_csv(is_contacts_import ? 'local342_contact_list_import' : 'local342_golf_outing_contact_list_import')

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :local342_commands_update, %w[account] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  changes_hash =
    {
      'NORTH  INC. VILLAGE OF PATCHOGUEOGUE FIRE DISTRICT': 'NORTH PATCHOGUE FIRE DISTRICT',
      'INC. VILLAGE OF PATCHOGUEOGUE': 'INC. VILLAGE OF PATCHOGUE'
    }

  @errors = {}

  changes_hash.each do |key, value|
    old_command = Office.kept.where('LOWER(name) = ?', key.to_s.downcase).first
    new_command = Office.kept.where('LOWER(name) = ?', value.to_s.downcase).first_or_create!(name: value.to_s)

    @row_number = key

    employee_offices = old_command.employee_offices
    employee_offices&.update_all(office_id: new_command.id)

    old_command.discard if old_command.employee_offices.blank?
  end
  generate_csv_report_errors(t.name + '_')
end

def create_associated_model_and_values(model_name, value, hash)
  return unless value.present?

  model_value = model_name.constantize.where('lower(name) = ?', value.downcase)&.first

  model_value = model_name.constantize.create!(name: value) if model_value.blank?
  key = value.to_sym
  hash[@value_downcase.call(value).to_sym] = model_value&.id if hash[key].blank? || hash[key] != model_value&.id
  model_value
end

def get_employees_with_suffix(first_name, last_name, suffix)
  last_name_variants = [
    "#{last_name.downcase} #{suffix.downcase}",
    "#{last_name.downcase}, #{suffix.downcase}.",
    "#{last_name.downcase} #{suffix.downcase}.",
    "#{last_name.downcase},#{suffix.downcase}",
    "#{last_name.downcase},#{suffix.downcase}."
  ]
  Employee.kept.where('lower(first_name) = ? AND lower(last_name) IN (?)', first_name.downcase, last_name_variants)
end

def local342_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)
rescue StandardError => e
  local342_feed_errors('PHONE ' + e.message)
end

def local342_parse_date(date)
  return '' unless date.present?

  date_array = date.split('/')
  day = date_array[1].to_i
  month = date_array[0].to_i
  year = date_array[2].to_i
  year_str = year.to_s

  case year_str.length

  when 2
    year = (year > 24 && year <= 99 ? '19' + year_str : '20' + year_str).to_i
  when 1
    year = ('200' + year_str).to_i
  when (5..)
    raise 'Invalid Year'
  end

  Date.new(year, month, day)
rescue StandardError => e
  local342_feed_errors('DATE - ' + e.message)
end

def local342_generate_offices(office_name)
  return nil unless office_name.present?

  Office.kept.where('LOWER(name) = ?', office_name.strip.downcase).first_or_create!(name: office_name.strip)
rescue StandardError => e
  local342_feed_errors('OFFICE ' + e.message)
end

def local342_check_unit(unit_name)
  return nil unless unit_name.present?

  Unit.kept.where('LOWER(name) = ?', unit_name.strip.downcase).first_or_create!(name: unit_name.strip)
rescue StandardError => e
  feed_errors('UNIT ' + e.message)
end

def local342_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')
  ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)
rescue StandardError => e
  local342_feed_errors('SSN ' + e.message)
end

def generate_local342_error_report_csv(file_name)
  CSV.open("#{Rails.root}/#{file_name}_errors_#{Date.today}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def local342_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'local342_members_import[local342, ILA-Table 1.csv]'
# bundle exec rake 'local342_member_update[local342, Census (1).csv]'
# bundle exec rake 'local342_union_member_import[local342, ILA MEMBERSHIP UNION (1).csv]'
# bundle exec rake 'local342_employer_import[local342, ILA-Table 1.csv]'
# bundle exec rake 'local342_contact_list_import[local342, Contact List - Contacts.csv]'
# bundle exec rake 'local342_contact_list_import[local342, Sheet1-Table 1.csv]'
# bundle exec rake 'local342_commands_update[local342]'
