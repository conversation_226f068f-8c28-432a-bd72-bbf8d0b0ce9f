require 'csv'

desc 'import data'
task :papba_date_employed_class_import, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  sand = {
    "107": "06-15-2006",
    "108": "01-08-2007",
    "109": "08-24-2007",
    "110": "03-14-2008",
    "111": "10-17-2008",
    "112": "08-16-2013",
    "113": "03-10-2014",
    "114": "06-17-2016",
    "115": "02-10-2017",
    "116A": "01-20-2017",
    "116B": "01-05-2018",
    "117": "09-28-2018",
    "118": "04-19-2019",
    "119": "09-13-2019" }

  Employee.includes(:employment_statuses).where("lower(employment_statuses.name) = ?", 'active').references(:employment_statuses).each do |employee|
    @row_number = employee.full_name
    if sand.has_key?(:"#{employee.previous_shield_number}")
      if employee.start_date.blank? || sand[:"#{employee.previous_shield_number}"] != employee.start_date.strftime('%m-%d-%Y')
        employee.update(start_date: Date.strptime(sand[:"#{employee.previous_shield_number}"], '%m-%d-%Y'))
      end
    elsif sand.has_value?(employee.start_date.strftime('%m-%d-%Y'))
      if employee.previous_shield_number.blank? || sand.key(employee.start_date.strftime('%m-%d-%Y')).to_s != employee.previous_shield_number
        employee.update(previous_shield_number: sand.key(employee.start_date.strftime('%m-%d-%Y')).to_s)
      end
    elsif sand.has_key?(:"#{employee.previous_shield_number}") == false && sand.has_value?(employee.start_date.strftime('%m-%d-%Y')) == false
        papba_feed_errors("No Matches found for this employee")
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_papba_date_employed_class_import.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end


def papba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake "papba_date_employed_class_import[papba]"