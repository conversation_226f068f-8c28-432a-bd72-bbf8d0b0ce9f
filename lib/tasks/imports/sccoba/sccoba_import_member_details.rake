# frozen_string_literal: true

require 'csv'

desc 'import data'
task :sccoba_import_member_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # Member Name,Court Location,Title,Employment Status,Email,Marital Status,Position,Shield Number,PO Registration,Pay Grade,DOB,Home phone,Cell phone,Work Phone,City,State,Zip Code,SCCEA Start Date,UCS Start Date,Apt,Address,Sex,Tier

  csv_file.each do |row|

    full_name = sccoba_full_name(row['Member Name'])

    if full_name.size > 2
      first_name = full_name.first
      last_name = full_name[2..-1].join(" ")
      mi = full_name.second
    else
      first_name = full_name.first
      last_name = full_name.second
      mi = ''
    end

    @row_number = row['Member Name']
    street = row['Address'] || ''
    apt = row['Apt'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zipcode = row['Zip Code'].present? ? row['Zip Code'].split('-').first.rjust(5, '0') : ''
    cell_phone = sccoba_parse_phone(row['Cell phone'])
    home_phone = sccoba_parse_phone(row['Home phone'])
    work_phone = sccoba_parse_phone(row['Work Phone'])
    email = row['Email'] || ''
    title = sccoba_check_rank(row['Title'])
    shield_number = row['Shield Number'] || ''
    grade_number = row['Pay Grade'] || ''
    healthplex = row['PO Registration'] || ''
    court_location = sccoba_check_office(row['Court Location'])
    sccoba_date = sccoba_parse_date(row['SCCEA Start Date'], 'sccoba_date')
    dob = sccoba_parse_date(row['DOB'], 'dob')
    position = sccoba_check_position(row['Position'])
    marital_status = sccoba_check_marital_status(row['Marital Status'])
    sex = sccoba_check_gender(row['Sex'])

    usc_date = sccoba_parse_date(row['UCS Start Date'], 'usc_date')
    tier = sccoba_check_affiliation(row['Tier'])
    employment_status = sccoba_check_employment_status(row['Employment Status'])

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = street
    employee.apartment = apt
    employee.city = city
    employee.state = state
    employee.zipcode = zipcode
    employee.shield_number = shield_number
    employee.placard_number = grade_number
    employee.start_date = sccoba_date
    employee.birthday = dob
    employee.a_number = healthplex
    employee.gender_id = sex.id if sex
    employee.ncc_date = usc_date
    employee.marital_status_id = marital_status.id if marital_status
    employee.affiliation_id = tier.id if tier

    if employee.save
      personal_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
      personal_phone_contact.value = cell_phone if cell_phone
      personal_phone_contact.save!
      home_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE)
      home_phone_contact.value = home_phone if home_phone
      home_phone_contact.save!
      work_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE)
      work_phone_contact.value = work_phone if work_phone
      work_phone_contact.save!
      personal_email_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
      personal_email_contact.value = email if email.present?
      personal_email_contact.save!
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!

      employee.employee_ranks.new(rank_id: title.id).save(validate: false) if title
      employee.employee_offices.new(office_id: court_location.id).save(validate: false) if court_location
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false) if employment_status
      employee.employee_positions.new(position_id: position.id).save(validate: false) if position
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def sccoba_full_name(name)
  name.split(' ')
end

def sccoba_check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def sccoba_check_affiliation(affiliation_type)
  return nil unless affiliation_type.present?

  affiliation = Affiliation.where(name: affiliation_type).first_or_create

  affiliation

rescue => e
  feed_errors('AFFILIATION ' + affiliation.errors.full_messages)
end

def sccoba_check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  employment_status = EmploymentStatus.where(name: employment_status_type).first_or_create

  employment_status

rescue => e
  feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def sccoba_check_marital_status(marital_status_name)
  return nil unless marital_status_name.present?

  marital_status = MaritalStatus.where(name: marital_status_name).first_or_create

  marital_status

rescue => e
  feed_errors('MARITAL STATUS ' + marital_status.errors.full_messages)
end

def sccoba_check_office(office_name)
  return nil unless office_name.present?

  office = Office.where(name: office_name).first_or_create

  office

rescue => e
  feed_errors('OFFICE ' + office.errors.full_messages)
end

def sccoba_check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create

  rank

rescue => e
  feed_errors('RANK ' + rank.errors.full_messages)
end

def sccoba_check_position(position)
  return nil unless position.present?

  position = Position.where(name: position).first_or_create

  position

rescue => e
  feed_errors('POSITION ' + position.errors.full_messages)
end

def sccoba_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('-')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def sccoba_parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
