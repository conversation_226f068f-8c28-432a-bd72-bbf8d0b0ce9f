# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nyccoba_members_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # idMembers,SSN,FirstName,LastName,Address1,Address2,City,State,ZipCode,Telephone,Status,Sex,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Chief,<PERSON><PERSON><PERSON><PERSON><PERSON>,DO<PERSON>,Appointed,Rank,Command, Email,PromCaptain,Resigned

  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]
  gender_hash = {}
  marital_status_hash = {}
  offices_hash = {}
  rank_hash = {}
  total_employment_status_hash = {}
  total_arr = []
  member_status_count = 0
  member_employment_statuses = { PromCaptain: '', Resigned: '', Reinstated: '', LeaveOfAbsense: '', Returned: '', Suspension: '', Revocation: '', Vested: '', Retirement: '', Terminated: '', Deceased: '' }
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }

  Employee.skip_callback :save, :after, :update_benefit_address
  if ENV['APP_ENV'] == 'development'
    Employee.skip_callback :commit, :after, :update_legislative_detail
  else
    Employee.skip_callback :save, :after, :update_legislative_detail
  end
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['FirstName'] || ''
    last_name = row['LastName'] || ''
    ssn = row['SSN'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['SSN']).blank?

      nyccoba_feed_errors('Mandatory details not present')
      next
    end
    employee = Employee.new
    employee.first_name = first_name
    employee.last_name = last_name
    employee.social_security_number = ssn
    employee.street = row['Address1'] || ''
    employee.apartment = row['Address2'] || ''
    employee.city = row['City'] || ''
    employee.state = row['State'] || ''
    employee.zipcode = if row['ZipCode'].present?
                         row['ZipCode'].slice(0, 5)
                       else
                         ''
                         # elsif (row['ZipCode'].present? && row['ZipCode'].length >= 5)
                         #   nyccoba_feed_errors('Zipcode length is greater than 5, So saving it as blank') if row['ZipCode'].present?
                         #   ''
                       end

    employee.gender_id = if row['Sex'].present? && gender_hash[@value_downcase.call(row['Sex']).to_sym].present?
                           gender_hash[@value_downcase.call(row['Sex']).to_sym]
                         elsif row['Sex'].present?
                           create_associated_model_and_values('Gender', row['Sex'], gender_hash)&.id
                         end
    employee.marital_status_id = if row['MaritalStatus'].present? && marital_status_hash[@value_downcase.call(row['MaritalStatus']).to_sym]
                                   marital_status_hash[@value_downcase.call(row['MaritalStatus']).to_sym]
                                 elsif row['MaritalStatus'].present?
                                   create_associated_model_and_values('MaritalStatus', row['MaritalStatus'], marital_status_hash)&.id
                                 end
    employee.shield_number = row['ShieldNumber'] || ''
    employee.birthday = row['DOB'].present? ? Date.parse(row['DOB']) : ''
    employee.previous_shield_number = row['PensionNumber'] || ''
    employee.start_date = row['Appointed'].present? ? Date.parse(row['Appointed']) : ''

    # if employee.invalid? && employee.errors.full_messages == 'Validation failed: Social security number has already been taken'
    #   employee.social_security_number = ''
    #   nyccoba_feed_errors('Social Security Number has already been taken, Saving the member as blank')
    # end

    if employee.save!

      contacts_hash.last[:value] = row['Email']
      contacts_hash.last(2).first[:value] = nyccoba_parse_phone(row['Telephone']) || ''
      # CONTACTS
      employee.contacts.import contacts_hash

      # COMMANDS
      command_id = if row['Command'].present? && offices_hash[@value_downcase.call(row['Command']).to_sym].present?
                     offices_hash[@value_downcase.call(row['Command']).to_sym]
                   elsif row['Command'].present?
                     create_associated_model_and_values('Office', row['Command'], offices_hash)&.id
                   end

      employee.employee_offices.new(office_id: command_id).save(validate: false) if command_id.present?

      # Employment Status
      employment_status_id = if row['Status'].present? && total_employment_status_hash[@value_downcase.call(row['Status']).to_sym].present?
                               total_employment_status_hash[@value_downcase.call(row['Status']).to_sym]
                             elsif row['Status'].present?
                               create_associated_model_and_values('EmploymentStatus', row['Status'], total_employment_status_hash)&.id
                             end

      if employment_status_id.present?
        employee_employment_statuses = employee.employee_employment_statuses.new(employment_status_id: employment_status_id)
        employee_employment_statuses.save(validate: false)
      end

      member_employment_statuses[:PromCaptain] = row['PromCaptain'] || ''
      member_employment_statuses[:Resigned] = row['Resigned'] || ''
      member_employment_statuses[:Reinstated] = row['Reinstated'] || ''
      member_employment_statuses["Leave Of Absense".to_sym] = row['LeaveOfAbsense'] || ''
      member_employment_statuses[:Returned] = row['Returned'] || ''
      member_employment_statuses[:Suspended] = row['Suspension'] || ''
      member_employment_statuses[:Revocation] = row['Revocation'] || ''
      member_employment_statuses[:Vested] = row['Vested'] || ''
      member_employment_statuses[:Retired] = row['Retirement'] || ''
      member_employment_statuses[:Terminated] = row['TerminatedDate'] || ''
      member_employment_statuses[:Deceased] = row['Deceased'] || ''



      previous_start_date = nil
      member_employment_statuses = member_employment_statuses.sort_by { |_k, v| [v.empty? ? 0 : 1, v] }.reverse.to_h
      member_employment_statuses.each_key do |key|
        next unless member_employment_statuses[key.to_sym].present?

        employment_status_id = if key.to_s.present? && total_employment_status_hash[@value_downcase.call(key.to_s).to_sym].present?
                                 total_employment_status_hash[@value_downcase.call(key.to_s.titleize).to_sym]
                               elsif key.to_s.present?
                                 create_associated_model_and_values('EmploymentStatus', key.to_s, total_employment_status_hash)&.id
                               end

        employee_employment_status_clone = employee.employee_employment_statuses.select { |k| k.employment_status_id == employment_status_id }.first
        employee_employment_status_clone = employee.employee_employment_statuses.new(employment_status_id: employment_status_id) if employee_employment_status_clone.blank?
        employee_employment_status_clone.start_date = Date.parse(member_employment_statuses[key])
        employee_employment_status_clone.end_date = previous_start_date

        total_arr << employee_employment_status_clone
        if member_status_count == 1000
          EmployeeEmploymentStatus.import total_arr.flatten, on_duplicate_key_update: [:start_date, :end_date]
          total_arr = []
          member_status_count = 0
        end
        member_status_count += 1
        previous_start_date = Date.parse(member_employment_statuses[key.to_sym])
      end

      # Ranks
      rank_id = if row['Rank'].present? && rank_hash[@value_downcase.call(row['Rank']).to_sym].present?
                  rank_hash[@value_downcase.call(row['Rank']).to_sym]
                elsif row['Rank'].present?
                  create_associated_model_and_values('Rank', row['Rank'], rank_hash).id
                end

      if rank_id.present?
        employee_ranks = employee.employee_ranks.new
        employee_ranks.rank_id = rank_id
        captain_start_date = row['PromCaptain'] || ''
        employee_ranks.start_date = Date.parse(captain_start_date) if rank_hash.key(rank_id).to_s == 'captain' && captain_start_date.present?
        employee_ranks.save(validate: false)
      end

    else
      @errors[@row_number] = employee.errors.full_messages
    end
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  EmployeeEmploymentStatus.import total_arr.flatten, on_duplicate_key_update: [:start_date, :end_date]

  CSV.open("#{Rails.root}/#{args[:account]}_members_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end

  Employee.skip_callback :save, :after, :update_benefit_address
  if ENV['APP_ENV'] == 'development'
    Employee.skip_callback :commit, :after, :update_legislative_detail
  else
    Employee.skip_callback :save, :after, :update_legislative_detail
  end
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status
end
task :nyccoba_benefits_addition_for_active_and_retire, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  optical_benefit_id = Benefit.kept.where(name: "Optical").first_or_create.id
  prescription_benefit_id = Benefit.kept.where(name: "Prescription").first_or_create.id
  dental_benefit_id = Benefit.kept.where(name: "Dental").first_or_create.id

  benefits = [{ benefit_id: optical_benefit_id, employee_id: "" }, { benefit_id: prescription_benefit_id, employee_id: "" },
              { benefit_id: dental_benefit_id, employee_id: "" }]
  count = 0
  total_hash = []
  Employee.kept.includes(:gender, :employee_employment_statuses).where(employee_employment_statuses:
                                                                         { employment_status_id: EmploymentStatus.kept.where("lower(name) in (?)", ["active", "retired"]).pluck(:id) }).find_each do |employee|
    employee_benefits = benefits.deep_dup
    employee_benefits.each { |x| x["employee_id".to_sym] = employee.id }
    total_hash << employee_benefits
    if count == 100
      EmployeeBenefit.import total_hash.flatten
      total_hash = []
      count = 0
    end
    count += 1

  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  EmployeeBenefit.import total_hash.flatten

  CSV.open("#{Rails.root}/nyccoba_benefits_addition_for_active_and_retire_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_members_zipcode, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  total_employment_status_hash = {}
  employment_status_hash = { PromCaptain: '', Resigned: '', Reinstated: '', LeaveOfAbsense: '', Returned: '', Suspension: '', Revocation: '', Vested: '', Retirement: '', Terminated: '', Deceased: '' }
  count = 0
  total_arr = []

  leave_of_absence = EmploymentStatus.where('lower(name) = ?', 'leave of absence').first
  leave_of_absence.update_columns(name: 'LeaveOfAbsence') if leave_of_absence.present?

  csv_file.each do |row|
    first_name = row['FirstName'] || ''
    last_name = row['LastName'] || ''
    ssn = row['SSN'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['SSN']).blank?

      nyccoba_feed_errors('Mandatory details not present')
      next
    end

    employees = Employee.kept.includes(:employee_employment_statuses).where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)

    if employees.blank? || employees.count > 1
      employees = Employee.kept.includes(:employee_employment_statuses).where(social_security_number: ssn)
      if employees.blank?
        nyccoba_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyccoba_feed_errors('More than One employee found')
        next
      end
    end
    employee = employees.first

    # if row['ZipCode'].present?
    #   zipcode = row['ZipCode'].slice(0, 5)
    #   employee.update_columns(zipcode: zipcode) if employee.zipcode != zipcode
    # end

    employment_status_hash[:PromCaptain] = row['PromCaptain'] || ''
    employment_status_hash[:Resigned] = row['Resigned'] || ''
    employment_status_hash[:Reinstated] = row['Reinstated'] || ''
    employment_status_hash[:LeaveOfAbsense] = row['LeaveOfAbsense'] || ''
    employment_status_hash[:Returned] = row['Returned'] || ''
    employment_status_hash[:Suspended] = row['Suspension'] || ''
    employment_status_hash[:Revocation] = row['Revocation'] || ''
    employment_status_hash[:Vested] = row['Vested'] || ''
    employment_status_hash[:Retired] = row['Retirement'] || ''
    employment_status_hash[:Terminated] = row['TerminatedDate'] || ''
    employment_status_hash[:Deceased] = row['Deceased'] || ''

    previous_start_date = nil
    employment_status_hash = employment_status_hash.sort_by { |_k, v| v.blank? ? Date.new : Date.parse(v) }.reverse.to_h
    employment_status_hash.each_key do |key|
      next unless employment_status_hash[key.to_sym].present?

      employment_status_id = if key.to_s.present? && total_employment_status_hash[@value_downcase.call(key.to_s).to_sym].present?
                               total_employment_status_hash[@value_downcase.call(key.to_s).to_sym]
                             elsif key.to_s.present?
                               create_associated_model_and_values('EmploymentStatus', key.to_s, total_employment_status_hash)&.id
                             end

      employee_employment_status_clone = employee.employee_employment_statuses.select { |k| k.employment_status_id == employment_status_id }.first
      employee_employment_status_clone = employee.employee_employment_statuses.new(employment_status_id: employment_status_id) if employee_employment_status_clone.blank?
      employee_employment_status_clone.start_date = Date.parse(employment_status_hash[key])
      employee_employment_status_clone.end_date = previous_start_date

      total_arr << employee_employment_status_clone
      if count == 1000
        EmployeeEmploymentStatus.import total_arr.flatten, on_duplicate_key_update: [:start_date, :end_date]
        total_arr = []
        count = 0
      end
      count += 1
      previous_start_date = Date.parse(employment_status_hash[key.to_sym])
    end
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  EmployeeEmploymentStatus.import total_arr.flatten, on_duplicate_key_update: [:start_date, :end_date]

  CSV.open("#{Rails.root}/nyccoba_members_zipcode_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_members_ein_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    ssn = row['SS'] || ''
    @row_number = ssn

    if (@row_number = row['SS']).blank? || row['EIN'].blank?
      nyccoba_feed_errors('Mandatory details not present')
      next
    end

    ssn_rjust = ssn.rjust(9, '0')

    employees = Employee.kept.where(social_security_number: ssn_rjust)
    if employees.blank?
      nyccoba_feed_errors('Member not found')
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One employee found')
      next
    end

    employee = employees.first
    employee.update_columns(a_number: row['EIN'])
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/nyccoba_members_ein_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors', 'MemberID']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_dependents_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  # dependents_hash = { employee_id: '', employee_benefit_id: '', first_name: '', last_name: '', social_security_number: '',
  #                     relationship: '', birthday: '', student: '', school_status: '', dependent: '', semester: '' }
  dependents_hash = { employee_id: '', employee_benefit_id: '', first_name: '', last_name: '', social_security_number: '',
                      relationship: '', birthday: '', dependent: '' }
  total_arr = []
  member_ids = [21848, 21869, 21920, 21921, 21931, 21938, 21956, 21960, 21963, 21995, 21998, 22014, 22018, 22047, 22067, 22068, 22077, 22083, 22094, 22099, 22102, 22110, 22114, 22131, 22140, 22154, 22157, 22173, 22182, 22195, 22200, 22209, 22218, 22225, 22248, 22254, 22259, 22265, 22266, 22275, 22278, 22291, 22326, 22328, 22337, 22340, 22353, 22355, 22372, 22375, 22404, 22414, 22417, 22418, 22422, 22430, 22442, 22452, 22457, 22470, 22477, 22478, 22498, 22499, 22502, 22508, 22524, 22538, 22542, 22558, 22565, 22574, 22597, 22603, 22623, 22630, 22634, 22636, 22637, 22645, 22660, 22663, 22665, 22676, 22708, 22710, 22716, 22723, 22735, 22739, 22743, 22750, 22756, 22757, 22761, 22767, 22770, 22773, 22774, 22784, 22800, 22801, 22804, 22814, 22826, 22827, 22832, 22850, 22862, 22866, 22879, 22883, 22885, 22886, 22904, 22907, 22911, 22916, 22924, 22931, 22941, 22944, 22945, 22948, 22962, 22968, 22969, 22971, 22976, 22980, 22983, 23000, 23016, 23021, 23022, 23026, 23029, 23035, 23037, 23039, 23056, 23057, 23067, 23087, 23090, 23094, 23124, 23147, 23156, 23158, 23159, 23173, 23188, 23189, 23204, 23229, 23233, 23251, 23257, 23266, 23267, 23280, 23281, 23285, 23290, 23303, 23311, 23312, 23317, 23334, 23336, 23351, 23364, 23367, 23383, 23386, 23410, 23411, 23422, 23438, 23439, 23456, 23462, 23464, 23478, 23485, 23486, 23505, 23517, 23518, 23535, 23556, 23563, 23564, 23567, 23572, 23578, 23580, 23586, 23594, 23606, 23611, 23617, 23632, 23637, 23643, 23657, 23668, 23674, 23676, 23678, 23684, 23701, 23705, 23714, 23733, 23734, 23735, 23736, 23750, 23751, 23769, 23779, 23783, 23795, 23811, 23821, 23833, 23858, 23866, 23877, 23889, 23899, 23907, 23910, 23918, 23923, 23924, 23952, 23955, 23977, 23994, 23997, 24046, 24049, 24050, 24059, 24094, 24095, 24100, 24108, 24114, 24119, 24129, 24145, 24168, 24178, 24184, 24198, 24200, 24201, 24202, 24232, 24238, 24241, 24268, 24273, 24275, 24291, 24304, 24313, 24324, 24325, 24332, 24341, 24349, 24358, 24364, 24374, 24379, 24395, 24399, 24416, 24430, 24432, 24434, 24440, 24461, 24467, 24475, 24476, 24480, 24490, 24494, 24520, 24537, 24539, 24540, 24541, 24543, 24545, 24552, 24556, 24557, 24581, 24594, 24609, 24667, 24672, 24673, 24690, 24693, 24728, 24749, 24754, 24759, 24762, 24772, 24785, 24788, 24817, 24835, 24849, 24867, 24881, 24884, 24886, 24906, 24913, 24931, 24933, 24939, 24943, 24945, 24948, 24950, 24957, 24979, 24981, 25012, 25030, 25031, 25047, 25053, 25056, 25058, 25065, 25066, 25073, 25077, 25081, 25084, 25093, 25097, 25103, 25106, 25122, 25123, 25133, 25134, 25138, 25139, 25178, 25190, 25193, 25215, 25224, 25225, 25239, 25255, 25257, 25278, 25302, 25303, 25314, 25334, 25353, 25376, 25390, 25394, 25414, 25419, 25423, 25456, 25457, 25461, 25465, 25493, 25499, 25509, 25520, 25521, 25528, 25529, 25532, 25546, 25564, 25580, 25583, 25589, 25591, 25596, 25598, 25601, 25638, 25651, 25658, 25665, 25695, 25698, 25732, 25733, 25739, 25747, 25753, 25754, 25767, 25768, 25769, 25770, 25816, 25817, 25828, 25836, 25843, 25845, 25848, 25850, 25851, 25866, 25871, 25886, 25887, 25888, 25894, 25899, 25906, 25909, 25922, 25928, 25938, 25940, 25943, 25951, 25972, 25988, 25993, 25994, 25999, 26000, 26024, 26042, 26045, 26054, 26056, 26070, 26080, 26083, 26089, 26096, 26109, 26123, 26129, 26148, 26154, 26184, 26188, 26196, 26199, 26205, 26280, 26281, 26310, 26316, 26317, 26326, 26327, 26332, 26335, 26336, 26342, 26361, 26365, 26370, 26372, 26392, 26395, 26397, 26399, 26423, 26424, 26442, 26443, 26448, 26455, 26458, 26464, 26465, 26471, 26475, 26508, 26520, 26523, 26524, 26532, 26536, 26544, 26551, 26562, 26565, 26579, 26591, 26597, 26616, 26633, 26634, 26656, 26660, 26661, 26667, 26683, 26684, 26705, 26747, 26755, 26758, 26771, 26774, 26775, 26783, 26790, 26792, 26808, 26816, 26844, 26846, 26854, 26874, 26887, 26900, 26906, 26908, 26917, 26928, 26930, 26937, 26939, 26957, 26961, 26962, 26970, 26971, 27003, 27006, 27020, 27034, 27044, 27056, 27079, 27084, 27087, 27092, 27099, 27100, 27116, 27118, 27125, 27131, 27134, 27138, 27139, 27147, 27156, 27163, 27175, 27182, 27194, 27203, 27210, 27213, 27240, 27249, 27250, 27257, 27261, 27283, 27315, 27320, 27330, 27333, 27342, 27350, 27360, 27380, 27395, 27405, 27412, 27422, 27424, 27426, 27432, 27444, 27456, 27461, 27482, 27498, 27505, 27507, 27511, 27512, 27515, 27560, 27563, 27568, 27573, 27590, 27592, 27594, 27610, 27612, 27629, 27630, 27638, 27642, 27648, 27649, 27663, 27688, 27690, 27707, 27710, 27722, 27724, 27745, 27763, 27767, 27789, 27804, 27806, 27809, 27812, 27815, 27834, 27846, 27850, 27867, 27869, 27873, 27876, 27880, 27884, 27919, 27921, 27922, 27926, 27927, 27935, 27940, 27941, 27951, 27966, 27967, 27970, 27980, 28006, 28022, 28050, 28061, 28073, 28074, 28094, 28116, 28117, 28118, 28139, 28155, 28168, 28187, 28212, 28214, 28216, 28226, 28239, 28240, 28251, 28253, 28255, 28256, 28263, 28264, 28269, 28275, 28276, 28279, 28281, 28286, 28292, 28297, 28305, 28307, 28309, 28315, 28361, 28371, 28400, 28403, 28411, 28435, 28486, 28487, 28490, 28515, 28516, 28535, 28536, 28541, 28552, 28553, 28562, 28565, 28567, 28585, 28587, 28609, 28611, 28624, 28629, 28632, 28638, 28639, 28642, 28645, 28652, 28655, 28662, 28680, 28697, 28703, 28711, 28721, 28723, 28745, 28755, 28760, 28762, 28766, 28767, 28783, 28786, 28794, 28801, 28811, 28819, 28823, 28826, 28886, 28889, 28903, 28904, 28918, 28920, 28928, 28933, 28947, 28952, 28955, 28987, 28990, 28993, 28997, 29003, 29004, 29012, 29024, 29027, 29045, 29050, 29056, 29064, 29071, 29076, 29088, 29089, 29090, 29100, 29136, 29146, 29156, 29171, 29212, 29215, 29219, 29224, 29248, 29249, 29255, 29267, 29277, 29278, 29279, 29281, 29282, 29285, 29287, 29317, 29322, 29349, 29407, 29408, 29420, 29421, 29454, 29465, 29473, 29495, 29501, 29506, 29509, 29513, 29521, 29527, 29535, 29536, 29543, 29544, 29563, 29567, 29572, 29581, 29593, 29618, 29623, 29629, 29637, 29643, 29646, 29658, 29666, 29690, 29691, 29706, 29716, 29720, 29741, 29742, 29744, 29746, 29759, 29764, 29766, 29768, 29776, 29781, 29787, 29804, 29811, 29848, 29849, 29851, 29852, 29863, 29869, 29870, 29875, 29876, 29877, 29889, 29905, 29914, 29919, 29926, 29927, 29942, 29978, 29982, 29988, 29995, 30011, 30012, 30043, 30046, 30047, 30048, 30049, 30051, 30054, 30061, 30062, 30072, 30078, 30082, 30090, 30117, 30122, 30134, 30136, 30138, 30146, 30155, 30165, 30175, 30178, 30206, 30210, 30211, 30212, 30215, 30253, 30267, 30281, 30297, 30300, 30301, 30305, 30312, 30315, 30321, 30327, 30353, 30362, 30363, 30369, 30371, 30373, 30379, 30387, 30395, 30415, 30425, 30433, 30443, 30456, 30460, 30476, 30482, 30489, 30491, 30498, 30525, 30533, 30543, 30557, 30566, 30575, 30577, 30580, 30632, 30649, 30656, 30662, 30666, 30668, 30675, 30722, 30749, 30754, 30765, 30791, 30793, 30839, 30840, 30851, 30861, 30866, 30878, 30881, 30891, 30898, 30904, 30916, 30925, 30927, 30932, 30934, 30943, 30954, 30962, 30997, 30999, 31025, 31029, 31033, 31053, 31057, 31059, 31076, 31079, 31080, 31082, 31084, 31095, 31097, 31101, 31115, 31117, 31119, 31127, 31136, 31141, 31143, 31148, 31149, 31173, 31180, 31196, 31216, 31233, 31243, 31247, 31263, 31266, 31287, 31291, 31294, 31299, 31307, 31317, 31318, 31320, 31341, 31360, 31378, 31409, 31427, 31447, 31460, 31468, 31500, 31502, 31505, 31506, 31528, 31533, 31541, 31545, 31551, 31552, 31553, 31572, 31574, 31639, 31646, 31660, 31670, 31676, 31685, 31688, 31705, 31706, 31718, 31723, 31726, 31748, 31750, 31769, 31772, 31774, 31776, 31787, 31806, 31822, 31830, 31833, 31853, 31864, 31871, 31875, 31877, 31894, 31914, 31919, 31922, 31929, 31965, 31966, 31970, 31985, 32003, 32018, 32024, 32025, 32031, 32032, 32037, 32040, 32049, 32103, 32104, 32114, 32125, 32127, 32135, 32140, 32155, 32160, 32164, 32173, 32174, 32197, 32213, 32215, 32219, 32222, 32231, 32247, 32253, 32260, 32264, 32302, 32303, 32307, 32313, 32323, 32330, 32333, 32338, 32342, 32347, 32353, 32357, 32360, 32394, 32395, 32406, 32412, 32422, 32434, 32437, 32439, 32474, 32478, 32479, 32482, 32485, 32490, 32499, 32504, 32508, 32514, 32515, 32527, 32531, 32536, 32544, 32569, 32584, 32585, 32586, 32587, 32599, 32600, 32603, 32632, 32639, 32651, 32653, 32654, 32656, 32689, 32705, 32706, 32710, 32713, 32722, 32723, 32728, 32732, 32738, 32739, 32742, 32751, 32771, 32772, 32773, 32785, 32787, 32788, 32801, 32815, 32816, 32820, 32823, 32827, 32830, 32832, 32841, 32845, 32849, 32857, 32862, 32863, 32879, 32882, 32883, 32884, 32916, 32926, 32929, 32941, 32944, 32973, 32978, 32984, 32986, 32992, 32993, 33000, 33010, 33019, 33022, 33042, 33043, 33044, 33051, 33057, 33060, 33061, 33072, 33108, 33109, 33112, 33120, 33148, 33185, 33188, 33217, 33231, 33233, 33239, 33244, 33245, 33251, 33260, 33261, 33270, 33282, 33292, 33297, 33301, 33312, 33317, 33344, 33347, 33360, 33367, 33368, 33373, 33431, 33437, 33444, 33445, 33447, 33467, 33491, 33510, 33515, 33522, 33549, 33560, 33567, 33573, 33574, 33585, 33586, 33591, 33657, 33666, 33692, 33693, 33704, 33723, 33724, 33770, 33771, 33787, 33789, 33810, 33823, 33832, 33849, 33862, 33868, 33875, 33881, 33894, 33895, 33901, 33909, 33911, 33925, 33929, 33936, 33937, 33964, 33969, 34033, 34041, 34043, 34046, 34055, 34057, 34059, 34065, 34081, 34091, 34092, 34098, 34122, 34123, 34127, 34137, 34145, 34154, 34163, 34181, 34182, 34185, 34189, 34197, 34198, 34202, 34233, 34252, 34268, 34274, 34286, 34299, 34308, 34315, 34323, 34325, 34341, 34363, 34368, 34370, 34381, 34382, 34383, 34394, 34409, 34410, 34411, 34421, 34435, 34436, 34439, 34442, 34447, 34456, 34459, 34495, 34499, 34515, 34530, 34533, 34542, 34544, 34546, 34551, 34560, 34562, 34570, 34575, 34585, 34588, 34597, 34600, 34608, 34614, 34620, 34630, 34642, 34659, 34663, 34679, 34686, 34694, 34698, 34700, 34710, 34719, 34751, 34761, 34768, 34783, 34788, 34795, 34807, 34812, 34822, 34883, 34887, 34893, 34902, 34903, 34926, 34940, 34945, 34948, 34950, 34965, 34975, 34988, 34993, 35011, 35021, 35027, 35033, 35034, 35050, 35060, 35084, 35099, 35109, 35112, 35133, 35155, 35156, 35171, 35191, 35207, 35209, 35210, 35213, 35230, 35233, 35238, 35241, 35243, 35246, 35254, 35255, 35271, 35276, 35292, 35313, 35323, 35326, 35334, 35366, 35376, 35386, 35390, 35400, 35401, 35402, 35404, 35411, 35417, 35418, 35421, 35424, 35459, 35462, 35474, 35478, 35493, 35504, 35505, 35515, 35529, 35537, 35555, 35557, 35569, 35575, 35585, 35586, 35588, 35594, 35610, 35620, 35625, 35636, 35640, 35656, 35664, 35676, 35677, 35678, 35689, 35697, 35698, 35705, 35707, 35737, 35761, 35769, 35775, 35779, 35784, 35788, 35795, 35797, 35799, 35805, 35808, 35810, 35814, 35820, 35826, 35838, 35848, 35860, 35872, 35896, 35912, 35920, 35928, 35946, 35949, 35960, 35961, 35978, 35979, 35980, 35983, 35997, 36012, 36022, 36030, 36050, 36056, 36072, 36087, 36098, 36099, 36104, 36105, 36108, 36123, 36124, 36131, 36134, 36152, 36171, 36183, 36184, 36195, 36201, 36210, 36212, 36215, 36218, 36226, 36228, 36273, 36278, 36288, 36297, 36298, 36317, 36327, 36341, 36343, 36360, 36364, 36380, 36387, 36395, 36401, 36412, 36423, 36426, 36430, 36431, 36432, 36460, 36477, 36478, 36487, 36489, 36494, 36497, 36500, 36520, 36540, 36554, 36563, 36568, 36573, 36574, 36582, 36587, 36598, 36605, 36619, 36645, 36667, 36686, 36689, 36691, 36692, 36708, 36709, 36717, 36728, 36740, 36749, 36756, 36771, 36792, 36793, 36809, 36817, 36818, 36841, 36857, 36859, 36866, 36889, 36892, 36913, 36940, 36982, 37000, 37007, 37039, 37070, 37071, 37072, 37079, 37084, 37087, 37095, 37106, 37108, 37114, 37117, 37147, 37161, 37163, 37167, 37177, 37202, 37209, 37210, 37238, 37240, 37242, 37248, 37249, 37254, 37280, 37287, 37290, 37293, 37294, 37299, 37301, 37309, 37322, 37331, 37332, 37348, 37349, 37359, 37362, 37365, 37369, 37370, 37376, 37378, 37383, 37392, 37393, 37403, 37408, 37409, 37412, 37414, 37420, 37423, 37425, 37446, 37451, 37466, 37478, 37482, 37484, 37516, 37527, 37533, 37535, 37555, 37563, 37571, 37572, 37575, 37581, 37591, 37605, 37606, 37611, 37614, 37616, 37631, 37632, 37642, 37644, 37690, 37701, 37721, 37728, 37769, 37770, 37822, 37825, 37849, 37869, 37870, 37875, 37885, 37899, 37906, 37913, 37917, 37919, 37928, 37935, 37939, 37952, 37957, 37958, 37963, 37974, 37986, 37988, 38001, 38006, 38013, 38016, 38017, 38022, 38028, 38058, 38059, 38074, 38078, 38087, 38093, 38105, 38117, 38144, 38155, 38158, 38179, 38186, 38188, 38191, 38206, 38218, 38223, 38226, 38227, 38229, 38235, 38260, 38264, 38283, 38309, 38321, 38342, 38410, 38440, 38452, 38459, 38460, 38470, 38486, 38502, 38503, 38508, 38516, 38517, 38536, 38540, 38543, 38545, 38554, 38555, 38559, 38577, 38586, 38625, 38629, 38634, 38637, 38652, 38673, 38702, 38707, 38745, 38750, 38777, 38781, 38782, 38790, 38791, 38825, 38841, 38849, 38854, 38871, 38875, 38880, 38908, 38925, 38950, 38958, 38968, 38971, 38977, 38990, 38997, 39011, 39052, 39055, 39062, 39076, 39080, 39082, 39084, 39107, 39124, 39126, 39133, 39139, 39150, 39157, 39168, 39171, 39174, 39176, 39181, 39187, 39188, 39190, 39198, 39202, 39208, 39218, 39219, 39220, 39246, 39253, 39272, 39273, 39287, 39303, 39311, 39313, 39329, 39343, 39350, 39358, 39360, 39372, 39375, 39389, 39418, 39438, 39474, 39490, 39491, 39500, 39502, 39511, 39522, 39523, 39535, 39543, 39551, 39574, 39576, 39586, 39590, 39592, 39595, 39600, 39601, 39602, 39616, 39617, 39619, 39633, 39638, 39640, 39643, 39660, 39671, 39678, 39682, 39691, 39701, 39708, 39711, 39715, 39734, 39741, 39760, 39774, 39784, 39791, 39824, 39826, 39832, 39840, 39841, 39842, 39849, 39852, 39866, 39871, 39872, 39881, 39893, 39906, 39912, 39926, 39933, 39935, 39966, 39972, 39975, 39993, 39996, 40003, 40006, 40012, 40023, 40027, 40041, 40047, 40063, 40064, 40080, 40082, 40092, 40125, 40128, 40137, 40182, 40189, 40192, 40198, 40209, 40210, 40214, 40216, 40246, 40247, 40252, 40261, 40272, 40275, 40287, 40296, 40307, 40313, 40323, 40329, 40342, 40345, 40347, 40350, 40356, 40357, 40366, 40370, 40393, 40408, 40410, 40412, 40420, 40428, 40449, 40450, 40457, 40469, 40481, 40486, 40494, 40495, 40497, 40508, 40516, 40517, 40523, 40531, 40535, 40551, 40557, 40561, 40571, 40579, 40626, 40629, 40631, 40645, 40652, 40656, 40658, 40659, 40662, 40664, 40666, 40682, 40688, 40689, 40695, 40696, 40699, 40703, 40710, 40718, 40720, 40725, 40726, 40727, 40728, 40739, 40746, 40748, 40753, 40762, 40769, 40783, 40791, 40807, 40810, 40829, 40830, 40853, 40868, 40900, 40901, 40909, 40912, 40916, 40921, 40933, 40934, 40939, 40940, 40950, 40956, 40987, 40988, 41010, 41011, 41029, 41031, 41040, 41049, 41065, 41082, 41083, 41090, 41107, 41112, 41115, 41120, 41125, 41126, 41141, 41173, 41175, 41178, 41179, 41180, 41183, 41200, 41221, 41223, 41233, 41245, 41264, 41281, 41295, 41299, 41304, 41310, 41342, 41346, 41347, 41349, 41363, 41374, 41375, 41388, 41391, 41396, 41400, 41414, 41417, 41427, 41437, 41438, 41441, 41445, 41446, 41457, 41466, 41489, 41506, 41520, 41539, 41566, 41576, 41597, 41605, 41607, 41615, 41624, 41634, 41655, 41688, 41689, 41708, 41710, 41716, 41722, 41723, 41727, 41733, 41735, 41743, 41747, 41770, 41772, 41776, 41780, 41782, 41801, 41808, 41809, 41814, 41815, 41818, 41819, 41822, 41832, 41836, 41840, 41843, 41871, 41873, 41875, 41885, 41886, 41894, 41903, 41906, 41912, 41925, 41953, 41961, 41965, 41967, 41968, 41970, 41976, 41981, 41985, 42014, 42016, 42041, 42045, 42068, 42069, 42087, 42097, 42099, 42102, 42111, 42121, 42127, 42148, 42152, 42176, 42182, 42198, 42204, 42212, 42215, 42251, 42255, 42256, 42272, 42280, 42287, 42290, 42316, 42318, 42320, 42332, 42333, 42336, 42342, 42356, 42363, 42370, 42372, 42395, 42401, 42405, 42408, 42414, 42417, 42419, 42422, 42424, 42431, 42464, 42470, 42472, 42482, 42487, 42492, 42496, 42503, 42507, 42508, 42533, 42548, 42563, 42573, 42589, 42592, 42605, 42621, 42627, 42647, 42676, 42681, 42684, 42703, 42738, 42744, 42777, 42778, 42785, 42787, 42788, 42805, 42835, 42841, 42849, 42855, 42858, 42864, 42872, 42880, 42884, 42890, 42904, 42909, 42929, 42955, 42957, 42969, 42970, 42979, 42982, 43002, 43004, 43005, 43020, 43021, 43028, 43034, 43053, 43092, 43095, 43109, 43116, 43134, 43138, 43145, 43147, 43150, 43153, 43157, 43159, 43162, 43192, 43199, 43201, 43202, 43212, 43224, 43230, 43282, 43295, 43296, 43309, 43323, 43338, 43389, 43393, 43394, 43395, 43401, 43411, 43418, 43419, 43443, 43469, 43475, 43478, 43492, 43493, 43494, 43501, 43506, 43513, 43520, 43523, 43526, 43551, 43553, 43556, 43563, 43571, 43591, 43605, 43620, 43629, 43631, 43643, 43654, 43667, 43668, 43669, 43685, 43698, 43716, 43718, 43729, 43736, 43738, 43788, 43798, 43799, 43803, 43812, 43823, 43831, 43837, 43840, 43845, 44363, 44933, 45223, 45294, 46031, 46195, 46856, 47244, 47334, 47537, 48838, 49879, 50241, 50478, 50994, 51224, 51784, 51785, 51786, 51787, 51788, 51789, 51790, 51792, 51814, 51842, 52232]
  count = 0
  same_employee_count = 0
  employee_outside_loop = nil
  member_id = nil
  optical_benefit_id = Benefit.kept.where('lower(name) = ?', 'optical').first.id
  dental_benefit_id = Benefit.kept.where('lower(name) = ?', 'dental').first.id
  prescription_benefit_id = Benefit.kept.where('lower(name) = ?', 'prescription').first.id

  csv_file.each do |row|
    next if row['InternalStatus']&.downcase == 'deleted' || member_ids.exclude?(row['MemberID'].to_i)

    same_employee_count = 0 if member_id != row['MemberID']
    ssn = row['SSN'] || ''
    @row_number = ssn

    if (@row_number).blank?
      @row_number = [row['FirstName'], row['LastName']]
      if row['FirstName'].blank? || row['LastName'].blank?
        nyccoba_feed_errors('Mandatory details not present')
        next
      end
    end

    if ssn.present? && member_id != row['MemberID']
      ssn_rjust = ssn.gsub('-', '').rjust(9, '0')
      parsed_ssn = nyccoba_parse_ssn(ssn_rjust)
      employees = Employee.kept.includes(:employee_benefits).where(social_security_number: parsed_ssn)
    end

    if (ssn.blank? || employees.blank? || employees.count > 1) && member_id != row['MemberID']
      first_name = row['FirstName']
      last_name = row['LastName']
      employees = Employee.kept.includes(:employee_benefits).where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
      if employees.blank?
        nyccoba_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyccoba_feed_errors('More than One employee found')
        next
      end
    end

    employee = if member_id.blank? || member_id != row['MemberID']
                 employees.first
               elsif member_id == row['MemberID']
                 employee_outside_loop
               end
    employee_optical_benefit_id = check_benefit_creation(optical_benefit_id, employee)
    employee_dental_benefit_id = check_benefit_creation(dental_benefit_id, employee)
    employee_prescription_benefit_id = check_benefit_creation(prescription_benefit_id, employee)

    benefit_ids_arr = [employee_dental_benefit_id, employee_optical_benefit_id, employee_prescription_benefit_id]

    dependents_hash_clone = dependents_hash.deep_dup
    dependents_hash_clone[:employee_id] = employee.id
    dependents_hash_clone[:first_name] = row['FirstName'] || ''
    dependents_hash_clone[:last_name] = row['LastName'] || ''
    dependents_hash_clone[:social_security_number] = row['SSN_D'] || ''
    relationship = row['Relation'].present? ? row['Relation'].downcase.gsub(' ', '_') : ''
    dependents_hash_clone[:relationship] = if relationship.present? && %w[son daughter].include?(relationship)
                                             'child'
                                           else
                                             relationship
                                           end
    dependents_hash_clone[:birthday] = Date.strptime(row['DOB'], '%Y-%m-%d') if row['DOB'].present?
    # dependents_hash_clone[:student] = nyccoba_get_school_status(row)
    # dependents_hash_clone[:school_status] = get_fall_or_spring(row)
    # dependents_hash_clone[:semester] = row['EligBenefits'] || ''
    # dependents_hash_clone[:unexpire] = if row['EligBenefits'] == 'Y'
    #                                      true
    #                                    else
    #                                      false
    #                                    end

    # dependents_hash_clone[:dependent] = check_dependent_number(row, relationship, same_employee_count)

    benefit_ids_arr.each_with_index do |benefit_id, index|
      if index != 0
        dependents_hash_clone = dependents_hash_clone.deep_dup
      end
      dependents_hash_clone[:employee_benefit_id] = benefit_id
      total_arr << dependents_hash_clone
    end

    if count == 500
      benefit_coverage_create(total_arr)
      total_arr = []
      count = 0
    end

    count += 1
    employee_outside_loop = employee if employees.present?
    member_id = row['MemberID']
    same_employee_count += 1
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  # BenefitCoverage.import total_arr.flatten
  benefit_coverage_create(total_arr)


  CSV.open("#{Rails.root}/nyccoba_dependents_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

def nyccoba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def create_associated_model_and_values(model_name, value, hash)
  return unless value.present?

  if (model_value = model_name.constantize.where('lower(name) = ?', value&.downcase)&.first).present?
    model_value
  else
    model_value = model_name.constantize.create!(name: value)
  end
  hash[@value_downcase.call(value).to_sym] = model_value.id if hash[value.to_sym].blank? || (hash[value.to_sym].present? && hash[value.to_sym] != model_value.id)
  model_value
end

def check_benefit_creation(benefit_id, employee)
  employee_benefit = employee.employee_benefits.select { |v| v.benefit_id == benefit_id }
  employee_benefit = if employee_benefit.present?
                       employee_benefit.first
                     else
                       employee.employee_benefits.create!(benefit_id: benefit_id)
                     end

  employee_benefit.id
end

def get_fall_or_spring(row)
  fall_or_spring_hash = { Spring1: '', Spring2: '', Fall1: '', Fall2: '' }
  fall_or_spring_hash[:Spring1] = row['Spring'] || ''
  fall_or_spring_hash[:Spring2] = row['Spring2'] || ''
  fall_or_spring_hash[:Fall1] = row['Fall'] || ''
  fall_or_spring_hash[:Fall2] = row['Fall2'] || ''

  fall_or_spring_hash = fall_or_spring_hash.sort_by { |_k, v| [v.empty? ? 0 : 1, v] }.reverse.to_h

  return '' unless fall_or_spring_hash.values.first.present?

  key = fall_or_spring_hash.keys.first
  key = key.to_s[0..-2]
  key.downcase
end

def check_dependent_number(row, relationship, count)
  if relationship.present? && relationship&.downcase == 'member'
    '00'
  elsif relationship.present? && (relationship&.downcase == 'spouse' || relationship&.downcase == 'domestic_partner')
    '01'
  else
    member_name = "#{row['MemberFirstName']} #{row['MemberLastName']}".downcase
    dependent_name = "#{row['FirstName']} #{row['LastName']}".downcase
    return '00' if member_name == dependent_name

    "0#{count}"
  end
end

def nyccoba_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  nyccoba_feed_errors('PHONE ' + e.message)
end

def nyccoba_get_school_status(row)
  date_range = Date.parse('2024-02-01')..Date.parse('2025-01-31')
  spring1 = Date.parse(row['Spring']) if row['Spring'].present?
  spring2 = Date.parse(row['Spring2']) if row['Spring2'].present?
  fall1 = Date.parse(row['Fall']) if row['Fall'].present?
  fall2 = Date.parse(row['Fall2']) if row['Fall2'].present?

  if date_range === spring1 && date_range === spring2 || date_range === fall1 && date_range === fall2
    true
  else
    false
  end
end

def nyccoba_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')

  ssn_number = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)

  ssn_number
rescue => e
  nyccoba_feed_errors('SSN ' + e.message)
end

def benefit_coverage_create(total_arr)
  total_arr.each do |record|
    benefit_coverage = BenefitCoverage.where(
      employee_id: record[:employee_id],
      employee_benefit_id: record[:employee_benefit_id],
      first_name: record[:first_name],
      last_name: record[:last_name]
    ).first_or_create

    benefit_coverage.update(social_security_number: record[:social_security_number], relationship: record[:relationship], birthday: record[:birthday])
  end
end

# bundle exec rake "nyccoba_members_import[nyccoba, members.csv]"
# bundle exec rake "nyccoba_dependents_import[nyccoba, nyccoba-dependents-disability-retired.csv]"
# bundle exec rake "nyccoba_dependents_import[nyccoba, Sheet 1-dependents.csv]"
