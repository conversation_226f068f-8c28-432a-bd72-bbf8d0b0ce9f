# frozen_string_literal: true

require 'csv'

desc 'import data'

task :nyccoba_social_security_number_update, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  total_arr = []
  count = 0
  Employee.kept.find_each do |employee|
    @row_number = employee.full_name
    ssn = employee.social_security_number
    next if ssn.blank?

    parsed_ssn = nyccoba_parse_ssn(ssn)
    employee.social_security_number = parsed_ssn

    total_arr << employee

    if count == 500
      Employee.import total_arr.flatten, on_duplicate_key_update: [:social_security_number]
      total_arr = []
      count = 0
    end
    count += 1
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  Employee.import total_arr.flatten, on_duplicate_key_update: [:social_security_number]

  CSV.open("#{Rails.root}/nyccoba_social_security_number_update_errors_#{Time.new.strftime("%d-%m-%Y-%H:%M:%S")}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_active_member_start_date_update, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  total_arr = []
  count = 0
  Employee.kept.includes(:employee_employment_statuses).where(employee_employment_statuses: { employment_status_id: EmploymentStatus.kept.where("lower(name) = ?", 'active').first.id}).find_each do |employee|
    @row_number = employee.full_name

    employee_employment_status = employee.employee_employment_statuses.first
    employee_employment_status.start_date = employee.start_date
    total_arr << employee_employment_status

    if count == 500
      EmployeeEmploymentStatus.import total_arr.flatten, on_duplicate_key_update: [:start_date]
      total_arr = []
      count = 0
    end
    count += 1
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  EmployeeEmploymentStatus.import total_arr.flatten, on_duplicate_key_update: [:start_date]

  CSV.open("#{Rails.root}/nyccoba_active_member_start_date_update_errors#{Time.new.strftime("%d-%m-%Y-%H:%M:%S")}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_disabled_retired_benefit_coverage_expiration, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  current_account = Account.find_by(subdomain: args[:account])
  @errors = {}

  retire_benefit_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages',
                                                                     'retire_benefit_coverage_expire_age') if current_account.present?

  if retire_benefit_coverage_expire_age.present?

    Employee.kept.includes(:employee_employment_statuses, :benefit_coverages).where(employee_employment_statuses: { employment_status_id: EmploymentStatus.kept.where('lower(name) = ?', 'disability retired').first.id }, benefit_coverages: { semester: 'N' }).each do |employee|
      @row_number = employee.full_name || ''
      benefit_coverages = employee.benefit_coverages
      benefit_coverages.each do |benefit_coverage|
        now = Time.now
        age = now.year - benefit_coverage.birthday.to_time.year - (benefit_coverage.birthday.to_time.change(:year => now.year) > now ? 1 : 0) if benefit_coverage.birthday
        if (age.present? && retire_benefit_coverage_expire_age.present? && age > retire_benefit_coverage_expire_age && benefit_coverage.relationship.present? &&
          %w[child step_child].include?(benefit_coverage.relationship&.downcase))
          retire_expires_at = benefit_coverage.birthday + retire_benefit_coverage_expire_age.year
          benefit_coverage.update_columns(expires_at: retire_expires_at)
        end
      rescue => e
        p @row_number, e.message
        nyccoba_feed_errors(e.message)
      end
    end
  end
  CSV.open("#{Rails.root}/nyccoba_disability_benefit_coverages_expiration_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_other_member_start_date_update, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  total_employment_status_hash = {}
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status
  csv_file.each do |row|
    next if row['InternalStatus']&.downcase == 'deleted'

    ssn = row['SSN'] || ''
    @row_number = ssn
    first_name = row['FirstName'] || ''
    last_name = row['LastName'] || ''

    if @row_number.blank?
      if first_name.blank? || last_name.blank?
        nyccoba_feed_errors('Mandatory details not present')
        next
      end
      @row_number = "#{row['FirstName']} #{row['LastName']}"
    end

    if ssn.present?
      parsed_ssn = nyccoba_parse_ssn(ssn)
      employees = Employee.kept.where(social_security_number: parsed_ssn)
    end

    if ssn.blank? || employees.blank? || employees.count > 1

      employees = Employee.kept.where('lower(first_name) = ? and lower(last_name ) = ?', first_name&.downcase, last_name&.downcase)
      if employees.blank?
        nyccoba_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyccoba_feed_errors('More than One employee found')
        next
      end
    end
    employee = employees.first
    employment_status_id = if row['Status'].present? && total_employment_status_hash[@value_downcase.call(row['Status']).to_sym].present?
                             total_employment_status_hash[@value_downcase.call(row['Status']).to_sym]
                           elsif row['Status'].present?
                             create_associated_model_and_values('EmploymentStatus', row['Status'], total_employment_status_hash)&.id
                           end

    next if employment_status_id.blank?

    employee_employment_statuses = employee.employee_employment_statuses.kept.where(employment_status_id: employment_status_id)
    if employee_employment_statuses.count > 1
      nyccoba_feed_errors('More than one Union Status found with same name')
      next
    end

    employee_employment_status = employee_employment_statuses.first_or_initialize
    start_date = if row['Status'].present? && row['Status']&.downcase == 'active'
                   Date.parse(row['Appointed']) if row['Appointed'].present?
                 else
                   Date.parse(row['StatusChangeDate']) if row['StatusChangeDate'].present?
                 end
    employee_employment_status.start_date = start_date
    employee_employment_status.save(validate: false)

  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/nyccoba_other_member_start_date_update_errors_#{Time.new.strftime("%d-%m-%Y-%H:%M:%S")}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :nyccoba_duplicate_members_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # idMembers,SSN,FirstName,LastName,Address1,Address2,City,State,ZipCode,Telephone,Status,Sex,MaritalStatus,ShieldNumber,Chief,PensionNumber,DOB,Appointed,Rank,Command, Email,PromCaptain,Resigned

  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]
  gender_hash = {}
  marital_status_hash = {}
  offices_hash = {}
  rank_hash = {}
  total_employment_status_hash = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }

  Employee.skip_callback :save, :after, :update_benefit_address
  if ENV['APP_ENV'] == 'development'
    Employee.skip_callback :commit, :after, :update_legislative_detail
  else
    Employee.skip_callback :commit, :after, :update_legislative_detail
  end
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['FirstName'] || ''
    last_name = row['LastName'] || ''
    ssn = row['SSN'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['SSN']).blank?

      nyccoba_feed_errors('Mandatory details not present')
      next
    end

    active_or_deleted = row['InternalStatus']

    if ssn.present?
      parsed_ssn = nyccoba_parse_ssn(ssn)
      employees = Employee.kept.where(social_security_number: parsed_ssn)
    end

    if ssn.blank? || employees.blank? || employees.count > 1
      employees = Employee.kept.where('lower(first_name) = ? and lower(last_name ) = ?', first_name&.downcase, last_name&.downcase)
    end

    next if active_or_deleted.downcase == 'deleted' && employees.blank?

    if active_or_deleted.downcase == 'deleted' && employees.count == 1
      employee = employees.first
      employee.discard
      next
    elsif active_or_deleted.downcase == 'deleted' && employees.count > 1
      nyccoba_feed_errors('More than one employee found, Ignoring delete for this member')
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One employee found')
      next
    end

    employee = employees.first_or_initialize
    employee.first_name = first_name
    employee.last_name = last_name
    employee.social_security_number = parsed_ssn
    employee.street = row['Address1'] || ''
    employee.apartment = row['Address2'] || ''
    employee.city = row['City'] || ''
    employee.state = row['State'] || ''
    employee.zipcode = if row['ZipCode'].present?
                         row['ZipCode'].slice(0, 5)
                       else
                         ''
                       end
    employee.gender_id = if row['Sex'].present? && gender_hash[@value_downcase.call(row['Sex']).to_sym].present?
                           gender_hash[@value_downcase.call(row['Sex']).to_sym]
                         elsif row['Sex'].present?
                           create_associated_model_and_values_duplicated('Gender', row['Sex'], gender_hash)&.id
                         end
    employee.marital_status_id = if row['MaritalStatus'].present? && marital_status_hash[@value_downcase.call(row['MaritalStatus']).to_sym]
                                   marital_status_hash[@value_downcase.call(row['MaritalStatus']).to_sym]
                                 elsif row['MaritalStatus'].present?
                                   create_associated_model_and_values_duplicated('MaritalStatus', row['MaritalStatus'], marital_status_hash)&.id
                                 end
    employee.shield_number = row['ShieldNumber'] || ''
    employee.birthday = row['DOB'].present? ? Date.parse(row['DOB']) : ''
    employee.previous_shield_number = row['PensionNumber'] || ''
    employee.start_date = row['Appointed'].present? ? Date.parse(row['Appointed']) : ''


    if employee.save!
      new_record = employee.created_at === (Date.today.beginning_of_day..Date.today.end_of_day)
      # COMMANDS
      command_id = if row['Command'].present? && offices_hash[@value_downcase.call(row['Command']).to_sym].present?
                     offices_hash[@value_downcase.call(row['Command']).to_sym]
                   elsif row['Command'].present?
                     create_associated_model_and_values_duplicated('Office', row['Command'], offices_hash)&.id
                   end
      if command_id.present?
        employee_offices = employee.employee_offices.where(office_id: command_id).first_or_initialize
        employee_offices.save(validate: false)
      end

      # Employment Status
      employment_status_id = if row['Status'].present? && total_employment_status_hash[@value_downcase.call(row['Status']).to_sym].present?
                               total_employment_status_hash[@value_downcase.call(row['Status']).to_sym]
                             elsif row['Status'].present?
                               create_associated_model_and_values_duplicated('EmploymentStatus', row['Status'], total_employment_status_hash)&.id
                             end

      if employment_status_id.present?
        employee_employment_statuses = employee.employee_employment_statuses.where(employment_status_id: employment_status_id).first_or_initialize
        employee_employment_statuses.start_date = if row['Status'].present? && row['Status']&.downcase == 'active'
                                                    Date.parse(row['Appointed']) if row['Appointed'].present?
                                                  else
                                                    Date.parse(row['StatusChangeDate']) if row['StatusChangeDate'].present?
                                                  end
        employee_employment_statuses.save(validate: false)
      end
      # Ranks
      rank_id = if row['Rank'].present? && rank_hash[@value_downcase.call(row['Rank']).to_sym].present?
                  rank_hash[@value_downcase.call(row['Rank']).to_sym]
                elsif row['Rank'].present?
                  create_associated_model_and_values_duplicated('Rank', row['Rank'], rank_hash).id
                end

      if rank_id.present?
        employee_ranks = employee.employee_ranks.where(rank_id: rank_id).first_or_initialize
        captain_start_date = row['PromCaptain'] || ''
        employee_ranks.start_date = Date.parse(captain_start_date) if rank_hash.key(rank_id).to_s == 'captain' && captain_start_date.present?
        employee_ranks.save(validate: false)
      end

      if new_record || employee.contacts.blank?
        contacts_hash.last[:value] = row['Email']
        contacts_hash.last(2).first[:value] = nyccoba_parse_phone(row['Telephone']) || ''
        # CONTACTS
        employee.contacts.import contacts_hash
      else
        personal_email = employee.contacts.where( contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).first_or_initialize
        personal_email.value = row['Email'] || ''
        personal_email.save!
        personal_phone = employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).first_or_initialize
        personal_phone.value = nyccoba_parse_phone(row['Telephone']) || ''
        personal_phone.save!
      end

    else
      @errors[@row_number] = employee.errors.full_messages
    end
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_nyccoba_duplicate_members_import_errors_#{Time.new.strftime("%d-%m-%Y-%H:%M:%S")}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors', 'MemberID']
    @errors.each do |error|
      csv << error
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  if ENV['APP_ENV'] == 'development'
    Employee.set_callback :commit, :after, :update_legislative_detail
  else
    Employee.set_callback :commit, :after, :update_legislative_detail
  end
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :nyccoba_reimport_references, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  employee_reference_data = []
  count = 0
  csv_file.each do |row|
    @row_number = row['SSN'] || ''
    reference = row['REFNO'] || ''
    next if @row_number.blank? || reference.blank?

    employee_data = reference_data(@row_number, reference)
    next unless employee_data

    employee_reference_data << employee_data

    if count == 500
      Employee.import employee_reference_data.flatten, on_duplicate_key_update: [:a_number], validate: false
      employee_reference_data = []
      count = 0
    end
    count += 1

  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  Employee.import employee_reference_data.flatten, on_duplicate_key_update: [:a_number], validate: false

  generate_csv_errors_report(args[:file_path])
end

def nyccoba_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')

  ssn_number = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)

  ssn_number
rescue => e
  nyccoba_feed_errors('SSN ' + e.message)
end

def nyccoba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def create_associated_model_and_values_duplicated(model_name, value, hash)
  return unless value.present?

  if (model_value = model_name.constantize.where('lower(name) = ?', value&.downcase)&.first).present?
    model_value
  else
    model_value = model_name.constantize.create!(name: value)
  end
  hash[@value_downcase.call(value).to_sym] = model_value.id if hash[value.to_sym].blank? || (hash[value.to_sym].present? && hash[value.to_sym] != model_value.id)
  model_value
end

def reference_data(ssn, reference)
  parsed_ssn = nyccoba_parse_ssn(ssn)
  employees = Employee.kept.where(social_security_number: parsed_ssn)
  if employees.count == 1
    employee = employees.first
    employee_hash = {
      id: employee.id,
      a_number: reference
    }
  elsif employees.count > 1
    nyccoba_feed_errors('More than one Employee Found')
    return false
  else
    nyccoba_feed_errors('Invalid Employee or Employee was missing')
    return false
  end
  employee_hash
end

def generate_csv_errors_report(name)
  CSV.open("#{Rails.root}/#{name}_errors#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Employee Name', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

# bundle exec rake 'nyccoba_social_security_number_update[nyccoba]'
# bundle exec rake 'nyccoba_active_member_start_date_update[nyccoba]'
# bundle exec rake "nyccoba_other_member_start_date_update[nyccoba, nyccoba_mar21_members.csv]"
# bundle exec rake "nyccoba_disabled_retired_benefit_coverage_expiration[nyccoba]"
# # bundle exec rake 'nyccoba_reimport_references[nyccoba, FILE_NAME]'
