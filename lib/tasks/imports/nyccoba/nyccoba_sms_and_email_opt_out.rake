# frozen_string_literal: true

require 'csv'

task :nyccoba_sms_opt_out, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  csv_file.each do |row|
    @row_number = row['Member Name'] || ''
    name = @row_number.split

    first_name = name.first&.downcase
    last_name = nil
    middle_name = nil

    case name.length
    when 2
      last_name = name.last.downcase
    when 3
      if name[1].length == 1
        middle_name = name[1].downcase
        last_name = name[2].downcase
      else
        last_name = name[1..].join(' ').downcase
      end
    when 4
      if name.any? { |part| %w[JR JR. III COOPER].include?(part.upcase) }
        if name.length >= 4
          middle_name = name[1].downcase
          last_name = name[2..].join(' ').downcase
        end
      else
        middle_name = name[1].downcase
        last_name = name[2..].join(' ').downcase
      end
    end

    employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', first_name, last_name)

    if employees.count > 1 && middle_name
      employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ? AND lower(middle_name) = ?', first_name, last_name, middle_name)
    end

    if employees.blank?
      nyccoba_feed_errors('Employee not Found')
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One Employee Found')
      next
    end
    employee = employees.first
    employee.update_columns(sms_opt_out: true, updated_at: Time.now) if employee
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_report_errors(t.name + '_')
end

task :nyccoba_sms_and_email_opt_out, %i[account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  Employee.joins(:employment_statuses).where("LOWER(employment_statuses.name) IN (?)", [ "active deceased", "deceased", "retired deceased", "terminated", "resigned", "promoted" ]).find_each do |employee|
    employee.update_columns(sms_opt_out: true, email_opt_out: true)
  end
end

# bundle exec rake 'nyccoba_sms_opt_out[nyccoba, nyccoba_sms_opt_out_file.csv]'
# bundle exec rake 'nyccoba_sms_and_email_opt_out[nyccoba]'
