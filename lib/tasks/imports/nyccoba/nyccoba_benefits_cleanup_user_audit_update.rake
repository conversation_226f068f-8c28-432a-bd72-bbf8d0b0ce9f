# frozen_string_literal: true

require 'csv'
desc 'User Audit benefits clean up - Updates'

task :nyccoba_benefits_cleanup_user_audit_update, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  employee_benefits_hash_id = {}
  employee_benefit_hash.each do |key, value|
    key_id = Benefit.where('lower(name) = ?', key.downcase).first.id
    value_id = Benefit.where('lower(name) in (?)', value.map(&:downcase)).pluck(:id)
    employee_benefits_hash_id[key_id] = value_id
  end
  benefit_discarded_ids = employee_benefits_hash_id.values.flatten

  UserAudit.where('whodunnit is not null').where('item_type = ?', 'EmployeeBenefit').find_each do |user_audit|
    object_changes = user_audit.object_changes
    object_json_original = PaperTrail.serializer.load(object_changes)
    object_json = object_json_original.deep_dup
    benefit_ids = object_json['benefit_id']
    if object_json.keys.include?('benefit_id') && (benefit_ids & benefit_discarded_ids).present?
      benefit_ids_change = []
      benefit_ids.each do |benefit_id|
        (benefit_ids_change << '') && next if benefit_id.nil?

        employee_benefits_hash_id.select { |k, v| benefit_ids_change << k if v.include?(benefit_id) }
      end
      benefit_ids_change.each_with_index { |x, i| x == '' ? benefit_ids_change[i] = nil : '' }
      object_json['benefit_id'] = benefit_ids_change
      do_update = (object_json.values.flatten != object_json_original.values.flatten)
      user_audit.update_columns(object_changes: object_json.to_yaml) && puts(user_audit&.employee&.full_name) if do_update
    end
  end
end

task :nyccoba_retirement_start_date_update, %w[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  format_date = ->(date) { date.present? ? date.strftime('%Y-%m-%d') : '' }
  retired_ids = EmploymentStatus.kept.where('lower(name) in (?)', ['retired', 'disability retired']).pluck(:id)
  retired_deceased_ids = EmploymentStatus.kept.where('lower(name) in (?)', ['retired deceased']).pluck(:id)

  @errors = {}
  CSV.open("#{Rails.root}/coba_2015_2016_first_file_#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << [csv_file.headers, 'UnionStatus'].flatten
    csv_file.each do |row|
      @row_number = row['SubscriberSSN']
      member_status = ''
      formated_employment_status_date = ''

      employees = Employee.kept.includes(employee_employment_statuses: :employment_status).where(social_security_number: nyccoba_parse_ssn(row['SubscriberSSN']&.rjust(9, '0')))
      unless check_any_errors(employees, true)
        employee = employees.first
        employment_status_name = employee.employment_status_name
        employment_status = employee.employee_employment_statuses.where('(employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?)', Date.today)
        employment_status_ids = employment_status.pluck(:employment_status_id)
        if employment_status_ids.count == 1 && retired_ids.include?(employment_status_ids.first)
          member_status = 'ret_or_dis'
        elsif employment_status_ids.count == 1 && retired_deceased_ids.include?(employment_status_ids.first)
          member_status = 'ret_dec'
        end

        formated_employment_status_date = if employment_status.count == 1 && (member_status != 'ret_dec' || member_status.blank?)
                                            format_date.call(employment_status.first.start_date)
                                          elsif member_status == 'ret_dec'
                                            'ret_dec'
                                          elsif employment_status.count > 1 || employment_status.count.zero?
                                            nyccoba_feed_errors('No Union Status or More than one UnionStatus found')
                                            ''
                                          end

      end

      csv << [row['MemberSSN'], row['FName'], row['LName'], row['DOB'], row['Gender'], row['RelCode'], row['ContractID'], formated_employment_status_date, row['SubscriberSSN'], employment_status_name]
    end
  end
  generate_csv_report_errors('coba_2015_2016_first_file_errors_')
end

task :nyccoba_retirement_deceased_start_date_update, %w[account file_path file_path2] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  members_file = File.read(args[:file_path2])
  @csv_members_file ||= CSV.parse(members_file, headers: true)

  format_date = ->(date) { date.present? ? date.strftime('%Y-%m-%d') : '' }

  @errors = {}
  CSV.open("#{Rails.root}/coba_2015_2016_#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << csv_file.headers
    csv_file.each do |row|
      @row_number = row['SubscriberSSN']
      formated_status_date = if row['RetireDate'] == 'ret_dec'
                               parse_retire_date_from_members_file
                             else
                               row['RetireDate']
                             end
      formated_status_date = format_date.call(Date.parse(formated_status_date)) if formated_status_date.present?

      csv << [row['MemberSSN'], row['FName'], row['LName'], row['DOB'], row['Gender'], row['RelCode'], row['ContractID'], formated_status_date, row['SubscriberSSN'], row['UnionStatus']]
    end
  end
  generate_csv_report_errors('coba_2015_2016_errors_')
end

task :nyccoba_employee_benefit_addition, %w[account start_date] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  start_date = Date.parse(args[:start_date])
  benefit_ids = Benefit.kept.where('lower(name) IN (?)', %w[prescription dental optical]).ids
  employees = Employee.kept.where(start_date: start_date)

  ActiveRecord::Base.transaction do
    employees.each do |employee|
      @row_number = employee.full_name
      benefit_ids.each do |benefit_id|
        employee_benefit = employee.employee_benefits.where(benefit_id: benefit_id).first_or_create!

        employee_benefit.update!(start_date: start_date) if employee_benefit.end_date.nil? || employee_benefit.end_date > start_date
      end
    end
  rescue StandardError => e
    @errors[@row_number] = e.message
    p @row_number, e.message
  end
  generate_csv_report_errors(t.name)
end

task :nyccoba_delegate_list_update, %w[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  position_id = Position.kept.where('lower(name) = ?', 'delegate').pluck(:id).first
  employee_ids = []
  date = Date.parse('July 1, 2024')

  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    next if first_name.blank? || last_name.blank?

    shield = row['Shield'] || ''
    address = row['Address'] || ''
    @row_number = "#{first_name} #{last_name}"

    valid_employee = find_employee(first_name, last_name, shield, address)
    next unless valid_employee

    employee_ids << valid_employee.id
    start_and_end_employee_positions(position_id, date, valid_employee)
  end
  start_and_end_employee_positions(position_id, date, nil, employee_ids)
  generate_csv_report_errors(t.name)
end

task :legal_fringe_and_health_share_reports, %w[account employment_status report_type report_format] => :environment do |_t, args|
  account = args[:account]
  employment_status = args[:employment_status]
  report_type = args[:report_type]
  report_format = args[:report_format]
  Apartment::Tenant.switch!(account)
  filepath = "#{Rails.root}/#{account}_#{report_type}_for_#{employment_status}_employees.#{report_format}"
  is_csv_file = report_format == 'csv'
  employment_status_id = EmploymentStatus.kept.where('lower(name) = ?', employment_status.downcase).pluck(:id).first
  data_array = []
  csv_data = []
  employees = Employee.kept.joins(:employee_employment_statuses)
                      .where('employee_employment_statuses.employment_status_id = ? and (employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date >= ?)', employment_status_id, Date.today)
  employees.each do |employee|
    employee_data = get_report_details(employee, employment_status, employment_status_id, report_type)
    employee_data.flatten!
    csv_data << employee_data.map { |data| data.strip.empty? ? '' : data } if is_csv_file
    data_array << employee_data.join
  end

  if report_format == 'csv'
    header = csv_header(report_type, employment_status)
    CSV.open(filepath, 'w') do |csv|
      csv << header
      csv_data.each do |data|
        csv << data
      end
    end
  else
    File.open(filepath, 'wb') do |file|
      data_array.each do |data|
        file.puts data
      end
    end
  end
end

task :nyccoba_report, %w[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  is_potentials_over65_file = csv_file.headers.include?('SubscriberSSN')
  @errors = {}
  retirement_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'retired').first.id
  retired_deceased_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'retired deceased').first.id
  csv_file.each do |row|
    ssn = is_potentials_over65_file ? row['SubscriberSSN'] || '' : row['MemSSN'] || ''
    next if ssn.blank?

    @row_number = parse_ssn(ssn)
    employee = Employee.kept.where(social_security_number: @row_number).first
    first_name = is_potentials_over65_file ? row['SubscriberFname'] || '' : row['FName'] || ''
    last_name = is_potentials_over65_file ? row['SubscriberLname'] || '' : row['LName'] || ''

    next if employee.blank? && (first_name.blank? || last_name.blank?)

    employee = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase) if employee.blank?

    if employee.blank?
      nyccoba_feed_errors('Member Not Found')
      next
    end

    retirement_date = employee.employee_employment_statuses.where('end_date IS NULL').where(employment_status_id: retirement_status_id).first&.start_date
    retired_deceased_present = employee.employee_employment_statuses.where('end_date IS NULL').where(employment_status_id: retired_deceased_status_id).first&.start_date.present? if retirement_date.blank?

    if retired_deceased_present
      nyccoba_feed_errors('Member with only Retired Deceased Status')
      next
    end

    if is_potentials_over65_file
      row['RetireDate'] = retirement_date&.strftime('%m/%d/%y')
    else
      row['Last Day Worked'] = retirement_date&.strftime('%m/%d/%y')
    end

    CSV.open(args[:file_path], 'w', write_headers: true, headers: csv_file.headers) do |csv|
      csv_file.each do |row|
        csv << row
      end
    end
  end
  generate_csv_report_errors(is_potentials_over65_file ? 'nyccoba_potentials_over65_report_' : 'medicare_disabled_report_')
end

task :nyccoba_commands_update, %w[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  old_office_id = Office.kept.where('LOWER(name) = ?', 'mdc').first.id
  new_office = Office.kept.where('LOWER(name) = ?', 'mncts').first_or_create!(name: 'MNCTS')

  @errors = {}

  csv_file.each do |row|
    @row_number = row['Member Name'] || ''
    name = split_employee_name(@row_number)
    shield_number = row['Shield #'] || ''

    employees = Employee.kept.where('(lower(first_name) = ? and lower(last_name) = ?)', name.first.downcase, name.last.downcase)
    employees = employees.where(shield_number: shield_number) if employees.count > 1

    if employees.blank?
      nyccoba_feed_errors('Employee not Found')
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One Employee Found')
      next
    end
    employee = employees.first
    employee_offices = employee.employee_offices.where(office_id: old_office_id)

    if employee_offices.blank?
      nyccoba_feed_errors('No Existing Commands Found')
    else
      employee_offices.update_all(office_id: new_office.id)
    end
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_report_errors(t.name + '_')
end

task :nyccoba_sms_undeliverables, %w[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  employees_data = []

  csv_file.each do |row|
    @row_number = row['Message UUID'] || ''

    employees = NotificationTracker.where(message_id: @row_number).map(&:employee)
    if employees.blank?
      nyccoba_feed_errors("Employee with Message_id:#{@row_number} was not Found")
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One Employee Found')
      next
    end

    employee = employees.first
    employees_data << format_employee_data(employee)
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_employee_data(employees_data, 'message_undelivered_employees.csv')
  generate_csv_report_errors(t.name + '_')
end

def format_employee_data(employee)
  {
    first_name: employee.first_name,
    last_name: employee.last_name,
    ssn: employee.social_security_number,
    street: employee.street,
    apartment: employee.apartment,
    city: employee.city,
    state: employee.state,
    zipcode: employee.zipcode,
    phone: employee.work_phone,
    email: employee.work_email
  }
end

def generate_csv_employee_data(employees_data, output_file_path)
  CSV.open(output_file_path, 'w') do |csv|
    csv << ['First Name', 'Last Name', 'SSN', 'Address', 'Apartment', 'City', 'State', 'Zip', 'Phone', 'Email']
    employees_data.each do |data|
      csv << [data[:first_name], data[:last_name], data[:ssn], data[:street], data[:apartment], data[:city], data[:state], data[:zipcode], data[:phone], data[:email]]
    end
  end
end

def get_report_details(employee, employment_status, employment_status_id, report_type)
  employee_data = []
  active_status = employment_status.downcase == 'active'
  retirement_start_date = employee.employee_employment_statuses.where('employment_status_id = ? and (end_date is NULL OR end_date >= ?)', employment_status_id, Date.today).pluck(:start_date).first unless active_status
  legal_fringe_report = report_type == 'legal_fringe_report'
  employee_data << get_common_report_details(employee, legal_fringe_report, active_status)
  if legal_fringe_report
    employee_data << format_text_fields('', 1)
    employee_data << '39.84'.rjust(9, '0') # Fringe Amount
    employee_data << format_text_fields('', 67)
    employee_data << format_text_fields(employee.street, 35) # Street Address
    employee_data << format_text_fields('', 35)
    employee_data << format_text_fields(employee.city, 20) # City
    employee_data << format_text_fields(employee.state, 2) # State
    employee_data << format_text_fields(employee.zipcode, 9) # zip+4
    employee_data << format_text_fields(format_date(employee.start_date), 8)
    employee_data << format_text_fields(format_date(retirement_start_date), 8)
    employee_data << format_text_fields('', 93)
  else
    employee_data << format_text_fields('', 228)
    employee_data << (active_status ? '1596.00'.rjust(7, '0') : format_text_fields('', 7)) # Health Share (Union)
    employee_data << format_text_fields(format_date(employee.start_date), 8)
    if active_status
      employee_data << format_text_fields('', 44)
    else
      employee_data << format_text_fields(format_date(retirement_start_date), 8)
      employee_data << format_text_fields('', 36)
    end
  end
  employee_data
end

def get_common_report_details(employee, legal_fringe_report, active_status)
  employee_data = []
  employee_data << format_text_fields(active_status ? 'A' : 'R', 1) # Active / Retiree Indicator
  employee_data << format_text_fields(legal_fringe_report ? 'L' : 'U', 1) # File Type Indicator
  employee_data << format_text_fields('C', 1) # Entity
  employee_data << format_text_fields('610', 3) # Union Code
  employee_data << format_text_fields('2024', 4) # Tax Year
  employee_data << format_text_fields(legal_fringe_report ? '4' : '5', 1) # Quarter
  employee_data << format_text_fields('', 7)
  employee_data << format_text_fields(employee.last_name, 15) # Last Name
  employee_data << format_text_fields(employee.first_name, 14) # First Name
  employee_data << format_text_fields('', 1) # Middle Initial
  employee_data << format_text_fields(employee.social_security_number.delete('-'), 9) # Social Security Number
  employee_data
end

def csv_header(report_type, employment_status)
  header = ['Active / Retiree Indicator', 'File Type Indicator', 'Entity', 'Union Code', 'Tax Year', 'Quarter', 'Filler',
            'Last Name', 'First Name', 'Middle Initial', 'Social Security Number', 'Filler']
  if report_type == 'legal_fringe_report'
    header.concat(['Fringe Amount', 'Filler', 'Street Address', 'Filler', 'City', 'State', 'Zip+4', 'Date of Appointment', 'Retirement Start date', 'Filler'])
  else
    header.concat(['Health Share (Union)', 'Date of Appointment'])
    employment_status == 'active' ? header.concat(['Filler']) : header.concat(['Retirement Start date', 'Filler'])
  end
  header
end
def format_text_fields(text, length)
  text.ljust(length, ' ').slice(0, length)
end

def format_date(date)
  date.strftime('%Y%m%d')
rescue StandardError
  ''
end

def parse_retire_date_from_members_file
  retirement_date = ''
  member_ssn = @row_number.rjust(9, '0')
  @csv_members_file.each do |row|
    next unless row['SSN'] == member_ssn

    retirement_date = row['Retirement']
    break
  end
  retirement_date
end

def find_employee(first_name, last_name, shield, address)
  employees = Employee.kept.where('lower(first_name) like ? and lower(last_name) like ?', "#{first_name.downcase}%", "#{last_name.downcase}%")
  if employees.count > 1
    if shield.present?
      employees = employees.where(shield_number: shield)
    else
      address_array = address.split(',').map(&:strip)
      state_zip_parts = address_array.split(' ')
      apartment = address_array.size == 3 ? address_array[1] : ''

      employees = employees.where(street: address_array.first, apartment: apartment, city: address_array[-2], state: state_zip_parts[0], zipcode: state_zip_parts[1])
    end
  end
  check_employees_count(employees)
end

def check_employees_count(employees)
  if employees.blank?
    nyccoba_feed_errors('Invalid Employee or Employee was missing')
    false
  elsif employees.count > 1
    nyccoba_feed_errors('More than One Employee found')
    false
  else
    employees.first
  end
end

def start_and_end_employee_positions(position_id, date, employee = nil, employee_ids = nil)
  ActiveRecord::Base.transaction do
    if employee.present?
      existing_active_positions = employee.employee_positions.kept.where('end_date IS NULL OR end_date > ?', date)
      existing_active_positions.update_all(end_date: date) if existing_active_positions.exists?

      EmployeePosition.create!(employee_id: employee.id, position_id: position_id, start_date: date)
    elsif employee_ids.present?
      employee_positions = EmployeePosition.kept.where('position_id = ? and employee_id NOT IN (?) and (end_date is NULL or end_date > ?)', position_id, employee_ids, date)
      employee_positions.update_all(end_date: date) if employee_positions.exists?
    end
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
end

def split_employee_name(name)
  words = name.split(' ')

  if words.length == 2
    first_name = words.first
    last_name = words.last
  elsif %w[JR JR. III COOPER].include?(words.last)
    first_name = words[0...-2].join(' ')
    last_name = words[-2..].join(' ')
  elsif words.length >= 4
    first_name = words.first
    last_name = words[1..].join(' ')
  else
    first_name = words[0...-1].join(' ')
    last_name = words.last
  end

  [first_name, last_name]
end

## bundle exec rake 'nyccoba_benefits_cleanup_user_audit_update[nyccoba]'
# bundle exec rake 'nyccoba_retirement_start_date_update[nyccoba, nyccoba_2015_2016.csv]'
# bundle exec rake 'nyccoba_retirement_deceased_start_date_update[nyccoba, coba_2015_2016_first_file_02-07-2024-13:47.csv, nyccoba_retired_deceased_members.csv]'
# bundle exec rake 'nyccoba_delegate_list_update[nyccoba, Delegate List July 2024.csv]'
# bundle exec rake 'nyccoba_delegate_list_update[nyccoba, Delegate List July 2024 - 2.csv]'
# bundle exec rake 'legal_fringe_and_health_share_reports[nyccoba, retired, legal_fringe_report, txt]'
# bundle exec rake 'legal_fringe_and_health_share_reports[nyccoba, active, health_share_report, txt]'
# bundle exec rake 'legal_fringe_and_health_share_reports[nyccoba, retired, health_share_report, txt]'
# bundle exec rake 'legal_fringe_and_health_share_reports[nyccoba, active, health_share_report, csv]'
# bundle exec rake 'legal_fringe_and_health_share_reports[nyccoba, retired, health_share_report, csv]'
# bundle exec rake 'nyccoba_report[nyccoba, BASMI 1_COBA_(2019-2022) 2/PotentialsOver65-Table 1.csv]'
# bundle exec rake 'nyccoba_report[nyccoba, BASMI 1_COBA_(2019-2022) 2/Medicare Disabled-Table 1.csv]'
# bundle exec rake 'nyccoba_commands_update[nyccoba, nyccoba_commands_update.csv]'
# bundle exec rake 'nyccoba_sms_undeliverables[nyccoba, NYC COBA Text Undeliverables.csv]'
# bundle exec rake 'nyccoba_employee_benefit_addition[nyccoba, 19/12/2024]'
