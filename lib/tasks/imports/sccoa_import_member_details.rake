# frozen_string_literal: true

require 'csv'

desc 'import data'
# bundle exec rake 'sccoa_import_member_details[FILENAME]'
task :sccoa_import_member_details, [:file_path] => :environment do |_t, args|

  @current_account = Account.find_by(subdomain: 'sccoa')
  Apartment::Tenant.switch!(@current_account.subdomain)

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # First Name,Middle Initial,Middle Name,Last Name,Name Suffix,Previous Shield,Shield,Address,City,State,Zip,Birth Date,
  # Facility,Past Facility,E-mail,Backup Personal E-mail,Notes,Mobile Phone,Home Phone,Rank,Start Date,Gender

  active_employement_status = EmploymentStatus.where(name: 'Active').first_or_create
  current_platoon = Platoon.where(name: 'Current').first_or_create

  csv_file.each do |row|

    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    mi = row['Middle Initial'] || row['Middle Name'] || ''
    suffix = row['Name Suffix'] || ''

    @row_number = [first_name, last_name, mi, suffix].reject { |x| x.blank? }.join(', ')
    shield_number = row['Shield'] || ''
    prev_shield_number = row['Previous Shield'] || ''
    address = row['Address'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zipcode = row['Zip'].present? ? row['Zip'].split('-').first.rjust(5, '0') : ''
    birth_date = sccoa_parse_date(row['Birth Date'], 'birth_date')
    email = row['E-mail']
    backup_email = row['Backup Personal E-mail']
    notes = row['Notes'] || ''
    mobile_phone = sccoa_parse_phone(row['Mobile Phone'])
    home_phone = sccoa_parse_phone(row['Home Phone'])
    rank = sccoa_check_rank(row['Rank'])
    start_date = sccoa_parse_date(row['Start Date'], 'start_date')
    gender = sccoa_check_gender(row['Gender'])
    facility, crew, unit = parse_facility_crew_unit(row['Facility'], 'Present')
    past_facility, past_crew, past_unit = parse_facility_crew_unit(row['Past Facility'], 'Past')

    flsa_date = Date.parse('28/2/2009')

    employee = Employee.new

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = address
    employee.city = city
    employee.state = state
    employee.zipcode = zipcode
    employee.notes = notes
    employee.shield_number = shield_number
    employee.previous_shield_number = prev_shield_number
    employee.start_date = start_date
    employee.birthday = birth_date
    employee.gender_id = gender.id if gender
    employee.platoon_id = current_platoon.id if current_platoon
    employee.staff_member = start_date >= flsa_date if start_date.present?

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: mobile_phone).save! if mobile_phone
      employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: home_phone).save! if home_phone
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: nil).save!
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: email).save! if email.present?
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: backup_email).save! if backup_email.present?

      employee.employee_employment_statuses.new(employment_status_id: active_employement_status).save(validate: false) if active_employement_status.present?

      employee.employee_ranks.new(rank_id: rank.id).save(validate: false) if rank.present?

      employee.employee_facilities.new(facility_id: facility.id).save(validate: false) if facility.present?
      employee.employee_facilities.new(facility_id: past_facility.id).save(validate: false) if past_facility.present?

      employee.employee_officer_statuses.new(officer_status_id: crew.id).save(validate: false) if crew.present?
      employee.employee_officer_statuses.new(officer_status_id: past_crew.id).save(validate: false) if past_crew.present?

      employee.employee_offices.new(office_id: unit.id).save(validate: false) if unit.present?
      employee.employee_offices.new(office_id: past_unit.id).save(validate: false) if past_unit.present?
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    sccoa_feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{Apartment::Tenant.current}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :sccoa_upload_member_details, [:file_path] => :environment do |_t, args|

  @current_account = Account.find_by(subdomain: 'sccoa')
  Apartment::Tenant.switch!(@current_account.subdomain)

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  retired_employment_status = EmploymentStatus.where(name: 'Retired').first_or_create

  csv_file.each do |row|

    name = row['First Name'].split(' ')
    first_name = name&.first || ''
    last_name = row['Last Name'] || ''
    middle_name = name&.second || ''

    @row_number = [first_name, last_name].reject { |x| x.blank? }.join(', ')
    address = row['Address'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    birth_day = sccoa_parse_date(row['Date of Birth'], 'birth_date')
    personal_email = row['E-Mail'] || ''
    home_phone = sccoa_parse_phone(row['Telephone'])
    start_date = sccoa_parse_date(row['Date Retired'], 'start_date')

    employees = Employee.where("lower(first_name) like ? and lower(last_name) like ?", "#{first_name.downcase}%", "%#{last_name.downcase}")

    existing_member = false

    if employees.count == 1
      employee = employees.first
      existing_member = true
    elsif employees.count > 1
      sccoa_feed_errors("#{row['Name']} - Multiple Members found")
      next
    else
      employee = Employee.new
    end

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = middle_name
    employee.street = address
    employee.city = city
    employee.state = state
    employee.birthday = birth_day

    if employee.save

      unless existing_member
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).save!

        home_phone_contact = employee.contacts.where(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).first_or_initialize
        home_phone_contact.value = home_phone if home_phone
        home_phone_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!

        personal_email_contact = employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).first_or_initialize
        personal_email_contact.value = personal_email if personal_email.present?
        personal_email_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!
      end

      create_new_status = true
      past_statuses = employee.employee_employment_statuses.where(discarded_at: nil, end_date: nil)
      if past_statuses.present?
        if past_statuses.map(&:status_name).uniq.include? retired_employment_status.name.downcase
          create_new_status = false
        else
          create_new_status = true
          past_statuses.update_all(end_date: start_date)
        end
      end
      employee.employee_employment_statuses.new(employment_status_id: retired_employment_status.id, start_date: start_date).save(validate: false) if create_new_status && retired_employment_status.present?
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    sccoa_feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{Apartment::Tenant.current}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def sccoa_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  sccoa_feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def sccoa_parse_phone(phone)
  return nil unless phone.present?

  phone.remove!(/\D/).first(10)
  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)
  phone_number
rescue => e
  sccoa_feed_errors('PHONE ' + e.message)
end

def sccoa_check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create
  gender
rescue => e
  sccoa_feed_errors('GENDER ' + gender.errors.full_messages)
end

def sccoa_check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create
  rank
rescue => e
  sccoa_feed_errors('RANK ' + rank.errors.full_messages)
end

def sccoa_check_office(office_name, type)
  return nil unless office_name.present?

  office = Office.where(name: office_name).first_or_create
  office
rescue => e
  sccoa_feed_errors("OFFICE - #{type} - " + office.errors.full_messages)
end

def sccoa_check_officer_status(office_status_name, type)
  return nil unless office_status_name.present?

  officer_status = OfficerStatus.where(name: office_status_name).first_or_create
  officer_status
rescue => e
  sccoa_feed_errors("OFFICER STATUS - #{type} - " + officer_status.errors.full_messages)
end

def sccoa_check_facility(facility_name, type)
  return nil unless facility_name.present?

  facility = Facility.where(name: facility_name).first_or_create
  facility
rescue => e
  sccoa_feed_errors("OFFICER STATUS - #{type} - " + facility.errors.full_messages)
end

def parse_facility_crew_unit(names, type)
  return [nil, nil, nil] unless names.present?
  facility = crew = unit = nil
  if names.downcase.include?('yaphank')
    facility = sccoa_check_facility('Yaphank', type)
  elsif names.downcase.include?('riverhead')
    facility = sccoa_check_facility('Riverhead', type)
  else
    sccoa_feed_errors("#{type} Facility Not Found")
  end

  if names.downcase.include?('a crew')
    crew = sccoa_check_officer_status('Crew A', type)
  elsif names.downcase.include?('b crew')
    crew = sccoa_check_officer_status('Crew B', type)
  elsif names.downcase.include?('c crew')
    crew = sccoa_check_officer_status('Crew C', type)
  elsif names.downcase.include?('d crew')
    crew = sccoa_check_officer_status('Crew D', type)
  elsif names.downcase.include?('e crew')
    crew = sccoa_check_officer_status('Crew E', type)
  elsif names.downcase.include?('f crew')
    crew = sccoa_check_officer_status('Crew F', type)
  else
    sccoa_feed_errors("#{type} Crew Not Found")
  end

  unit_name_array = names.split(' ') - %w[Yaphank YAPHANK Riverhead RIVERHEAD CREW Crew crew A B C D E F]
  unit_name = unit_name_array.join(' ').titleize

  unit = sccoa_check_office(unit_name, type)

  [facility, crew, unit]
end

def sccoa_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
