# frozen_string_literal: true

task :split_name_and_add_start_date, [:account] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}

  BenefitCoverage.kept.find_each do |benefit_coverage|
    @row_number = benefit_coverage.name
    first_name, last_name = split_name(benefit_coverage.name.split)
    compare_date = Date.new(2024, 4, 01)
    insert_date = if benefit_coverage.birthday.nil?
                    compare_date
                  elsif benefit_coverage.birthday <= compare_date
                    compare_date
                  else
                    benefit_coverage.birthday
                  end
    benefit_coverage.update_columns(first_name: first_name, last_name: last_name, effective_date: insert_date)
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_report_errors(t.name + '_')
end

def split_name(name)
  if name.length == 3 && (name[1].length == 1 || name[1].length == 2)
    first_name = name[0..1].join(' ')
    last_name = name[2..-1]&.join(' ') || ''
  elsif name.length >= 4
    first_name = name[0..1].join(' ')
    last_name = name[2..-1]&.join(' ') || ''
  else
    first_name = name[0]
    last_name = name[1..-1]&.join(' ') || ''
  end
  [first_name, last_name]
end

task :update_start_date_under_anthem_dental, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  BenefitCoverage.joins(employee_benefit: :benefit).where('LOWER(benefits.name) = ?', 'anthem dental').find_each do |dependent|
    if dependent.birthday.nil? || dependent.birthday < Date.new(2025, 3, 01)
      dependent.update_columns(effective_date: Date.new(2025, 3, 01))
    else
      dependent.update_columns(effective_date: dependent.birthday)
    end
  end
end

task :restore_placard_nysscoa, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  csv_file.each do |row|
    @row_number = row['Member Name'] if row['Member Name'].present?
    birthday = row['DOB'].present? ? Date.strptime(row['DOB'], '%m-%d-%Y') : ''
    next if @row_number.blank? && birthday.blank?

    name_split = @row_number&.split
    f_name = name_split&.first&.downcase
    l_name = name_split&.last&.downcase
    m_name = name_split[1]&.downcase&.gsub('.', '') if name_split&.size == 3

    if birthday.present?
      employees = Employee.kept.where(birthday: birthday)
    elsif m_name.present?
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ? AND lower(middle_name) LIKE ?', "%#{f_name}%", "%#{l_name}%", "%#{m_name}%")
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name} #{m_name}%", "%#{l_name}%") if employees.empty?
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name}%", "%#{m_name} #{l_name}%") if employees.empty?
    else
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name}%", "%#{l_name}%")
    end

    if employees.count > 1
      nysscoa_feed_errors('More than One Employee found')
      next
    elsif employees.blank?
      nysscoa_feed_errors('Invalid Employee or Employee was missing')
      next
    end
    employee = employees.first
    changes = employee&.versions&.last&.changeset

    if (placard_number = changes['placard_number']&.first&.strip).blank?
      nysscoa_feed_errors("Previous version doesn't include a placard number")
    else
      employee.update!(placard_number: placard_number)
    end

  rescue StandardError => e
    p @row_number, e.message
    nysscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/restore_placard_nysscoa_#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Employee Name', 'Error']

    @errors.each do |error|
      csv << error
    end
  end
end

task :placard_import_2026, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  csv_file.each do |row|
    @row_number = row['Member Name'] if row['Member Name'].present?
    birthday =  if row['DOB'].present?
                  Date.strptime(row['DOB'], row['DOB'].include?('-') ? '%m-%d-%Y' : '%m/%d/%Y')
                else
                  ''
                end
    next if @row_number.blank? && birthday.blank?

    placard_number = row['Placard Number'] || ''
    name_split = @row_number&.split
    f_name = name_split&.first&.downcase
    l_name = name_split&.last&.downcase
    m_name = name_split[1]&.downcase&.gsub('.', '') if name_split&.size == 3

    if m_name.present?
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ? AND lower(middle_name) LIKE ?', "%#{f_name}%", "%#{l_name}%", "%#{m_name}%")
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name} #{m_name}%", "%#{l_name}%") if employees.empty?
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name}%", "%#{m_name} #{l_name}%") if employees.empty?
    else
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name}%", "%#{l_name}%")
    end

    employees = employees.where(birthday: birthday) if birthday.present? && employees.count > 1

    if employees.count > 1
      nysscoa_feed_errors('More than One Employee found')
      next
    elsif employees.blank?
      nysscoa_feed_errors('Invalid Employee or Employee was missing')
      next
    end

    employee = employees.first
    old_placard_number = employee&.placard_number
    if old_placard_number.blank?
      employee.update!(placard_number: ",#{placard_number}")
    else
      placard1 = old_placard_number.split(',').first
      employee.update!(placard_number: "#{placard1},#{placard_number}")
    end

  rescue StandardError => e
    p @row_number, e.message
    nysscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/placard_import_2026_#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Employee Name', 'Error']

    @errors.each do |error|
      csv << error
    end
  end
end

# bundle exec rake 'split_name_and_add_start_date[nysscoa]'
# bundle exec rake 'update_start_date_under_anthem_dental[nysscoa]'
# bundle exec rake 'restore_placard_nysscoa[nysscoa, Bronx_Commands_Restoration_List.csv]'
# bundle exec rake 'restore_placard_nysscoa[nysscoa, Brooklyn_Restoration_List.csv]'
# bundle exec rake 'restore_placard_nysscoa[nysscoa, Queens_Commands_Restoration_List.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, 9th_JD_2526_Parking_Placards.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, Bronx_Commands_2526_Parking_Placards.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, Brooklyn_Commands_2526_Parking_Placard_List.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, DPS_OCA_ACADEMY_2526_Parking_Placards.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, Manhattan_2526_Parking_Placards.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, Queens_commands_2526_Parking_Placards.csv]'
# bundle exec rake 'placard_import_2026[nysscoa, Richmond_and_Staten_Island_2526_Parking_Placards.csv]'
