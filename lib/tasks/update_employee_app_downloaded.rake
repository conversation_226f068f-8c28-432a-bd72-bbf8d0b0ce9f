# frozen_string_literal: true

desc 'Update App Downloaded for Employees'
task :update_app_downloaded_for_employees, [:account] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}

  Employee.kept.where('current_sign_in_at is NOT NULL').each do |employee|
    @employee_name = employee.full_name

    next if employee.app_downloaded == true

    begin
      ActiveRecord::Base.transaction do
        employee.update_columns(app_downloaded: true)
      end
    rescue StandardError => e
      member_feed_errors(e.message)
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_app_downloaded_for_employees.csv", 'w') do |csv|
    p csv << ['Row Number', 'Errors']

    @errors.each do |error|
      p csv << error
    end
  end
end

def member_feed_errors(message)
  if @errors[@employee_name].present?
    @errors[@employee_name] << message
  else
    @errors[@employee_name] = [message]
  end
end

# bundle exec rake 'update_app_downloaded_for_employees[papba]'