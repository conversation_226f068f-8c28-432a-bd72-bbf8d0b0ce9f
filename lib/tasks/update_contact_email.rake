# frozen_string_literal: true

require 'csv'

desc 'import data'
task :update_contact_email, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @first_name = row['First Name']
    @last_name = row['Last Name']
    @email = row['Email address']

    employees = Employee.where("first_name ilike ? and last_name ilike ?", (@first_name + '%'), (@last_name + '%'))

    if employees.present?
      if employees.count > 1
        feed_errors('Multiple occurrences for name')
      else
        employee = employees.first
        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: @email).save! if @email
      end
    else
      feed_errors("Employee doesn't exist")
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_email_import_errors.csv", 'w') do |csv|
    csv << ["Email", "First Name", "Last Name", "Error"]

    @errors.each do |error|
      csv << error
    end
  end

end

def feed_errors(message)
  @errors[@email] = [@first_name, @last_name, message]
end
