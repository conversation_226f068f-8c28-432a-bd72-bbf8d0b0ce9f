# frozen_string_literal: true

require_relative 'boot'

require 'rails'
# Pick the frameworks you want:
require 'active_model/railtie'
require 'active_job/railtie'
require 'active_record/railtie'
require 'active_storage/engine'
require 'action_controller/railtie'
require 'action_mailer/railtie'
require 'action_mailbox/engine'
require 'action_text/engine'
require 'action_view/railtie'
require 'action_cable/engine'
require 'sprockets/railtie'
# require 'rails/test_unit/railtie'
require_relative '../app/jobs/multi_tenant_support'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module FuseRails
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 6.0

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.middleware.use ActionDispatch::Flash
    config.api_only = true

    # https://github.com/cyu/rack-cors#configuration
    development_hosts = [/localhost:/, /ngrok.io$/, /lvh.me:/, /xip.io/]
    prod_hosts = [/.#{ENV['REACT_HOST']}/]
    config.middleware.insert_before 0, Rack::Cors do
      allow do
        origins Rails.env.development? ? development_hosts : prod_hosts
        resource '*', headers: :any, methods: %i[get post put patch delete options], credentials: true
      end
    end

    # To allow cross origin to access the socket connection.
    config.action_cable.allowed_request_origins = Rails.env.development? ? development_hosts : prod_hosts

    config.active_job.queue_adapter = :sidekiq

    config.eager_load_paths += %W[
      #{config.root}/lib
      #{config.root}/app/decorators
      #{config.root}/app/queries
      #{config.root}/app/searchables
      #{config.root}/app/serializers
      #{config.root}/app/services
      #{config.root}/app/validators
    ]

    # To include a module which overrides active jobs methods.
    ActiveJob::Base.include(MultiTenantSupport)

    Rails.application.routes.default_url_options = { host: ENV['ACTIVE_DOMAIN_URL'], protocol: ENV['ACTIVE_DOMAIN_PROTOCOL'] }

    config.action_mailer.default_url_options = {
      protocol: ENV['ACTIVE_DOMAIN_PROTOCOL'],
      host: ENV['ACTIVE_DOMAIN_URL']
    }

    if Rails.env.development? || ENV['APP_ENV'] == 'development'
      config.after_initialize do
        Bullet.enable = true
        Bullet.bullet_logger = true if Rails.env.development?
        Bullet.raise = false
        Bullet.rails_logger = true
        Bullet.unused_eager_loading_enable = true
        # if ENV['APP_ENV'] == 'development'
        #   Bullet.slack = { webhook_url: '*******************************************************************************' }
        # end
      end
    end

    config.autoload_paths << "#{Rails.root}/lib"
    config.after_initialize do
      require 'custom_token_response'
    end
  end
end
