# frozen_string_literal: true

# Override the check_api method in the PgHero::HomeController
module PgheroOverrides
  extend ActiveSupport::Concern

  included do
    # Override the check_api method to customize behavior for API-only applications
    def check_api
      true # Allow the action to proceed
    end
  end
end

Rails.application.config.to_prepare do
  # Prepend the override to the original controller
  PgHero::HomeController.include(PgheroOverrides)
end