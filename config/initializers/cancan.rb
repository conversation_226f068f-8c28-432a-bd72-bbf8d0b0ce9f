# frozen_string_literal: true

require 'cancan'

if defined?(CanCan)
  class Object
    def metaclass
      class << self
        self
      end
    end
  end

  module CanCan
    module ModelAdapters
      class ActiveRecordAdapter < AbstractAdapter
        @@friendly_support = {}

        def self.find(model_class, id)
          klass = if model_class.metaclass.ancestors.include?(ActiveRecord::Associations::CollectionProxy)
                    model_class.klass
                  else
                    model_class
                  end
          @@friendly_support[klass] ||= klass.metaclass.ancestors.include?(FriendlyId)
          @@friendly_support[klass] == true ? model_class.unscoped.friendly.find(id) : model_class.unscoped.find(id)
        end
      end
    end
  end
end
