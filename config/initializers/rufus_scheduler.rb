# frozen_string_literal: true

require 'rufus-scheduler'
require 'rake'
if $scheduler_thread
  scheduler = Rufus::Scheduler.new

  scheduler.cron '30 5 1 4 *' do
    Apartment::Tenant.switch!('nyscoa')

    employees = Employee.kept

    if employees.present?
      employees.each do |employee|

        if employee.placard_number.present?
          placard_number = ['', employee.placard_number.split(',')].flatten.first(3).join(',')
          employee.update_columns(placard_number: placard_number)
        end

      end
    end
  end

  scheduler.cron '5 0 * * *' do
    Account.where(subdomain: %w[btoba nysscoa nyccoba iuoe211 cobanc sccoa local342]).each do |current_account|
      Apartment::Tenant.switch!(current_account.subdomain)

      other_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'coverage_expire_age') if current_account.present?
      other_coverage_expire_age ||= 26

      optical_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'optical_coverage_expire_age') if current_account.present?
      optical_coverage_expire_age ||= 19

      dental_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'dental_coverage_expire_age') if current_account.present?
      dental_coverage_expire_age ||= 23
      is_optical_dental_benefit = false

      expire_relationship_types = current_account.saas_json.dig('schema', 'benefit_coverages', 'expire_relationship_types') if current_account.present?
      expire_relationship_types ||= false

      retire_benefit_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'retire_benefit_coverage_expire_age') if current_account.present?

      BenefitCoverage.kept.includes(employee_benefit: :benefit,
                                    employee: :employee_employment_statuses).where(unexpire: false, expires_at: nil).each do |benefit_coverage|
        now = Time.now
        age = now.year - benefit_coverage.birthday.to_time.year - (benefit_coverage.birthday.to_time.change(:year => now.year) > now ? 1 : 0) if benefit_coverage.birthday

        if %w[btoba nysscoa nyccoba].include?(current_account.subdomain) && %w[optical vision].include?(benefit_coverage.employee_benefit&.benefit.name&.downcase)
          coverage_expire_age = optical_coverage_expire_age
          is_optical_dental_benefit = true
        elsif %w[nysscoa].include?(current_account.subdomain) && %w[dentalppo empiredental unitedhealthcaredental cookdental].include?(benefit_coverage.employee_benefit.benefit&.name&.downcase&.delete(' '))
          coverage_expire_age = dental_coverage_expire_age
          is_optical_dental_benefit = true
        else
          coverage_expire_age = other_coverage_expire_age
          is_optical_dental_benefit = false
        end

        if age.present? && age >= coverage_expire_age && benefit_coverage.relationship.present? && ((expire_relationship_types.present? && expire_relationship_types.include?(benefit_coverage.relationship.downcase)) ||
          (benefit_coverage.relationship.downcase == "child" || (is_optical_dental_benefit && benefit_coverage.relationship.downcase == 'step_child')))
          benefit_coverage.update(expires_at: Date.today)
        end
      rescue StandardError => e
        Rails.logger.info('Error in the rufus scheduler')
        CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      end

      if retire_benefit_coverage_expire_age.present?
        Employee.kept.includes(:employee_employment_statuses, :benefit_coverages).where('employee_employment_statuses.employment_status_id in  (?) and (benefit_coverages.expires_at is null and benefit_coverages.unexpire = ?)',
                                                                                        EmploymentStatus.where('lower(name) in (?)', ['retired', 'disability retired']).ids, false).references(:employee_employment_statuses, :benefit_coverages).each do |employee|
          benefit_coverages = employee.benefit_coverages
          benefit_coverages.each do |benefit_coverage|
            now = Time.now
            age = now.year - benefit_coverage.birthday.to_time.year - (benefit_coverage.birthday.to_time.change(:year => now.year) > now ? 1 : 0) if benefit_coverage.birthday
            if (age.present? && retire_benefit_coverage_expire_age.present? && age > retire_benefit_coverage_expire_age &&
              %w[child step_child].include?(benefit_coverage.relationship&.downcase))
              benefit_coverage.update(expires_at: Date.today)
            end
          end
        rescue StandardError => e
          Rails.logger.info('Error in the rufus scheduler')
          CustomAppsignalError.send_error(e, Apartment::Tenant.current)
        end
      end
    end
  end

  scheduler.cron '0 5 * * *' do
    Apartment::Tenant.switch!('iuoe211')
    auto_expire_week = Account.find_by(subdomain: 'iuoe211').saas_json.dig('schema', 'disabilities', 'auto_expire_week')
    auto_expire_week ||= 26
    Disability.kept.where(to_date: nil).each do |disability|
      from_date = disability.from_date
      current_date = Date.today
      no_of_days = (current_date - from_date).to_i
      disability.update(duration: no_of_days)
    end
  end

  if ENV['CAN_SEND_FTP_FILES'] == 'true'
    scheduler.cron '5 11 * * *' do
      # HealthplexFileUploadJob.perform_later
      # ProActFileUploadJob.perform_later
      GvsFileUploadJob.perform_later('btoba', 'Optical') # BTOBA GVS
      sleep(60)
      GvsFileUploadJob.perform_later('nysscoa', 'Vision') # NYSSCOA GVS
      sleep(60)
      GvsFileUploadJob.perform_later('cobanc', 'Optical') # COBANC GVS
      ArayaRxFileUploadJob.perform_later
      DavisVisionFileUploadJob.perform_later('btoba', 'Optical') # BTOBA Davis Vision
      CpsFileUploadJob.perform_later('btoba', 'Optical') # BTOBA CPS Eligibility
      # VisionScreeningFileUploadJob.perform_later('btoba','Optical') #BTOBA VisionScreening
      # VisionScreeningFileUploadJob.perform_later('cobanc','Optical') #COBANC VisionScreening
      CvsFileUploadJob.perform_later('iuoe211', 'Prescription Drug Benefit') # IUOE211 CVS Eligibility
    end
  end

  scheduler.cron '0 3 * * *' do
    OpticalBenefitCoverageUnExpirationJob.perform_later("btoba")
  end

  scheduler.cron '0 0 1 1 *' do
    OpticalBenefitCoverageUnExpirationJob.perform_later("cobanc")
  end

  scheduler.cron '5 9 * * *' do
    Account.where(subdomain: %w[iuoe211]).each do |current_account|
      Apartment::Tenant.switch!(current_account.subdomain)
      expiry_relationships = current_account.saas_json.dig('schema', 'employee_benefits', 'expire_relationship_types')
      status_ids = EmploymentStatus.kept.where("lower(REPLACE(name, ' ', '')) IN (?)", EmployeeEmploymentStatus::CVS_ELIGIBLE_STATUSES).pluck(:id)

      Employee.kept.includes(:employee_employment_statuses).where('employee_employment_statuses.start_date = ? AND employee_employment_statuses.employment_status_id IN (?)', Date.today, status_ids)
              .references(:employee_employment_statuses).each do |employee|
        cvs_active_statuses = %w[agency cobra union]
        expiry_status_names = current_account.saas_json.dig('schema', 'employee_benefits', 'employee_status', 'section_III')
        status = employee.employee_employment_statuses.where(start_date: Date.today).first
        last_two_statuses = employee.employee_employment_statuses.where('id != ?', status.id).order(updated_at: :desc).limit(2)&.map(&:name)
        eligible_to_eligible = (cvs_active_statuses.include?(last_two_statuses&.first&.downcase) || (expiry_status_names&.exclude?(last_two_statuses&.first&.downcase) && cvs_active_statuses.include?(last_two_statuses&.last&.downcase)))
        employee_coverages = expiry_relationships ? employee.benefit_coverages.where(relationship: expiry_relationships) : employee.benefit_coverages

        if eligible_to_eligible && cvs_active_statuses.include?(status.employment_status.name.downcase)
          employee.employee_benefits.update_all(end_date: status.end_date)
          employee_coverages.update_all(expires_at: status.end_date)
        else
          employee.employee_benefits.update_all(start_date: status.start_date, end_date: status.end_date)
          employee_coverages.update_all(expires_at: status.end_date)
        end
      rescue StandardError => e
        Rails.logger.info('Error in the rufus scheduler')
        CustomAppsignalError.send_error(e, Apartment::Tenant.current)
      end
    end
  end

  scheduler.cron '0 16 * * *' do
    current_run_time = Time.current.change(hour: 16, min: 0, sec: 0)
    cron_time = current_run_time + 12.hours + 5.minutes
    previous_run_time = current_run_time - 24.hours

    Account.find_each do |current_account|
      Apartment::Tenant.switch!(current_account&.subdomain)
      next unless current_account&.saas_json&.dig('schema', 'reminders', 'enable_cron_logics') == true

      scheduled_set = Sidekiq::ScheduledSet.new
      failed_reminders = []
      Reminder.kept.where(status: [ 'pending', 'scheduled' ]).each do |reminder|
        schedule = reminder.schedule
        next_occurrence = schedule&.next_occurrence(current_run_time)

        next unless next_occurrence && next_occurrence >= current_run_time && next_occurrence <= cron_time

        job_exists = reminder.status == 'scheduled' && scheduled_set.any? { |job| job.jid == reminder.job_id }
        next if job_exists || reminder.status == 'completed' || (reminder.reminder_end_date && reminder.reminder_end_date < current_run_time)

        scheduled_job = ReminderJob.set(wait_until: next_occurrence).perform_later(reminder.id, current_account.subdomain)
        reminder.update_columns(job_id: scheduled_job.provider_job_id, status: 'scheduled') if scheduled_job
      end

      # Check for failed reminders from the previous run
      Reminder.kept.where(status: ['scheduled']).each do |reminder|
        schedule = reminder.schedule
        missed_times = schedule.occurs_between?(previous_run_time, current_run_time)

        if missed_times && reminder.status == 'scheduled'
          failed_reminders << {
            id: reminder.id,
            subdomain: current_account.subdomain,
            date: schedule.previous_occurrence(current_run_time),
          }
        end
      end
      if failed_reminders.any?
        Rails.logger.info("Failed Reminders: #{failed_reminders}")
        Appsignal.send_error(StandardError.new("Failed Reminders: #{failed_reminders.to_json}"))
      end
    rescue StandardError => e
      Rails.logger.info("Error occurred while scheduling reminders: #{e.message}")
      Appsignal.send_error(e)
    end
  end

  scheduler.cron '0 4 * * *' do
    Account.find_each do |current_account|
      Apartment::Tenant.switch!(current_account&.subdomain)
      next unless current_account&.saas_json&.dig('schema', 'reminders', 'enable_cron_logics') == true
      current_cron_time = Time.current.in_time_zone('Eastern Time (US & Canada)').change(hour: 4, min: 0, sec: 0)
      cron_interval = 24.hours
      next_scheduled_run = current_cron_time + cron_interval
      scheduled_set = Sidekiq::ScheduledSet.new

      begin
        reminders = Reminder.kept.where(status: ['pending','scheduled'])
        reminders.each do |reminder|
          schedule = reminder.schedule
          next_occurrence = schedule&.next_occurrence(current_cron_time)

          next unless next_occurrence && next_occurrence >= current_cron_time && next_occurrence <= next_scheduled_run

          job_exists = reminder.status == 'scheduled' && scheduled_set.any? { |job| job.jid == reminder.job_id }
          next if reminder.status == 'completed' || job_exists || (reminder.reminder_end_date && reminder.reminder_end_date < current_cron_time)

          scheduled_job = ReminderJob.set(wait_until: next_occurrence).perform_later(reminder.id, current_account.subdomain)
          reminder.update_columns(job_id: scheduled_job.provider_job_id, status: 'scheduled') if scheduled_job
        end
      rescue StandardError => e
        Rails.logger.info("Error occurred while scheduling notifications: #{e.message}")
        Appsignal.send_error(e)
      end
    end
  end

  scheduler.cron '0 4 * * *' do
    Notification::DOMAINS_WITH_SCHEDULED_NOTIFICATIONS.each do |subdomain|
      Apartment::Tenant.switch!(subdomain)
      current_cron_time = Time.current.change(hour: 4, min: 0, sec: 0)
      cron_interval = 24.hours
      next_scheduled_run = current_cron_time + cron_interval

      Notification.kept.where(is_scheduled: true, status: 'pending').where("(scheduled_date + scheduled_time)::timestamp AT TIME ZONE 'America/New_York' AT TIME ZONE 'UTC' BETWEEN ? AND ?", current_cron_time, next_scheduled_run).each do |notification|
        scheduled_at = Notification.utc_time(notification.scheduled_date, notification.scheduled_time)
        scheduled_job = NotificationJob.set(wait_until: scheduled_at).perform_later(notification.id, subdomain)
        notification.update_columns(job_id: scheduled_job.provider_job_id, status: 'scheduled') if scheduled_job
      end
    rescue StandardError => e
      Rails.logger.info("Error occurred while scheduling notifications: #{e.message}")
      Appsignal.send_error(e)
    end
  end

  scheduler.cron '0 16 * * *' do
    Notification::DOMAINS_WITH_SCHEDULED_NOTIFICATIONS.each do |subdomain|
      Apartment::Tenant.switch!(subdomain)
      current_run_time = Time.current.change(hour: 16, min: 0, sec: 0)
      cron_time = current_run_time + 12.hours + 5.minutes
      previous_run_time = current_run_time - 24.hours
      scheduled_set = Sidekiq::ScheduledSet.new
      failed_notifications = []

      Notification.kept.where("is_scheduled = true AND status != 2").where("(scheduled_date + scheduled_time)::timestamp AT TIME ZONE 'America/New_York' AT TIME ZONE 'UTC' BETWEEN ? AND ?", current_run_time, cron_time).each do |notification|
        job_exists = notification.status == 'scheduled' && scheduled_set.any? { |job| job.jid == notification.job_id }
        next if job_exists

        scheduled_at = Notification.utc_time(notification.scheduled_date, notification.scheduled_time)
        scheduled_job = NotificationJob.set(wait_until: scheduled_at).perform_later(notification.id, subdomain)
        notification.update_columns(job_id: scheduled_job.provider_job_id, status: 'scheduled') if scheduled_job
      end
      Notification.kept.where("is_scheduled = true AND status != 2").where("(scheduled_date + scheduled_time)::timestamp AT TIME ZONE 'America/New_York' AT TIME ZONE 'UTC' BETWEEN ? AND ?", previous_run_time, current_run_time).each do |notification|
        failed_notifications << { id: notification.id, subdomain: subdomain, date: notification.scheduled_date, time: notification.scheduled_time&.strftime('%H:%M:%S') }
      end
      if failed_notifications.any?
        Rails.logger.info("Failed Notifications: #{failed_notifications}")
        Appsignal.send_error(StandardError.new("Failed Notifications: #{failed_notifications.to_json}"))
      end
    rescue StandardError => e
      Rails.logger.info("Error occurred while scheduling notifications: #{e.message}")
      Appsignal.send_error(e)
    end
  end
end
