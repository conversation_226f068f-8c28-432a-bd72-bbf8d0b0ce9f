{"ignored_warnings": [{"warning_type": "Mass Assignment", "warning_code": 105, "fingerprint": "827e7eafc6c3ef5711a35f50258167fe41d1470e9a76949d5c189cd4160fc94c", "check_name": "PermitAttributes", "message": "Potentially dangerous key allowed for mass assignment", "file": "app/controllers/admin/users_controller.rb", "line": 83, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.require(:user).permit(:username, :email, :first_name, :last_name, :notification, :role)", "render_path": null, "location": {"type": "method", "class": "Admin::UsersController", "method": "permitted_edit_params"}, "user_input": ":role", "confidence": "Medium", "note": ""}, {"warning_type": "Remote Code Execution", "warning_code": 24, "fingerprint": "87ab1a80c95fddc5e829beb19b7fc5d5781d253dcc057956a9cad3370b578051", "check_name": "UnsafeReflection", "message": "Unsafe reflection method `constantize` called with parameter value", "file": "app/jobs/report_generator_job.rb", "line": 25, "link": "https://brakemanscanner.org/docs/warning_types/remote_code_execution/", "code": "\"ReportGenerator::#{params[:report_type].classify}Report\".constantize", "render_path": null, "location": {"type": "method", "class": "ReportGeneratorJob", "method": "report_generator"}, "user_input": "params[:report_type].classify", "confidence": "High", "note": ""}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "a9638e1e707957cc634f643c31ff9b9142a881f8d656c8d9e386346796ace0b5", "check_name": "MassAssignment", "message": "Parameters should be whitelisted for mass assignment", "file": "app/controllers/admin/reports_controller.rb", "line": 22, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.require(:report).permit!", "render_path": null, "location": {"type": "method", "class": "Admin::ReportsController", "method": "permitted_params"}, "user_input": null, "confidence": "Medium", "note": ""}, {"warning_type": "Mass Assignment", "warning_code": 105, "fingerprint": "e9e4f1626c006914b803c2749069b8c708d2863c2d41afc4102befc366e11dae", "check_name": "PermitAttributes", "message": "Potentially dangerous key allowed for mass assignment", "file": "app/controllers/admin/users_controller.rb", "line": 79, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.require(:user).permit(:username, :email, :first_name, :last_name, :notification, :password, :role)", "render_path": null, "location": {"type": "method", "class": "Admin::UsersController", "method": "permitted_params"}, "user_input": ":role", "confidence": "Medium", "note": ""}], "updated": "2019-09-30 13:56:38 +0000", "brakeman_version": "4.5.0"}