# This is the configuration used to check the rubocop source code.
# Check out: https://github.com/bbatsov/rubocop

AllCops:
  NewCops: enable
  EnabledByDefault: false
  DisplayCopNames: true
  TargetRubyVersion: 2.6
  Exclude:
    - 'vendor/**/*'
    - 'bin/**/*'
    - 'Procfile.*'
    - 'db/**/*'

Lint/SuppressedException:
  Exclude:
    - 'config/unicorn.rb'

Lint/ParenthesesAsGroupedExpression:
  Exclude:
    - 'config/unicorn.rb'

Metrics/AbcSize:
  Exclude:
    - 'app/controllers/admin/reports_controller.rb'
    - 'app/models/beneficiary.rb'
    - 'app/models/user.rb'
    - 'app/services/generators/employee_data_generator.rb'
    - 'app/queries/employees_with_employment_statuses_query.rb'
    - 'app/queries/employees_with_firearm_statuses_query.rb'
    - 'app/queries/employees_with_officer_statuses_query.rb'
    - 'app/queries/employees_with_offices_query.rb'
    - 'app/queries/employees_with_positions_query.rb'
    - 'app/queries/employees_with_ranks_query.rb'
    - 'app/queries/employees_with_delegate_assignments_query.rb'
    - 'app/services/generators/beneficiary_generator.rb'
    - 'app/services/generators/benefit_coverage_generator.rb'
    - 'app/services/generators/benefit_disbursement_generator.rb'
    - 'app/services/generators/employee_benefit_generator.rb'
    - 'app/services/generators/employee_firearm_status_generator.rb'
    - 'app/services/generators/employee_generator.rb'
    - 'app/services/generators/employee_office_generator.rb'
    - 'app/services/generators/employee_officer_status_generator.rb'
    - 'app/services/generators/employee_position_generator.rb'
    - 'app/services/generators/employee_rank_generator.rb'
    - 'app/services/generators/lodi_generator.rb'
    - 'app/services/generators/sick_bank_generator.rb'
    - 'app/services/importers/employees_importer.rb'
    - 'test/channels/application_cable/connection_test.rb'
    - 'test/test_helper.rb'

Metrics/BlockLength:
  Exclude:
    - 'app/services/generators/employee_data_generator.rb'
    - 'config/environments/development.rb'
    - 'config/environments/production.rb'
    - 'config/environments/staging.rb'
    - 'config/routes.rb'
    - 'Gemfile'
    - 'spec/**/*'

Metrics/ClassLength:
  Max: 250
  Exclude:
    - 'app/controllers/admin/employees_controller.rb'
    - 'app/controllers/admin/reports_controller.rb'
    - 'app/controllers/admin/users_controller.rb'
    - 'app/models/beneficiary.rb'
    - 'app/models/employee.rb'
    - 'app/models/user.rb'
    - 'app/queries/employees_with_delegate_assignments_query.rb'
    - 'app/searchables/employee_searchable.rb'
    - 'app/services/generators/employees_query_generator.rb'
    - 'test/channels/application_cable/connection_test.rb'
    - 'test/test_helper.rb'

Metrics/CyclomaticComplexity:
  Enabled: false

Layout/LineLength:
  Max: 220
  Exclude:
    - 'app/controllers/admin/beneficiaries_controller.rb'
    - 'app/controllers/admin/benefit_coverages_controller.rb'
    - 'app/controllers/admin/benefit_disbursements_controller.rb'
    - 'app/controllers/admin/delegate_assignments_controller.rb'
    - 'app/controllers/admin/employees_controller.rb'
    - 'app/controllers/admin/reports_controller.rb'
    - 'app/controllers/admin/users_controller.rb'
    - 'app/models/account.rb'
    - 'app/models/beneficiary.rb'
    - 'app/models/employee.rb'
    - 'app/models/user.rb'
    - 'app/queries/employees_with_delegate_assignments_query.rb'
    - 'app/searchables/employee_searchable.rb'
    - 'app/services/generators/employees_query_generator.rb'
    - 'config/initializers/devise.rb'
    - 'spec/support/driver_registration.rb'
    - 'test/channels/application_cable/connection_test.rb'
    - 'test/test_helper.rb'

Metrics/MethodLength:
  Max: 30
  Exclude:
    - 'app/searchables/employee_searchable.rb'
    - 'app/services/generators/employee_data_generator.rb'
    - 'app/services/generators/benefit_coverage_generator.rb'
    - 'app/services/generators/benefit_disbursement_generator.rb'
    - 'app/services/generators/employee_officer_status_generator.rb'
    - 'app/services/generators/employee_rank_generator.rb'
    - 'app/services/importers/**/*'

Metrics/PerceivedComplexity:
  Exclude:
    - 'app/services/generators/employee_data_generator.rb'

Metrics/ModuleLength:
  Max: 200
Style/ClassAndModuleChildren:
  Exclude:
    - 'app/controllers/admin/**/*'
    - 'app/serializers/admin/**/*'
    - 'test/channels/**/*'
    - 'test/test_helper.rb'

Style/Documentation:
  Enabled: false

Style/EvalWithLocation:
  Enabled: false

Naming/PredicateName:
  Enabled: false
