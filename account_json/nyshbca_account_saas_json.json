{"schema": {"employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "cellphone": "Cell", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street"], "associated_model": {"contacts": ["value"], "ranks": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "required_fields": []}, "email_address": {"key_name": "Email address", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "ranks": {"key_name": "Member Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "departments": {"key_name": "Department", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_attachments": "SMS attachments", "sms_no_reply_text": "Please note, this is a no-reply text."}, "roles": {"key_name": "Roles"}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "rank_name", "personal_phone", "employment_status_name"], "tabs": ["profile"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["name", "address", "precinct"], "contacts": [{"contact_number": ["personal_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_ranks", "employee_departments"]}}, "settings": {"key_name": "Settings", "tabs": ["ranks", "departments", "employment_statuses"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["ranks", "departments"], ["employment_statuses", ""]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}