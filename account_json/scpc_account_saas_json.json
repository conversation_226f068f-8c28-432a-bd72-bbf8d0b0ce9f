{"schema": {"notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_no_reply_text": "Please note, this is a no-reply text."}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip", "cellphone": "Cell Phone", "email": "Email", "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name"], "associated_model": {"contacts": ["value"], "employment_statuses": ["name"], "positions": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "required_fields": []}, "email_address": {"key_name": "Email address", "personal_email": "Personal", "required_fields": []}}, "employment_statuses": {"key_name": "Union Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "positions": {"key_name": "Union Affiliation", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "ranks": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": {"employees": {"placeholder": "Filter by Status"}, "ranks": {"placeholder": "Filter by Union Position"}, "positions": {"placeholder": "Filter by Union Affiliation"}}, "table_headers": ["name", "employment_status_name", "personal_email", "personal_phone", "position_name"], "tabs": ["profile"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["name", "address"], "contacts": [{"contact_number": ["personal_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_positions", "employee_ranks"]}}, "settings": {"key_name": "Settings", "tabs": ["ranks", "employment_statuses", "positions"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["employment_statuses", "positions"], ["employees.state", "employees.city"], ["employees.zipcode", "ranks"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}