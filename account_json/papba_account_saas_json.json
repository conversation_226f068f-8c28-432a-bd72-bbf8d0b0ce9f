{"schema": {"change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status", "is_handling_name_from_json": true}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "marital_statuses": "Marital Status", "birthday": "DOB", "age": "Age", "do_not_mail": "Do Not Mail", "social_security_number": "Social Security Number", "veteran_status": "Military Time", "shield_number": "Shield Number", "a_number": "PA Employment Number", "title_code": "Pistol Number", "previous_shield_number": "Class", "placard_number": "Placard Number", "start_date": "Date Employed", "member_start_date": "Date Graduated", "staff_member": "NJSPBA Enrollment", "notes": "Notes", "janus_card": "<PERSON><PERSON>", "janus_card_opt_out_date": "<PERSON><PERSON>t Out Date", "cellphone": "Cell phone", "home_phone": "Home phone", "email": "Email", "social_security_number_format": "9", "username": "Username", "app_downloaded": "App Downloaded", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "custom_validations": {"integer_fields": ["a_number", "shield_number"]}, "do_not_email_required_validation_fields": ["address", "city", "state", "street", "zipcode"], "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode", "birthday", "social_security_number"], "unique_fields": ["username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "suffix", "shield_number", "placard_number", "birthday", "shield_number", "a_number"], "associated_model": {"contacts": ["value"], "benefit_coverages": ["name"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"], "associated_model": {"benefit_coverages": ["name"], "beneficiaries": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell Phone", "home_phone": "Home Phone", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}}, "genders": {"key_name": "Gender", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "improper_practices": {"key_name": "Improper Practices", "improper_practice_type_id": "Type", "improper_practice_type_name": "Type", "date": "Date", "status": "Status", "office_id": "Command", "employee_ids": "Members", "docket_number": "Docket #", "hearing_officer_id": "Hearing Officer", "hearing_officer_appointed_date": "Hearing Officer Appointed Date", "pa_attorney": "PA Attorney", "files": "Uploads", "improper_practice_charge": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/papba/improper_practice_charge.doc", "ip_letters": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/papba/ip_letters.docx", "required_fields": ["date", "improper_practice_type_id"]}, "improper_practice_types": {"key_name": "Improper Practice Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "improper_practice_tabs": {"improper_practice_id": "Improper Practice", "date": "Date", "notes": "Notes", "tab_type": "Tab Type", "files": "Uploads", "transcripts": "Transcripts", "description": "Description", "evidences": "Exhibits", "brief_date": "Brief Date", "brief": "Brief", "briefs": "Briefs", "report_description": "Report Description", "report_date": "Report Date", "reports": "Reports & Recommendations", "tentative_decision_description": "Tentative Decision Description", "tentative_decision_date": "Tentative Decision Date", "tentative_decisions": "Tentative Decisions", "final_decision_description": "Final Decision Description", "final_decision_date": "Final Decision Date", "final_decisions": "Final Decisions", "required_fields": ["improper_practice_id"]}, "benefit_coverages": {"key_name": "Dependents", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "dependent": "Dependent", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "age": "Age", "expires_at": "Expiration", "files": "Uploads", "order_by_relationship": true, "required_fields": ["employee_id", "birthday", "name", "relationship"], "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Step Child", "key": "step_child"}, {"value": "Disabled <PERSON> Child", "key": "disabled_step_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}, {"value": "Daughter", "key": "daughter"}, {"value": "Son", "key": "son"}, {"value": "Step Son", "key": "stepson"}, {"value": "Step Daughter", "key": "s<PERSON><PERSON><PERSON>"}, {"value": "Dependent", "key": "dependent"}]}, "departments": {"key_name": "Mailing List Category", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"key_name": "Mailing List Category", "required_fields": ["department_id", "employee_id"]}, "offices": {"key_name": "Command", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "positions": {"key_name": "Union Board Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"], "customization": true, "required_tables": ["firearm_statuses"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "social_security_number": "employees.social_security_number", "beneficiary_type": "Type", "beneficiary_type_values": ["Primary", "Contingent"], "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id"]}, "meeting_types": {"key_name": "Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "roles": {"key_name": "Roles"}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "reports": {"single_employee": "Single Member", "benefit_coverage_name": "Dependent Name", "employee_name": "Member Name", "relationship": "Relationship", "birthday": "DOB", "union_meetings": "Meetings", "janus": "<PERSON><PERSON>", "grievances": "Grievances", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "app_downloaded": "App Downloaded", "inactive_date": "Inactive Date", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "grievance_command": "Grievance Command", "pending_with_future_hearing": "Pending & Future hearing", "pending_with_past_hearing": "Pending & Past hearing", "arbitrator_details": "Arbitrator", "attorney_details": "PA Attorney", "no_grievance_number": "No Grievance #", "delegates": "Delegates", "meetings": "Meetings", "pacfs": "Annual Dues", "amount_from": "Amount From", "amount_to": "Amount To", "show_dollar_amount": "Show Dollar Amount", "exact_search_results": true, "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "exact_search_fields": ["shield_number", "a_number"]}, "pacfs": {"key_name": "Annual Dues", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "auto_dues_status": true, "required_fields": ["pacf_id", "employee_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Class Action Grievance", "number": "Grievance #", "status": "Status", "grievance_id": "Type", "date": "Date", "office_id": "Command", "employee_ids": "Member(s)", "description": "Charge", "provisions": "Provisions", "arbitrator_id": "Arbitrator", "pa_attorney_id": "PA Attorney", "files": "Uploads", "grievances_specific_logics_for_papba": true, "required_fields": ["grievance_id"], "unique_fields": ["number"]}, "employee_grievance_steps": {"arbitration": "Grievance #", "date": "Date", "olr": "Certified Mail #", "files": "Uploads", "recommended_notes": "Notes", "grievance_template": "Generate Grievance Template", "arbitration_template": "Generate Demand for Arbitration Template", "hearing_date_template": "Request for Hearing Date Template", "overall_grievance_template": "Overall Grievance Template", "required_fields": ["step", "employee_grievance_id"]}, "common_terms": {"step_1": "Step II - Demand for Arbitration", "step_2": "Step III - Hearings"}, "hearings": {"hearing_date": "Date of Hearing", "transcripts": "Transcripts", "notes": "Description", "evidences": "Exhibits", "description": "Description", "briefs": "Briefs", "brief_date": "Date of Brief", "brief": "Description", "required_fields": ["hearing_date"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message", "sms_no_reply_text": "This is a no-reply PAPBA text notification", "change_request_notification": true, "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "devices": {"key_name": "Push notification"}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "reminders": {"key_name": "Reminders", "title": "Title", "description": "Description", "reminder_start_date": "Reminder Start Date", "reminder_end_date": "Reminder End Date", "user_ids": "User List", "time": "Time", "repeat": "Repeat", "enable_cron_logics": true, "enable_rights": true, "repeat_options": ["Never", "Daily", "Weekly", "Monthly", "Yearly", "Custom", "Weekdays", "Weekends", "Every 3 Months", "Every 6 Months"]}}, "ui": {"notes_timestamps": true, "notes_disabled_fields": ["notes", "description", "recommended_notes"], "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}, "employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["employees", "grievance"], "table_headers": ["name", "employment_status_name", "shield_number", "personal_phone", "birthday"], "tabs": ["profile", "benefits", "firearm_statuses", "awards", "grievances", "meeting_types", "uploads", "pacfs", "legislative_addresses"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["avatar", "name", "address", "precinct", "do_not_mail", "birthday", "age", "genders", "marital_statuses", "social_security_number", "a_number", "shield_number", "title_code", "previous_shield_number", "placard_number", "start_date", "member_start_date", "staff_member", "veteran_status", "notes"], "others": ["employee_employment_statuses", "employee_offices", "employee_ranks", "employee_positions", "employees.janus_card", "employees.janus_card_opt_out_date", "employees.app_downloaded"], "contacts": [{"is_restrict_emergency_contact_details": true, "contact_number": ["home_phone", "personal_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["personal_email"]}], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}, "hide_benefits": true, "benefits": {"isDetailsView": true, "benefits_order": ["beneficiaries", "benefit_coverages"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2"], "table_headers": ["grievance", "number", "date", "status"]}}, "employee_grievances": {"key_name": "Class Action Grievance", "is_filter": ["grievance"], "grievances_specific_logics_for_papba": true, "table_headers": ["grievance", "number", "date", "status"], "status_options": [{"value": "Pending", "key": "pending"}, {"value": "Settled", "key": "settled"}, {"value": "Withdrawn", "key": "withdrawn"}, {"value": "Decided", "key": "decided"}]}, "improper_practices": {"key_name": "Improper Practices", "table_headers": ["number", "date", "status", "improper_practice_type_name"], "status_options": [{"value": "Pending", "key": "pending"}, {"value": "Settled", "key": "settled"}, {"value": "Decided", "key": "decided"}], "tabs": ["Improper Practices", "Step II - Chairmans Conference", "Step III - Panel"]}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "employee_departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "notes"], "contacts": [{"is_restrict_emergency_contact_details": true, "contact_number": ["work_phone", "personal_phone", "home_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["work_email", "personal_email"]}]}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "offices", "firearm_statuses", "ranks", "grievances", "employment_statuses", "positions", "meeting_types", "units", "departments", "pacfs", "improper_practices"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "union_meetings", "janus", "pacfs", "grievances"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["employees.email", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "positions"], ["employees.title_code", "employees.previous_shield_number"], ["employees.staff_member", "employees.a_number"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.suffix", "offices", "ranks", "employment_statuses", "employees.email", "firearm_statuses", "positions", "employees.social_security_number", "employees.placard_number", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "employees.title_code", "employees.previous_shield_number", "genders", "marital_statuses", "employees.staff_member", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id", "employees.app_downloaded"], "default_columns": ["employees", "employees.shield_number", "ranks", "employment_statuses", "offices", "firearm_statuses"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"], "mailing_list_types": [{"key": "CustomAvery5160", "value": "Avery 5160 - 1\" x 2-5/8\""}, {"key": "CustomPostCard", "value": "Postcard"}, {"key": "CustomEnvelope10", "value": "Envelope 10"}]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices", "ranks"], ["employment_statuses", "firearm_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "marital_statuses"], ["employees.title_code", "employees.previous_shield_number"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "employees.placard_number"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "marital_statuses"], ["employees.title_code", "employees.previous_shield_number"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "pacfs": {"primary_filters": [["pacfs"], ["reports.started_at", "reports.ended_at"], ["reports.amount_from", "reports.amount_to"], ["reports.show_dollar_amount"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "employees.placard_number"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "marital_statuses"], ["employees.title_code", "employees.previous_shield_number"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["reports.columns"], ["grievances", "reports.grievance_command"], ["reports.started_at", "reports.ended_at"], ["reports.attorney_details", "reports.arbitrator_details"], ["employee_grievances.status", "reports.no_grievance_number"], ["reports.pending_with_future_hearing", "reports.pending_with_past_hearing"]], "secondary_filters": [["employees", "employees.previous_shield_number"], ["offices", "employment_statuses"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"]], "columns": ["employee_grievances.status", "reports.attorney_details", "reports.arbitrator_details", "employees", "employees.last_name", "employees.first_name", "employees.middle_name", "employees.suffix", "employees.shield_number", "employees.social_security_number", "employees.a_number", "ranks", "employment_statuses", "employees.previous_shield_number", "employees.birthday", "employees.age", "employees.email", "employees.cellphone", "offices"], "default_columns": ["employees", "employees.first_name", "employees.last_name", "employees.middle_name", "employees.suffix", "employees.shield_number"], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees", "contact_persons"], ["offices", "genders"], ["employment_statuses", "employees.placard_number"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "marital_statuses"], ["employees.title_code", "employees.previous_shield_number"], ["units", "departments"], ["notifications.congress_district_id", "notifications.assembly_district_id"], ["notifications.senate_district_id", "notifications.council_district_id"], ["employees.app_downloaded", ""]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "reminders": {"key_name": "Reminders", "table_headers": ["title", "description", "reminder_start_date", "reminder_end_date"]}}, "mobile": {"employees": {"key_name": "Profile", "tabs": ["profile", "contacts", "employee_offices", "employee_employment_statuses", "legislation"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "For any items you can't edit on the app please call the union office to request changes"}, "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "suffix": {"type": "SingleLineText", "name": "Suffix", "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apartment", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "required": true, "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "required": true, "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "ZipCode", "required": true, "actions": ["view", "edit"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Date Employed", "actions": ["view"]}, "member_start_date": {"type": "Date", "name": "Date Graduated", "actions": ["view"]}, "staff_member": {"type": "CheckBox", "name": "NJSPBA Enrollment", "actions": ["view"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address", "emergency_contacts"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"personal_phone": {"type": "PhoneNumber", "name": "Cell Phone", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "home_phone": {"type": "PhoneNumber", "name": "Home Phone", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "emergency_contacts": {"key_name": "Emergency Contacts", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=emergency", "attributes": {"personal_emergency_name": {"type": "SingleLineText", "name": "Personal Emergency Contact Name", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "personal_emergency_phone": {"type": "PhoneNumber", "name": "Personal Emergency Contact Phone", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_emergency_relationship": {"type": "SingleLineText", "name": "Personal Emergency Contact Relationship", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}, "colleague_emergency_name": {"type": "SingleLineText", "name": "Colleague Emergency Contact Name", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "colleague_emergency_phone": {"type": "PhoneNumber", "name": "Colleague Emergency Contact Phone", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "colleague_emergency_relationship": {"type": "SingleLineText", "name": "Colleague Emergency Contact Relationship", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}}}}, "employee_offices": {"key_name": "Commands", "request_type": "employee_office", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_offices?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"office_name": {"type": "DropDown", "name": "Command", "api": "offices", "api_key": "office_id", "required": true, "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "employee_employment_statuses": {"key_name": "Member Status", "request_type": "employee_employment_status", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_employment_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"employment_status_name": {"type": "DropDown", "name": "Status", "required": true, "api": "employment_statuses", "api_key": "employment_status_id", "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "legislation": {"key_name": "Legislation", "widget_type": "legislation", "empty_message": "No Legislative Details Found", "api": "legislative_addresses/employee_legislative_address?employee_id=[EMPLOYEE_ID]", "sub_sections": ["county_details", "congress_member_details", "assembly_member_details", "senate_member_details", "council_member_details", "comptroller_member_details", "executive_member_details", "attorney_member_details"], "county_details": {"key_name": "County Details", "attributes": {"county_name": {"type": "SingleLineText", "name": "County Name", "actions": ["view"]}}}, "congress_member_details": {"key_name": "Congress Member Det<PERSON>", "attributes": {"congress_member_name": {"type": "SingleLineText", "name": "Congress Member Name", "actions": ["view"]}, "congress_district_name": {"type": "SingleLineText", "name": "Congress Member District Name", "actions": ["view"]}, "congress_web_url": {"type": "Url", "name": "Congress Member Web URL", "actions": ["view"]}}}, "assembly_member_details": {"key_name": "Assembly Member Details", "attributes": {"assembly_member_name": {"type": "SingleLineText", "name": "Assembly Member Name", "actions": ["view"]}, "assembly_district_name": {"type": "SingleLineText", "name": "Assembly Member District Name", "actions": ["view"]}, "assembly_web_url": {"type": "Url", "name": "Assembly Member Web URL", "actions": ["view"]}}}, "senate_member_details": {"key_name": "Senate Member <PERSON><PERSON>", "attributes": {"senate_member_name": {"type": "SingleLineText", "name": "Senate Member Name", "actions": ["view"]}, "senate_district_name": {"type": "SingleLineText", "name": "Senate Member District Name", "actions": ["view"]}, "senate_web_url": {"type": "Url", "name": "Senate Member Web URL", "actions": ["view"]}}}, "council_member_details": {"key_name": "Council Member Details", "attributes": {"council_member_name": {"type": "SingleLineText", "name": "Council Member Name", "actions": ["view"]}, "council_district_name": {"type": "SingleLineText", "name": "Council Member District Name", "actions": ["view"]}, "council_web_url": {"type": "Url", "name": "Council Member Web URL", "actions": ["view"]}}}, "comptroller_member_details": {"key_name": "Comptroller Member Details", "attributes": {"comptroller_member_name": {"type": "SingleLineText", "name": "Comptroller Member Name", "actions": ["view"]}, "comptroller_web_url": {"type": "Url", "name": "Comptroller Member Web URL", "actions": ["view"]}}}, "executive_member_details": {"key_name": "Executive Member Details", "attributes": {"executive_member_name": {"type": "SingleLineText", "name": "Executive Member Name", "actions": ["view"]}, "executive_web_url": {"type": "Url", "name": "Executive Member Web URL", "actions": ["view"]}}}, "attorney_member_details": {"key_name": "District Attorney Member Details", "attributes": {"attorney_member_name": {"type": "SingleLineText", "name": "District Attorney Member Name", "actions": ["view"]}, "attorney_web_url": {"type": "Url", "name": "District Attorney Web URL", "actions": ["view"]}}}}}, "beneficiaries": {"key_name": "Beneficiaries", "request_type": "beneficiary", "required_key": "beneficiary_type", "empty_message": "There are no Beneficiaries added.", "actions": ["view"], "api": "beneficiaries?employee_id=[EMPLOYEE_ID]", "widget_type": "Details", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "Contact the union office at (************* or your Delegate to receive a Benefit Enrollment Card to make any beneficiary changes", "hyperlink_phone": "(*************"}, "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "actions": ["view"]}, "address": {"type": "SingleLineText", "name": "Address", "actions": ["view"]}, "beneficiary_type": {"type": "DropDown", "name": "Type", "required": true, "value": {"Primary": "Primary", "Contingent": "Contingent"}, "actions": ["view"]}, "percentage": {"type": "Number", "name": "Percentage", "required": true, "actions": ["view"]}, "file": {"type": "FileField", "name": "Upload", "selection_type": "single", "max_file_size": 10, "total_files": 1, "actions": ["view"]}}}, "notification": {"key_name": "Notifications", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "to": "<EMAIL>", "subject": "PAPBA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "api": "employees/update_password"}}}