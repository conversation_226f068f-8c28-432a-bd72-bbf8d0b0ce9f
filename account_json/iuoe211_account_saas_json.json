{"schema": {"benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "offices": {"key_name": "Work Location", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "officer_statuses": {"key_name": "Agency", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "disabilities": {"key_name": "Disability", "from_date": "From Date", "to_date": "To Date", "duration": "Number of Weeks and Days", "check_number": "Check Number", "check_date": "Check Date", "disability_auto_expire": true, "auto_expire_week": 26, "last_working_date": "Last Day Worked", "last_paid_date": "Last Date Paid", "return_to_work": "Return to Work Date", "expires_at": "Expired at", "file": "Upload", "required_fields": ["employee_id"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "birthday": "Date of Birth", "social_security_number": "SS #", "social_security_number_format": "9", "a_number": "Employee ID", "previous_shield_number": "IUOE Register #", "prescription": "Prescription #", "age": "Age", "do_not_mail": "Do Not Mail", "is_auto_prescription": "Auto Prescription", "autoload_prescription": true, "beneficiary_address_confirmation": true, "autoload_prescription_popup": "Do you want to auto update the prescription #?", "prescription_calc": 12.5, "start_date": "Hire Date", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "email": "Email", "allow_multiple_present_status": "true", "required_fields": ["name", "address", "first_name", "last_name", "birthday", "city", "social_security_number", "state", "street", "zipcode", "gender_id"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "a_number", "street", "start_date", "birthday", "social_security_number"], "associated_model": {"officer_statuses": ["name"], "benefit_coverages": ["name"], "beneficiaries": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["officer_status_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "auto_expire_benefits": true, "custom_expiration_logics": true, "employee_status": {"section_III": ["Deceased", "Terminated", "WF Benefit Suspended", "Agency not WF", "Union not WF"]}, "expire_relationship_types": ["spouse", "domestic_partner", "disabled_child", "disabled_step_child"], "required_fields": ["benefit_id", "employee_id", "start_date"]}, "benefit_disbursements": {"key_name": "<PERSON><PERSON><PERSON>", "benefit_name": "Benefit Type", "year": "Year", "date": "Serviced Date", "benefit_coverage_id": "Person Serviced", "entry_date": "Payment Date", "reference_number": "Reference #", "amount": "Amount", "notes": "Notes", "file": "Uploads", "required_fields": ["employee_id", "employee_benefit_id", "benefit_name"], "is_benefit_coverage_value": true, "is_common_benefit_disbursements": true}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id"]}, "ranks": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "benefit_coverages": {"key_name": "Dependents", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "age": "Age", "effective_date": "Effective Date", "expires_at": "Expiration", "files": "Uploads", "order_by_relationship": true, "cvs_person_code_autoupdate": true, "coverage_expire_age": 26, "expire_relationship_types": ["dependent", "step_child", "legal_guardian"], "required_fields": ["employee_id", "birthday", "name", "relationship"], "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Dependent", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Step Child", "key": "step_child"}, {"value": "Disabled <PERSON> Child", "key": "disabled_step_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}, {"value": "Legal Guardian", "key": "legal_guardian"}]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_attachments": "SMS attachments", "sms_no_reply_text": "Please note, this is a no-reply text.", "sms_to_value": [{"key": "home", "value": "Home"}, {"key": "personal", "value": "Cell"}]}, "reports": {"single_employee": "Single Member", "benefit_coverage_expiration": "Benefit Coverage Expiration", "benefit_coverages": "Benefit Dependents", "benefit_coverage_birthday": "Birthday", "union_meetings": "Union Meetings", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "meetings": "Meetings", "employment_statuses_from": "Member Status Start Date", "employment_statuses_to": "Member Status End Date", "dependent_count": "Dependent Count", "dependent_birth_from": "Dependent Birth From", "dependent_birth_to": "Dependent Birth To", "dependent_expire_from": "Dependent Expire From", "dependent_expire_to": "Dependent Expire To", "show_coverages": "Show Dependents", "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"notes_timestamps": true, "allow_single_timestamp": true, "notes_disabled_fields": ["notes", "description"], "employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "employment_status_name", "officer_status_name", "address", "birthday"], "tabs": ["profile", "benefits", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["name", "address", "precinct", "do_not_mail", "birthday", "age", "social_security_number", "genders", "previous_shield_number", "a_number", "is_auto_prescription", "prescription", "start_date", "notes"], "contacts": [{"contact_number": ["home_phone", "personal_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_officer_statuses", "employee_ranks", "employee_offices", "employee_positions"]}, "benefits": {"isDetailsView": true, "is_non_detail_view_disbursement": true, "benefits_Order": ["others", "benefitDetails", "disability", "beneficiaries", "lifeInsurance", "benefits"]}}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "officer_statuses", "ranks", "employment_statuses", "positions", "payment_types", "meeting_types", "genders"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefit_coverage_expiration", "benefit_coverages"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["employees.email", "ranks"], ["positions", "employment_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["employees.social_security_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "officer_statuses"], ["employees.previous_shield_number", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.suffix", "offices", "employment_statuses", "officer_statuses", "ranks", "employees.email", "positions", "employees.social_security_number", "employees.a_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "genders", "reports.dependent_count", "employees.previous_shield_number", "reports.employment_statuses_from", "reports.employment_statuses_to"], "default_columns": ["employees", "employees.a_number", "employment_statuses", "offices"], "actions": ["single_mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "benefit_coverage_expiration": {"primary_filters": [["reports.dependent_birth_from", "reports.dependent_birth_to"], ["reports.dependent_expire_from", "reports.dependent_expire_to"]], "secondary_filters": [["offices", "genders"], ["employees.email", "ranks"], ["positions", "employment_statuses"], ["employees.social_security_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "officer_statuses"], ["employees.previous_shield_number", ""]], "columns": ["employees", "offices", "employment_statuses", "officer_statuses", "ranks", "employees.email", "marital_statuses", "positions", "employees.social_security_number", "employees.a_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "genders", "employees.previous_shield_number"], "actions": ["excel_report", "pdf_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["offices", "genders"], ["employees.email", "ranks"], ["positions", "employment_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["employees.social_security_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "officer_statuses"], ["employees.previous_shield_number", ""]], "columns": ["employees", "offices", "employment_statuses", "officer_statuses", "ranks", "employees.email", "marital_statuses", "positions", "employees.social_security_number", "employees.a_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "genders", "employees.previous_shield_number"], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "is_notification_sms_to": true, "filters": [["employees"], ["offices", "genders"], ["employees.email", "ranks"], ["positions", "employment_statuses"], ["employees.social_security_number", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "officer_statuses"], ["employees.previous_shield_number", ""]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}