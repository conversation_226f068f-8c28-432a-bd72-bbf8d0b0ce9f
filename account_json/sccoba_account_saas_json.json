{"schema": {"notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_no_reply_text": "Please note, this is a no-reply text message."}, "offices": {"key_name": "Court Location", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "ranks": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "platoons": {"key_name": "Membership Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "pacfs": {"key_name": "Dues", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Sex", "name": "Name", "description": "Description", "required_fields": ["name"]}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "SS #", "social_security_number_format": "4", "veteran_status": "Veteran Status", "shield_number": "Shield Number", "a_number": "PO Registration", "placard_number": "Pay Grade", "start_date": "SCCOBA Start Date", "ncc_date": "UCS Start Date", "t_shirt_size": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work Phone", "email": "Email", "janus_card": "<PERSON><PERSON>t Out", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "allow_multiple_present_status": "true", "required_fields": ["name", "address", "first_name", "last_name", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "street", "start_date", "birthday"], "associated_model": {"contacts": ["value"], "offices": ["name"], "ranks": ["name"]}}, "custom_validations": {"starts_with": {"placard_number": "JG"}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}}, "awards": {"key_name": "Awards", "name": "Name", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_analytics": {"key_name": "Analytics", "customization": true, "required_tables": ["lodis"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Uploads", "required_fields": ["office_id", "employee_id", "incident_date"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "t_shirt_options": ["Small", "Medium", "Large", "XL", "XXL", "XXXL"]}, "common_terms": {"employee_analytics": "Analytics", "sick_bank": "Union Release Bank", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "employment_status_name", "shield_number", "personal_phone", "rank_name", "office_name"], "tabs": ["profile", "employee_analytics", "pacfs", "awards", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "precinct", "do_not_mail", "birthday", "genders", "social_security_number", "marital_statuses", "platoons", "affiliations", "veteran_status", "shield_number", "a_number", "placard_number", "start_date", "ncc_date", "t_shirt_size", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_positions", "employee_ranks", "delegate_assignments", "employees.janus_card", "employees.janus_card_opt_out_date"]}}, "settings": {"key_name": "Settings", "tabs": ["offices", "ranks", "employment_statuses", "marital_statuses", "positions", "meeting_types", "genders", "affiliations", "pacfs", "platoons"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "lodi", "union_meetings"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "marital_statuses"], ["affiliations", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["platoons", "employees.t_shirt_size"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "offices", "ranks", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.shield_number", "employees.a_number", "employees.placard_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.ncc_date", "employees.apartment", "employees.street", "genders", "affiliations", "pacfs", "platoons", "employees.t_shirt_size"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "marital_statuses"], ["affiliations", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["platoons", ""]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "marital_statuses"], ["affiliations", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["platoons", ""]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["offices", "ranks"], ["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.shield_number", "employees.city"], ["employees.state", "employees.zipcode"], ["affiliations", "platoons"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}