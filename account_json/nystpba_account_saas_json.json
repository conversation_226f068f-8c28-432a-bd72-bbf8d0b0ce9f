{"schema": {"notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "sms": "false", "filter": "Filter", "sms_no_reply_text": "Please note, this is a no-reply text."}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "birthday": "DOB", "age": "Age", "start_date": "Entry Date", "cellphone": "Cell Phone", "ncc_date": "Retirement Date", "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name"], "associated_model": {"contacts": ["value"], "employment_statuses": ["name"], "offices": ["name"], "positions": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "required_fields": []}}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "offices": {"key_name": "Troop", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "positions": {"key_name": "Delegate Number", "name": "Name", "description": "Description", "order_alphanumerically": true, "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "position_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["employees"], "table_headers": ["name", "employment_status_name", "office_name", "position_name"], "tabs": ["profile"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["name", "birthday", "age", "start_date", "ncc_date"], "contacts": [{"contact_number": ["personal_phone"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_positions", "employee_ranks"]}}, "settings": {"key_name": "Settings", "tabs": ["employment_statuses", "positions", "ranks", "offices"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["employment_statuses", "positions"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["ranks", "offices"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}