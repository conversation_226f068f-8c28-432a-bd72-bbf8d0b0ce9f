{"schema": {"change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status", "is_handling_name_from_json": true}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "do_not_mail": "Do Not Mail", "birthday": "DOB", "age": "Age", "social_security_number": "Social Security Number", "veteran_status": "Veteran", "a_number": "Pass Number", "shield_number": "BSC Number", "placard_number": "Placard Number", "start_date": "TA Start Date", "member_start_date": "TA Timeframe", "prom_prov": "Prom Prov", "prom_perm": "Prom Perm", "member_since": "Member Since", "notes": "Notes", "janus_card": "<PERSON><PERSON>t Out", "staff_member": "Staff Member", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "cellphone": "Cell Phone", "home_phone": "Home Phone", "work_phone": "Work Phone", "email": "Email", "social_security_number_format": "4", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "janus_card_status": "true", "analytics_of_employee_user": true, "close_active_status": "true", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "username": "Username", "app_downloaded": "App Downloaded", "active_status_to_be_close": [], "required_fields": ["name", "first_name", "last_name", "a_number", "shield_number", "unit_id"], "unique_fields": ["username", "a_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "a_number", "member_since"], "associated_model": {"titles": ["name"], "contacts": ["value"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["department_id", "section_id", "title_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "awards": {"key_name": "Awards", "name": "Name", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "dan_number": "DAN Number", "date": "Date", "description": "Description", "recommended_penalty": "Recommended Penalty", "was_employee_pds": "Was Employee PDS", "case_and_abeyance": "Case and Abeyance", "ta_implemented": "TA implemented", "abandonment_hearing": "Abandonment Hearing", "files": "Uploads", "is_settled": "Settled", "required_fields": ["employee_id", "discipline_setting_id", "discipline_charge_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Recommended Notes", "is_settled": "Settled", "required_fields": ["step", "employee_discipline_setting_id"]}, "employee_grievances": {"key_name": "Grievances", "number": "Grievance Number", "date": "Date", "violation_alleged": "Violation Alleged", "description": "Notes", "files": "Uploads", "is_settled": "Settled", "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "recommended_notes": "Recommended Notes", "is_settled": "Settled", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "departments": {"key_name": "Departments", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sections", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "pacfs": {"key_name": "PAF", "name": "Name", "description": "Description", "required_fields": ["name"]}, "titles": {"key_name": "Titles", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["department_id", "section_id", "name", "title_code"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_charges": {"key_name": "Discipline Charges", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Units", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "members": {"key_name": "Members"}, "leave_type": {"key_name": "Leave Type"}, "analytics_type": {"key_name": "Analytics Type"}, "roles": {"key_name": "Roles"}, "days_earned": {"key_name": "No Of Days Earned"}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meeting", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "case_and_abeyance": "Case and Abeyance", "abandonment_hearing": "Abandonment Hearing", "was_employee_pds": "Was Employee PDS", "ta_implemented": "TA implemented", "settled_steps": "Settled Steps", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "app_downloaded": "App Downloaded", "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Arbritration", "key": "arbritration"}]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message", "sms_attachments": "SMS attachments", "sms_no_reply_text": "Please note, this is a no-reply text.", "change_request_notification": true, "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "devices": {"key_name": "Push notification"}, "leaves": {"key_name": "Leaves", "started_at": "From", "ended_at": "To", "year_range": "Year Range", "leave_type": "Leave Type", "days_earned": "No Of Days Earned", "days": "No. Of Days Used", "days_used": "No. Of Days Used", "cashed_out": "Cashed Out", "pay_period_ending": "Pay Period Ending", "days_balanced": "Balance In Days", "notes": "Comments", "table_headers": {"analytics_configuration": ["year_range", "days_earned", "days_used", "days_balanced"], "leaves": ["started_at", "ended_at", "days", "notes"]}, "required_fields": ["started_at", "ended_at", "leave_type", "days_used", "days"]}, "analytics_configuration": {"key_name": "Analytics Configuration", "analytics_type": "Analytics Type", "days_earned": "No Of Days Earned", "notes": "Notes", "required_fields": ["employee_ids", "analytics_type", "days_earned"]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "arbritration": "ARBRITRATION", "employee_analytics": "Analytics", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "ava": "AVA"}, "employee_analytics": {"key_name": "Analytics", "analytics_type": "Analytics Type", "days": "No Of Days Earned", "notes": "Notes", "customization": true, "dashboard_stats": true, "enable_rights": true, "required_tables": ["sick_bank", "vacation", "ava", "personal"], "required_fields": ["members", "leave_type", "days_earned"], "leave_types": [{"value": "Sick Bank", "key": "sick"}, {"value": "Vacation", "key": "vacation"}, {"value": "Personal", "key": "personal"}, {"value": "AVA", "key": "ava"}]}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "forms": {"key_name": "Forms", "file": "Form", "name": "Form Name", "heading": "Heading"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_user_employee_analytics": true, "dynamic_tabs": ["employee_analytics"], "is_search": ["employees", "is_staff_member"], "employment_statuses_count": ["Active", "Active-MAILING RETURNED", "Active-NO CARDS", "Active - RCC", "Active - Labor Relations", "Active - <PERSON><PERSON>", "Active - In Arrears/Bad Standing", "Active-SIRTOA"], "table_headers": ["name", "a_number", "title_name", "personal_phone", "employment_status_name", "member_since"], "tabs": ["profile", "employee_analytics", "pacfs", "awards", "discipline_settings", "grievances", "uploads", "legislative_addresses"], "show_tabs": ["employee_analytics"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["a_number", "first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "precinct", "do_not_mail", "birthday", "age", "social_security_number", "genders", "marital_statuses", "a_number", "shield_number", "units", "start_date", "member_start_date", "prom_prov", "prom_perm", "member_since", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_employment_statuses", "employee_departments", "employee_sections", "employee_titles", "employees.title_code", "employees.janus_card", "employees.janus_card_opt_out_date", "employee_positions", "employees.staff_member", "employees.app_downloaded"], "login_credentials": ["username", "send_credentials", "enable_mobile_access"], "analytics": {"confirm_only_delete_this": true, "singular_headers": true, "create_new": true}}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "discipline_charge", "date", "is_settled"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "arbritration"], "table_headers": ["grievance", "number", "date", "is_settled"]}}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["number", "date", "violation_alleged", "description", "files", "is_settled"]}, "settings": {"key_name": "Settings", "tabs": ["departments", "sections", "pacfs", "titles", "employment_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "discipline_charges", "grievances", "genders", "units", "analytics_configuration"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "janus"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["departments", "sections"], ["titles", "titles.title_code"], ["employment_statuses", "genders"], ["marital_statuses", "positions"], ["employees.email", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "titles", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.social_security_number", "employees.a_number", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.city", "employees.state", "employees.zipcode", "departments", "sections", "pacfs", "employees.member_since", "employees.start_date", "units", "employees.prom_prov", "employees.prom_perm", "employees.apartment", "employees.street", "genders", "titles.title_code", "employees.app_downloaded", "titles.title_code", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id"], "default_columns": ["employees.a_number", "titles", "employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["discipline_charges"], ["reports.started_at", "reports.ended_at"], ["reports.ta_implemented", "reports.case_and_abeyance"], ["reports.abandonment_hearing", "reports.was_employee_pds"], ["reports.settled_steps"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "arbritration"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.settled_steps"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "report_columns": ["number"], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "arbritration"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["departments", "sections"], ["titles", "titles.title_code"], ["employment_statuses", "genders"], ["marital_statuses", "positions"], ["employees.email", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "notifications.congress_district_id"], ["notifications.assembly_district_id", "notifications.senate_district_id"], ["notifications.council_district_id", ""]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}, "mobile": {"employees": {"key_name": "Profile", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/memberprofile.png", "welcome_message": {"header": "WELCOME TO THE SSSA MEMBER APP!", "description": "THIS APP WAS CREATED BY THE EXECUTIVE BOARD TO BRING USEFUL RESOURCES TO OUR MEMBERS. THIS APP WILL ALSO BE USED TO COMMUNICATE WITH MEMBERS, SO <PERSON><PERSON>AS<PERSON> MAKE SURE NOTIFICATIONS ARE ENABLED.", "regards": "IN UNITY,\nMICHAEL CARRUBE\nPRESIDENT", "button_name": "CLICK HERE TO ENTER"}, "tabs": ["profile", "contacts", "employee_employment_statuses", "employee_departments", "employee_sections", "employee_titles", "legislation"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "required": true, "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apt", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "required": true, "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "required": true, "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "Zip Code", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "required": true, "actions": ["view", "edit"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "a_number": {"type": "SingleLineText", "name": "Pass Number", "actions": ["view"]}, "shield_number": {"type": "SingleLineText", "name": "BSC Number", "actions": ["view"]}, "unit_name": {"type": "DropDown", "name": "Unit", "api": "units", "api_key": "unit_id", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "TA Start Date", "actions": ["view"]}, "member_start_date": {"type": "SingleLineText", "name": "TA Timeframe", "actions": ["view"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address", "emergency_contacts"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"personal_phone": {"type": "PhoneNumber", "name": "Cell", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "home_phone": {"type": "PhoneNumber", "name": "Home", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "work_phone": {"type": "PhoneNumber", "name": "Work", "contact_for": "work", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"work_email": {"type": "Email", "name": "Work", "contact_for": "work", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "emergency_contacts": {"key_name": "Emergency Contacts", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=emergency", "attributes": {"personal_emergency_name": {"type": "SingleLineText", "name": "Personal Emergency Contact Name", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "personal_emergency_phone": {"type": "PhoneNumber", "name": "Personal Emergency Contact Phone", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_emergency_relationship": {"type": "SingleLineText", "name": "Personal Emergency Contact Relationship", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}, "colleague_emergency_name": {"type": "SingleLineText", "name": "Colleague Emergency Contact Name", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "colleague_emergency_phone": {"type": "PhoneNumber", "name": "Colleague Emergency Contact Phone", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "colleague_emergency_relationship": {"type": "SingleLineText", "name": "Colleague Emergency Contact Relationship", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}}}}, "employee_employment_statuses": {"key_name": "Member Status", "request_type": "employee_employment_status", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_employment_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"employment_status_name": {"type": "DropDown", "name": "Member Status", "api": "employment_statuses", "api_key": "employment_status_id", "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "employee_departments": {"key_name": "Departments", "request_type": "employee_department", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_departments?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"department_name": {"type": "DropDown", "name": "Department", "api": "departments", "api_key": "department_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_sections": {"key_name": "Sections", "request_type": "employee_section", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_sections?employee_id=[EMPLOYEE_ID]", "widget_type": "Assignments", "attributes": {"department_name": {"type": "DropDown", "name": "Department", "api": "departments", "dependent": "section_name", "api_key": "department_id", "required": true, "actions": ["view", "edit"]}, "section_name": {"type": "DropDown", "name": "Section", "api": "sections?department_id=[DEPARTMENT_ID]", "api_key": "section_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_titles": {"key_name": "Titles", "request_type": "employee_title", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_titles?employee_id=[EMPLOYEE_ID]", "widget_type": "Assignments", "attributes": {"department_name": {"type": "DropDown", "name": "Department", "api": "departments", "dependent": "section_name", "api_key": "department_id", "required": true, "actions": ["view"]}, "section_name": {"type": "DropDown", "name": "Section", "api": "sections?department_id=[DEPARTMENT_ID]", "api_key": "section_id", "required": true, "actions": ["view"]}, "title_name": {"type": "DropDown", "name": "Title", "api": "titles?section_id=[SECTION_ID]", "api_key": "title_id", "required": true, "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "legislation": {"key_name": "Legislation", "widget_type": "legislation", "empty_message": "No Legislative Details Found", "api": "legislative_addresses/employee_legislative_address?employee_id=[EMPLOYEE_ID]", "sub_sections": ["county details", "congress_member_details", "assembly_member_details", "senate_member_details", "council_member_details", "comptroller_member_details", "executive_member_details", "attorney_member_details"], "county_details": {"key_name": "County Details", "attributes": {"county_name": {"type": "SingleLineText", "name": "County Name", "actions": ["view"]}}}, "congress_member_details": {"key_name": "Congress Member Det<PERSON>", "attributes": {"congress_member_name": {"type": "SingleLineText", "name": "Congress Member Name", "actions": ["view"]}, "congress_district_name": {"type": "SingleLineText", "name": "Congress Member District Name", "actions": ["view"]}, "congress_web_url": {"type": "Url", "name": "Congress Member Web URL", "actions": ["view"]}}}, "assembly_member_details": {"key_name": "Assembly Member Details", "attributes": {"assembly_member_name": {"type": "SingleLineText", "name": "Assembly Member Name", "actions": ["view"]}, "assembly_district_name": {"type": "SingleLineText", "name": "Assembly Member District Name", "actions": ["view"]}, "assembly_web_url": {"type": "Url", "name": "Assembly Member Web URL", "actions": ["view"]}}}, "senate_member_details": {"key_name": "Senate Member <PERSON><PERSON>", "attributes": {"senate_member_name": {"type": "SingleLineText", "name": "Senate Member Name", "actions": ["view"]}, "senate_district_name": {"type": "SingleLineText", "name": "Senate Member District Name", "actions": ["view"]}, "senate_web_url": {"type": "Url", "name": "Senate Member Web URL", "actions": ["view"]}}}, "council_member_details": {"key_name": "Council Member Details", "attributes": {"council_member_name": {"type": "SingleLineText", "name": "Council Member Name", "actions": ["view"]}, "council_district_name": {"type": "SingleLineText", "name": "Council Member District Name", "actions": ["view"]}, "council_web_url": {"type": "Url", "name": "Council Member Web URL", "actions": ["view"]}}}, "comptroller_member_details": {"key_name": "Comptroller Member Details", "attributes": {"comptroller_member_name": {"type": "SingleLineText", "name": "Comptroller Member Name", "actions": ["view"]}, "comptroller_web_url": {"type": "Url", "name": "Comptroller Member Web URL", "actions": ["view"]}}}, "executive_member_details": {"key_name": "Executive Member Details", "attributes": {"executive_member_name": {"type": "SingleLineText", "name": "Executive Member Name", "actions": ["view"]}, "executive_web_url": {"type": "Url", "name": "Executive Member Web URL", "actions": ["view"]}}}, "attorney_member_details": {"key_name": "Attorney Member Details", "attributes": {"attorney_member_name": {"type": "SingleLineText", "name": "Attorney Member Name", "actions": ["view"]}, "attorney_web_url": {"type": "Url", "name": "Attorney Member Web URL", "actions": ["view"]}}}}}, "analytics": {"key_name": "Analytics", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/analysis.png", "tabs": ["sick_bank", "vacation", "personal", "ava"], "sick_bank": {"key_name": "Sick Bank", "empty_message": "There are no Sick Bank hours posted.", "actions": ["view", "request"], "api": "analytics?employee_id=[EMPLOYEE_ID]&analytics_type=sick", "widget_type": "Action", "header": {"title": "Sick Bank", "value_from": "API", "value_key": "analytics_configuration_total.sick", "suffix_value": "Days"}, "attributes": {"year_range": {"type": "SingleLineText", "name": "Year Range", "actions": ["view"]}, "days_earned": {"type": "Number", "name": "No Of Days Earned", "actions": ["view"]}, "days_used": {"type": "Number", "name": "No Of Days Used", "actions": ["view"]}, "days_balance": {"type": "Number", "name": "Balance In Days", "actions": ["view"]}, "action_items": [{"detail_view": {"key_name": "Detail View", "request_type": "leave", "empty_message": "There aren't any Sick Banks.", "actions": ["view", "request"], "api": "leaves?employee_id=[EMPLOYEE_ID]&duration_from=[DURATION_FROM]&duration_to=[DURATION_TO]&leave_type=sick", "widget_type": "Analysis", "leave_type": "sick", "attributes": {"started_at": {"type": "Date", "name": "From", "required": true, "actions": ["view", "edit"]}, "ended_at": {"type": "Date", "name": "To", "required": true, "actions": ["view", "edit"]}, "days": {"type": "Number", "name": "No Of Days Used", "required": true, "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}}]}}, "vacation": {"key_name": "Vacation", "empty_message": "There are no Vacation hours posted.", "actions": ["view", "request"], "api": "analytics?employee_id=[EMPLOYEE_ID]&analytics_type=vacation", "widget_type": "Action", "header": {"title": "Vacation", "value_from": "API", "value_key": "analytics_configuration_total.vacation", "suffix_value": "Days"}, "attributes": {"year_range": {"type": "SingleLineText", "name": "Year Range", "actions": ["view"]}, "days_earned": {"type": "Number", "name": "No Of Days Earned", "actions": ["view"]}, "days_used": {"type": "Number", "name": "No Of Days Used", "actions": ["view"]}, "days_balance": {"type": "Number", "name": "Balance In Days", "actions": ["view"]}, "action_items": [{"detail_view": {"key_name": "Detail View", "request_type": "leave", "empty_message": "There aren't any Vacations.", "actions": ["view", "request"], "api": "leaves?employee_id=[EMPLOYEE_ID]&duration_from=[DURATION_FROM]&duration_to=[DURATION_TO]&leave_type=vacation", "widget_type": "Analysis", "leave_type": "vacation", "attributes": {"started_at": {"type": "Date", "name": "From", "required": true, "actions": ["view", "edit"]}, "ended_at": {"type": "Date", "name": "To", "required": true, "actions": ["view", "edit"]}, "days": {"type": "Number", "name": "No Of Days Used", "required": true, "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}}]}}, "personal": {"key_name": "Personal", "empty_message": "There are no Personal hours posted.", "actions": ["view", "request"], "api": "analytics?employee_id=[EMPLOYEE_ID]&analytics_type=personal", "widget_type": "Action", "header": {"title": "Personal", "value_from": "API", "value_key": "analytics_configuration_total.personal", "suffix_value": "Days"}, "attributes": {"year_range": {"type": "SingleLineText", "name": "Year Range", "actions": ["view"]}, "days_earned": {"type": "Number", "name": "No Of Days Earned", "actions": ["view"]}, "days_used": {"type": "Number", "name": "No Of Days Used", "actions": ["view"]}, "days_balance": {"type": "Number", "name": "Balance In Days", "actions": ["view"]}, "action_items": [{"detail_view": {"key_name": "Detail View", "request_type": "leave", "empty_message": "There aren't any Personals.", "actions": ["view", "request"], "api": "leaves?employee_id=[EMPLOYEE_ID]&duration_from=[DURATION_FROM]&duration_to=[DURATION_TO]&leave_type=personal", "widget_type": "Analysis", "leave_type": "personal", "attributes": {"started_at": {"type": "Date", "name": "From", "required": true, "actions": ["view", "edit"]}, "ended_at": {"type": "Date", "name": "To", "required": true, "actions": ["view", "edit"]}, "days": {"type": "Number", "name": "No Of Days Used", "required": true, "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}}]}}, "ava": {"key_name": "AVA", "empty_message": "There are no AVA hours posted.", "actions": ["view", "request"], "api": "analytics?employee_id=[EMPLOYEE_ID]&analytics_type=ava", "widget_type": "Action", "header": {"title": "AVA", "value_from": "API", "value_key": "analytics_configuration_total.ava", "suffix_value": "Days"}, "attributes": {"year_range": {"type": "SingleLineText", "name": "Year Range", "actions": ["view"]}, "days_earned": {"type": "Number", "name": "No Of Days Earned", "actions": ["view"]}, "days_used": {"type": "Number", "name": "No Of Days Used", "actions": ["view"]}, "days_balance": {"type": "Number", "name": "Balance In Days", "actions": ["view"]}, "action_items": [{"detail_view": {"key_name": "Detail View", "request_type": "leave", "empty_message": "There aren't any AVAs.", "actions": ["view", "request"], "api": "leaves?employee_id=[EMPLOYEE_ID]&duration_from=[DURATION_FROM]&duration_to=[DURATION_TO]&leave_type=ava", "widget_type": "Analysis", "leave_type": "ava", "attributes": {"started_at": {"type": "Date", "name": "From", "required": true, "actions": ["view", "edit"]}, "ended_at": {"type": "Date", "name": "To", "required": true, "actions": ["view", "edit"]}, "days": {"type": "Number", "name": "No Of Days Used", "required": true, "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}}]}}}, "forms": {"key_name": "Forms", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/form.png", "empty_message": "There are no Forms.", "api": "forms?file_type=form", "widget_type": "Forms", "actions": ["view"], "attributes": {"file": {"type": "FileField", "name": "Form", "actions": ["view"]}, "name": {"type": "SingleLineText", "name": "Form Name", "actions": ["view"]}, "heading": {"type": "SingleLineText", "name": "Heading", "actions": ["view"]}}}, "uploads": {"key_name": "Uploads", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/upload.png", "empty_message": "No Uploads", "actions": ["view", "new"], "api": "uploads?employee_id=[EMPLOYEE_ID]", "widget_type": "FileData", "attributes": {"file": {"type": "FileField", "name": "File", "selection_type": "single", "max_file_size": 10, "total_files": 1, "required": true, "actions": ["view", "edit"]}, "date": {"type": "FromAPI", "name": "Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "notification": {"key_name": "Notifications", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/notification.png", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/contact_us.png", "to": "<EMAIL>", "subject": "SSSA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/reset-password.png", "api": "employees/update_password"}}}