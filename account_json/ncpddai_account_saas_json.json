{"schema": {"employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "county": "County", "do_not_mail": "Do Not Mail", "birthday": "DOB", "age": "Age", "title_code": "Spouse", "social_security_number": "Social Security Number", "social_security_number_format": "9", "veteran_status": "Veteran Status", "a_number": "Serial Number", "shield_number": "Shield Number", "responder_911": "9/11 Re<PERSON>onder", "member_start_date": "Date of Appointment", "start_date": "DD Date", "notes": "Notes", "longevity_date": "Longevity Date", "janus_card": "<PERSON><PERSON>t Out", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "janus_card_status": "true", "register_vote": "Education", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "allow_multiple_present_status": "false", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode", "a_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "street", "start_date", "birthday"]}}, "contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"]}}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "tour_of_duties": {"key_name": "Grade", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "departments": {"key_name": "Mailing List Category", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"key_name": "Mailing List Category", "required_fields": ["department_id", "employee_id"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id"]}, "delegate_employees": {"key_name": "Delegate Name"}, "positions": {"key_name": "Union Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"], "customization": true, "required_tables": ["firearm_statuses"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "notified_date": "Date COBA Notified", "return_date": "Date of return", "office_name": "Location", "injury": "Injury #", "wcb": "WCB #", "carrier_case": "Carrier Case #", "approved": "Approved", "denied": "Denied", "denied_reason_ids": "Denied Reason", "notes": "Remarks", "files": "Files", "required_fields": ["employee_id", "incident_date"]}, "lodi_request_tab": {"date": "Date", "reason": "Reason for Request", "status": "Status", "remarks": "Remarks", "files": "Uploads", "required_fields": [], "reasons_types": {"medscope": ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Paragraph-13 Disputes"], "hearing": ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Did not occur in the Performance of Duties"], "arbitration": ["Causal Connection", "Did not occur in the Performance of Duties"]}, "status_types": {"medscope": ["Granted", "Denied", "Pending", "Moved to Arbitration", "Moved to 207c Hearing"], "hearing": ["Granted", "Denied", "Pending"], "arbitration": ["Granted", "Denied", "Pending", "Settled", "Other"]}}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "life_insurances": {"key_name": "Life Insurance", "insurance_type": "Type", "amount": "Amount", "start_date": "As of Date", "age": "Age", "notes": "Notes", "files": "Uploads", "member_contributions": "Member Contribution", "age_group_type": "Age Group", "required_fields": ["insurance_type", "employee_id", "amount"], "member_contribution": [{"low": 1, "high": 29, "amount": {"50000": 1.2, "100000": 2.4, "200000": 4.8}}, {"low": 30, "high": 34, "amount": {"50000": 1.37, "100000": 2.74, "200000": 5.47}}, {"low": 35, "high": 39, "amount": {"50000": 1.68, "100000": 3.36, "200000": 6.72}}, {"low": 40, "high": 44, "amount": {"50000": 2.4, "100000": 4.8, "200000": 9.6}}, {"low": 45, "high": 49, "amount": {"50000": 3.6, "100000": 7.2, "200000": 14.4}}, {"low": 50, "high": 54, "amount": {"50000": 5.52, "100000": 11.04, "200000": 22.08}}, {"low": 55, "high": 59, "amount": {"50000": 9.96, "100000": 19.92, "200000": 39.84}}, {"low": 60, "high": 64, "amount": {"50000": 15.05, "100000": 30.1, "200000": 60.2}}, {"low": 65, "high": 69, "amount": {"50000": 25.6, "100000": 51.22, "200000": 102.43}}, {"low": 70, "high": 99, "amount": {"50000": 48.38, "100000": 96.77, "200000": 193.53}}], "life_insurances_premium": {"key_name": "Premium", "member_contribution": "Member Contribution", "total_contribution": "Total Contribution"}, "insurance_amount": {"supplemental": [50000, 100000, 200000], "basic": [15000]}, "insurance_types": [{"value": "Basic", "key": "basic"}, {"value": "Supplemental", "key": "supplemental"}]}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "spouse_contribution": "Spouse Contribution", "relationship_amount_validation": {"basic": {"spouse": [15000], "child": [5000]}, "supplemental": {"spouse": [25000, 50000, 100000]}}, "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "address": "Address", "date": "DOB", "age": "Age", "required_fields": ["life_insurance_id", "employee_id", "name", "relationship", "amount"], "spouse_contribution_value": {"amount": {"25000": 1.82, "50000": 3.63, "100000": 7.26, "1000000": 999.99}}, "dependents_premium": {"key_name": "Premium", "spouse_contribution": "Spouse Contribution", "total_contribution": "Total Contribution"}}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Discipline Type", "charge": "Charge", "dan_number": "Case Number", "date": "Date", "description": "Remarks", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Grievances", "charge": "Charge", "number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "officer_statuses": {"key_name": "Officer Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievance_steps": {"date": "Date", "grievance_status_id": "Status", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "pacfs": {"key_name": "Dues", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "payment_type_id": "Payment Type", "notes": "Notes", "required_fields": ["pacf_id", "employee_id", "payment_type_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Member", "lodi": "<PERSON><PERSON>", "janus": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "filed_olr_from_date": "OLR From Date", "filed_olr_to_date": "OLR To Date", "step_1_from_date": "Step I From Date", "step_1_to_date": "Step I To Date", "step_2_from_date": "Step II From Date", "step_2_to_date": "Step II To Date", "step_3_from_date": "Step III From Date", "step_3_to_date": "Step III To Date", "arbritration_from_date": "Arbritration From Date", "arbritration_to_date": "Arbritration To Date", "win": "Win", "loss": "Loss", "dob_ranges": "DOB Ranges", "life_insurances": "Life Insurances", "age_group_type": "Type", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "dob_ranges_options": [{"value": "Less than 30", "key": "30"}, {"value": "30 to 34", "key": "30-34"}, {"value": "35 to 39", "key": "35-39"}, {"value": "40 to 44", "key": "40-44"}, {"value": "45 to 49", "key": "45-49"}, {"value": "50 to 54", "key": "50-54"}, {"value": "55 to 59", "key": "55-59"}, {"value": "60 to 64", "key": "60-64"}, {"value": "65 to 69", "key": "65-69"}, {"value": "70 to 99", "key": "70-99"}], "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Step III", "key": "step_3"}, {"value": "Arbritration", "key": "arbritration"}]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "lodis": "<PERSON><PERSON>", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration", "employee_analytics": "Analytics"}, "employee_analytics": {"key_name": "Analytics", "customization": true, "required_tables": ["lodis"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_no_reply_text": " This is a no-reply NCPDDAI text. Contact us if you need more information.", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "facilities": {"key_name": "Previous Agency", "name": "Name", "fax": "Description", "required_fields": ["name"]}, "employee_facilities": {"key_name": "Previous Agency", "start_date": "Start Date", "end_date": "End Date", "required_fields": ["facility_id", "employee_id"]}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "shield_number", "birthday", "start_date", "address"], "tabs": ["profile", "firearm_statuses", "employee_analytics", "benefits", "awards", "pacfs", "discipline_settings", "grievances", "meeting_types", "uploads", "legislative_addresses"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "precinct", "do_not_mail", "birthday", "age", "social_security_number", "genders", "marital_statuses", "title_code", "veteran_status", "a_number", "shield_number", "responder_911", "member_start_date", "start_date", "longevity_date", "affiliations", "tour_of_duties", "register_vote", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_facilities", "delegate_assignments", "employee_positions", "employee_ranks", "employees.janus_card", "employees.janus_card_opt_out_date"], "hide_delete_action": true}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}, "life_insurances": {"table_headers": ["insurance_type", "amount", "start_date", "notes"], "insurance_type": ["basic", "premium", "supplemental", "none"], "insurance_amount": [50000, 100000, 200000], "tabs": ["life_insurances", "premium"]}, "dependents": {"tabs": ["dependents", "premium"]}, "lodis": {"tabs": ["lodis", "medscope", "hearing", "arbitration"], "table_headers": ["incident_date", "notified_date", "return_date", "office_name", "notes", "files"]}}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "employee_departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "firearm_statuses", "ranks", "pacfs", "employment_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "meeting_types", "genders", "affiliations", "tour_of_duties", "units", "departments", "facilities"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "benefits", "lodi", "union_meetings", "janus", "life_insurances"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "offices", "ranks", "employment_statuses", "employees.email", "firearm_statuses", "marital_statuses", "positions", "employees.social_security_number", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.county", "employees.start_date", "employees.apartment", "employees.street", "employees.member_start_date", "employees.longevity_date", "affiliations", "tour_of_duties", "genders", "pacfs", "facilities", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id"], "default_columns": ["employees", "employees.shield_number", "ranks", "employment_statuses", "offices", "firearm_statuses", "marital_statuses"], "actions": ["single_mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "life_insurances": {"primary_filters": [["reports.age_group_type", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees", "contact_persons"], ["offices", "genders"], ["ranks", "employment_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.county", "employees.responder_911"], ["units", "departments"], ["facilities", "notifications.congress_district_id"], ["notifications.assembly_district_id", "notifications.senate_district_id"], ["notifications.council_district_id", ""]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}