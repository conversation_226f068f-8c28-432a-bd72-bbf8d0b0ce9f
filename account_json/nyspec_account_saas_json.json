{"schema": {"notifications": {"key_name": "Notifications", "subject": "Subject", "email_message": "Email Message", "sms_disabled": true, "email": "false", "filter": "Filter", "sendgrid_templates": true}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip", "cell_phone": "Cell Phone", "email": "Email", "required_fields": ["name", "first_name", "last_name"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name"]}}, "contacts": {"email_address": {"key_name": "Email address", "personal_email": "Email", "required_fields": []}, "contact_number": {"key_name": "Contact Number", "personal_phone": "Cell Phone", "required_fields": []}}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "offices": {"key_name": "Company Affiliation", "name": "Name", "fax": "Description", "required_fields": ["name"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "ranks": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["rank_id", "employee_id"]}, "positions": {"key_name": "Union Affiliation", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "position_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "reports": {"single_employee": "Flex Report", "columns": "Report Columns", "employee_name": "Member Name", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["employees", "ranks"], "table_headers": ["name", "employment_status_name", "position_name", "office_name"], "tabs": ["profile"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["name", "address"], "contacts": [{"contact_number": ["personal_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_ranks", "employee_positions", "employee_offices"]}}, "settings": {"key_name": "Settings", "tabs": ["employment_statuses", "ranks", "positions", "offices"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["employment_statuses", "positions"], ["ranks", "offices"]]}, "reports": {"key_name": "Report", "tabs": ["single_employee"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employment_statuses", "positions"], ["ranks", "offices"]], "columns": ["employees", "employment_statuses", "positions", "ranks", "offices"], "default_columns": ["employees"], "actions": ["excel_report", "pdf_report"]}}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}