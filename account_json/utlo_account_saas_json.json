{"schema": {"change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status"}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message", "change_request_notification": true, "sms_attachments": "SMS attachments", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "devices": {"key_name": "Push notification"}, "offices": {"key_name": "Departments", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "ranks": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "platoons": {"key_name": "Membership Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "affiliations": {"key_name": "RDO", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Leadership", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Company", "name": "Name", "description": "Description", "required_fields": ["name"]}, "tour_of_duties": {"key_name": "Tour Of Duty", "name": "Name", "description": "Description", "required_fields": ["name"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "departments": {"key_name": "Divisions", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sub-Divisions", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "Social Security Number", "social_security_number_format": "4", "veteran_status": "Veteran Status", "a_number": "Pass Number", "shield_number": "BSC Number", "start_date": "MTA Start Date", "notes": "Notes", "janus_card": "<PERSON><PERSON>t Out", "staff_member": "Staff Member", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "allow_multiple_present_status": "false", "primary_work_location": "Primary Work Location", "username": "Username", "app_downloaded": "App Downloaded", "required_fields": ["name", "first_name", "last_name", "shield_number"], "unique_fields": ["shield_number", "username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "member_since", "street", "start_date", "birthday"], "associated_model": {"benefit_coverages": ["name"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_grievances": {"key_name": "Grievances", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["grievance_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "reports": {"single_employee": "Single Member", "lodi": "IOD", "union_meetings": "Meeting Types", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "pacfs": "Dues Payment", "app_downloaded": "App Downloaded", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "common_terms": {"employee_analytics": "Analytics", "sick_bank": "Union Release Bank", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}, "pacfs": {"key_name": "Dues Payments", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "auto_dues_status": true, "required_fields": ["pacf_id", "employee_id"]}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "shield_number", "birthday", "start_date", "address"], "tabs": ["profile", "pacfs", "employee_analytics", "benefits", "awards", "discipline_settings", "grievances", "meeting_types", "uploads", "legislative_addresses"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "precinct", "do_not_mail", "birthday", "social_security_number", "genders", "marital_statuses", "platoons", "affiliations", "veteran_status", "a_number", "shield_number", "units", "start_date", "primary_work_location", "tour_of_duties", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["work_email", "personal_email"]}], "login_credentials": ["username", "send_credentials", "enable_mobile_access"], "others": ["employee_offices", "employee_employment_statuses", "employee_departments", "employee_sections", "employee_ranks", "employees.janus_card", "employees.janus_card_opt_out_date", "employee_positions", "employees.staff_member", "employees.app_downloaded"]}}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["date", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "departments", "sections", "ranks", "pacfs", "employment_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "meeting_types", "genders", "affiliations", "units", "platoons", "tour_of_duties"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "lodi", "union_meetings", "disciplines", "grievances", "pacfs"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["departments", "sections"], ["offices", "genders"], ["platoons", "tour_of_duties"], ["employment_statuses", "marital_statuses"], ["ranks", "units"], ["employees.email", "employees.shield_number"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["affiliations", "employees.app_downloaded"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "departments", "sections", "ranks", "units", "offices", "employment_statuses", "employees.email", "marital_statuses", "positions", "pacfs", "employees.social_security_number", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "employees.primary_work_location", "genders", "affiliations", "platoons", "tour_of_duties", "employees.app_downloaded"], "default_columns": ["employees", "employees.shield_number", "employment_statuses", "offices", "marital_statuses"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["departments", "sections"], ["platoons", "tour_of_duties"], ["offices", "positions"], ["ranks", "units"], ["employment_statuses", "marital_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["affiliations", "employees.app_downloaded"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["platoons", "tour_of_duties"], ["offices", "positions"], ["ranks", "units"], ["employment_statuses", "marital_statuses"], ["employees.email", "employees.shield_number"], ["employees.shield_number", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["affiliations", "employees.app_downloaded"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.meetings"]], "secondary_filters": [["employees"], ["departments", "sections"], ["platoons", "tour_of_duties"], ["offices", "positions"], ["ranks", "units"], ["employment_statuses", "marital_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["affiliations", "employees.app_downloaded"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["platoons", "tour_of_duties"], ["offices", "positions"], ["ranks", "units"], ["employment_statuses", "marital_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.email", "employees.work_phone"], ["employees.city", "employees.state"], ["affiliations", "employees.zipcode"], ["employees.app_downloaded", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["platoons", "tour_of_duties"], ["offices", "positions"], ["employment_statuses", "marital_statuses"], ["ranks", "units"], ["employees.shield_number", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.email", "employees.home_phone"], ["employees.email", "employees.work_phone"], ["employees.city", "employees.state"], ["affiliations", "employees.zipcode"], ["employees.app_downloaded", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "pacfs": {"primary_filters": [["pacfs"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["platoons", "tour_of_duties"], ["offices", "positions"], ["ranks", "units"], ["employment_statuses", "marital_statuses"], ["employees.shield_number", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.email", "employees.work_phone"], ["employees.city", "employees.state"], ["affiliations", "employees.zipcode"], ["employees.app_downloaded", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["departments", "sections"], ["ranks", "employees.social_security_number"], ["offices", "positions"], ["tour_of_duties", ""], ["platoons", "units"], ["employment_statuses", "marital_statuses"], ["employees.a_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.city", "employees.state"], ["employees.zipcode", "affiliations"], ["employees.app_downloaded", "notifications.congress_district_id"], ["notifications.assembly_district_id", "notifications.senate_district_id"], ["notifications.council_district_id", ""]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}, "mobile": {"employees": {"key_name": "Profile", "tabs": ["profile", "contacts", "employee_offices", "employee_employment_statuses", "employee_departments", "employee_sections", "employee_ranks", "employee_positions", "legislation"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apt", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "Zip Code", "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "actions": ["view", "edit"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "platoon_name": {"type": "DropDown", "name": "Membership Type", "api": "platoons", "api_key": "platoon_id", "actions": ["view", "edit"]}, "affiliation_name": {"type": "DropDown", "name": "RDO", "api": "affiliations", "api_key": "affiliation_id", "actions": ["view", "edit"]}, "tour_of_duty_name": {"type": "DropDown", "name": "Tour Of Duty", "api": "tour_of_duties", "api_key": "tour_of_duty_id", "actions": ["view", "edit"]}, "shield_number": {"type": "SingleLineText", "name": "BSC Number", "actions": ["view"]}, "a_number": {"type": "SingleLineText", "name": "Pass Number", "actions": ["view", "edit"]}, "unit_name": {"type": "DropDown", "name": "Company", "api": "units", "api_key": "unit_id", "actions": ["view", "edit"]}, "primary_work_location": {"type": "SingleLineText", "name": "Primary Work Location", "actions": ["view", "edit"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address", "emergency_contacts"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"personal_phone": {"type": "PhoneNumber", "name": "Personal", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "home_phone": {"type": "PhoneNumber", "name": "Home", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "work_phone": {"type": "PhoneNumber", "name": "Work", "contact_for": "work", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}, "work_email": {"type": "Email", "name": "Work", "contact_for": "work", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "emergency_contacts": {"key_name": "Emergency Contacts", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=emergency", "attributes": {"personal_emergency_name": {"type": "SingleLineText", "name": "Personal Emergency Contact Name", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "personal_emergency_phone": {"type": "PhoneNumber", "name": "Personal Emergency Contact Phone", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_emergency_relationship": {"type": "SingleLineText", "name": "Personal Emergency Contact Relationship", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}, "colleague_emergency_name": {"type": "SingleLineText", "name": "Colleague Emergency Contact Name", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "colleague_emergency_phone": {"type": "PhoneNumber", "name": "Colleague Emergency Contact Phone", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "colleague_emergency_relationship": {"type": "SingleLineText", "name": "Colleague Emergency Contact Relationship", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}}}}, "employee_offices": {"key_name": "Departments", "request_type": "employee_office", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_offices?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"office_name": {"type": "DropDown", "name": "Department", "api": "offices", "api_key": "office_id", "required": true, "actions": ["view", "edit"]}}}, "employee_employment_statuses": {"key_name": "Member Status", "request_type": "employee_employment_status", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_employment_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"employment_status_name": {"type": "DropDown", "name": "Member Status", "api": "employment_statuses", "api_key": "employment_status_id", "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "employee_departments": {"key_name": "Divisions", "request_type": "employee_department", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_departments?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"department_name": {"type": "DropDown", "name": "Division", "api": "departments", "api_key": "department_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_sections": {"key_name": "Sub-Divisions", "request_type": "employee_section", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_sections?employee_id=[EMPLOYEE_ID]", "widget_type": "Assignments", "attributes": {"department_name": {"type": "DropDown", "name": "Division", "api": "departments", "dependent": "section_name", "api_key": "department_id", "required": true, "actions": ["view", "edit"]}, "section_name": {"type": "DropDown", "name": "Sub Division ", "api": "sections?department_id=[DEPARTMENT_ID]", "api_key": "section_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_ranks": {"key_name": "Titles", "request_type": "employee_rank", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_ranks?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"rank_name": {"type": "DropDown", "name": "Title", "api": "ranks", "api_key": "rank_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "employee_positions": {"key_name": "Leadership", "request_type": "employee_position", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_positions?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"position_name": {"type": "DropDown", "name": "Leadership", "api": "positions", "api_key": "position_id", "required": true, "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view"]}}}, "legislation": {"key_name": "Legislation", "widget_type": "legislation", "empty_message": "No Legislative Details Found", "api": "legislative_addresses/employee_legislative_address?employee_id=[EMPLOYEE_ID]", "sub_sections": ["county_details", "congress_member_details", "assembly_member_details", "senate_member_details", "council_member_details", "comptroller_member_details", "executive_member_details", "attorney_member_details"], "county_details": {"key_name": "County Details", "attributes": {"county_name": {"type": "SingleLineText", "name": "County Name", "actions": ["view"]}}}, "congress_member_details": {"key_name": "Congress Member Det<PERSON>", "attributes": {"congress_member_name": {"type": "SingleLineText", "name": "Congress Member Name", "actions": ["view"]}, "congress_district_name": {"type": "SingleLineText", "name": "Congress Member District Name", "actions": ["view"]}, "congress_web_url": {"type": "Url", "name": "Congress Member Web URL", "actions": ["view"]}}}, "assembly_member_details": {"key_name": "Assembly Member Details", "attributes": {"assembly_member_name": {"type": "SingleLineText", "name": "Assembly Member Name", "actions": ["view"]}, "assembly_district_name": {"type": "SingleLineText", "name": "Assembly Member District Name", "actions": ["view"]}, "assembly_web_url": {"type": "Url", "name": "Assembly Member Web URL", "actions": ["view"]}}}, "senate_member_details": {"key_name": "Senate Member <PERSON><PERSON>", "attributes": {"senate_member_name": {"type": "SingleLineText", "name": "Senate Member Name", "actions": ["view"]}, "senate_district_name": {"type": "SingleLineText", "name": "Senate Member District Name", "actions": ["view"]}, "senate_web_url": {"type": "Url", "name": "Senate Member Web URL", "actions": ["view"]}}}, "council_member_details": {"key_name": "Council Member Details", "attributes": {"council_member_name": {"type": "SingleLineText", "name": "Council Member Name", "actions": ["view"]}, "council_district_name": {"type": "SingleLineText", "name": "Council Member District Name", "actions": ["view"]}, "council_web_url": {"type": "Url", "name": "Council Member Web URL", "actions": ["view"]}}}, "comptroller_member_details": {"key_name": "Comptroller Member Details", "attributes": {"comptroller_member_name": {"type": "SingleLineText", "name": "Comptroller Member Name", "actions": ["view"]}, "comptroller_web_url": {"type": "Url", "name": "Comptroller Member Web URL", "actions": ["view"]}}}, "executive_member_details": {"key_name": "Executive Member Details", "attributes": {"executive_member_name": {"type": "SingleLineText", "name": "Executive Member Name", "actions": ["view"]}, "executive_web_url": {"type": "Url", "name": "Executive Member Web URL", "actions": ["view"]}}}, "attorney_member_details": {"key_name": "District Attorney Member Details", "attributes": {"attorney_member_name": {"type": "SingleLineText", "name": "District Attorney Member Name", "actions": ["view"]}, "attorney_web_url": {"type": "Url", "name": "District Attorney Web URL", "actions": ["view"]}}}}}, "pacfs": {"key_name": "Dues Payments", "tabs": ["employee_pacfs"], "employee_pacfs": {"key_name": "Dues", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_pacfs?employee_id=[EMPLOYEE_ID]", "widget_type": "Analysis", "attributes": {"pacf_name": {"type": "DropDown", "name": "Status", "api": "pacfs", "api_key": "pacf_id", "required": true, "actions": ["view"]}, "date": {"type": "Date", "name": "Date", "actions": ["view"]}}}}, "meeting_types": {"key_name": "Meetings", "tabs": ["employee_meeting_types"], "employee_meeting_types": {"key_name": "Meetings", "request_type": "employee_meeting_type", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_meeting_types?employee_id=[EMPLOYEE_ID]", "widget_type": "Analysis", "attributes": {"meeting_type_name": {"type": "DropDown", "name": "Meeting Type", "api": "meeting_types", "api_key": "meeting_type_id", "required": true, "actions": ["view"]}, "meeting_date": {"type": "Date", "name": "Meeting Date", "actions": ["view"]}, "attended": {"type": "Radio", "name": "Attended", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view"]}}}}, "benefits": {"key_name": "Benefits", "tabs": ["employee_benefits", "beneficiaries"], "employee_benefits": {"key_name": "Benefits", "request_type": "employee_benefit", "empty_message": "There are no Benefits posted.", "disabled_message": "Visit our website to view information about your benefits https://www.utlo.org/benefits", "disabled": true, "actions": ["view"], "api": "employee_benefits?employee_id=[EMPLOYEE_ID]", "widget_type": "Action", "attributes": {"name": {"type": "SingleLineText", "name": "Type", "actions": ["view"]}, "action_items": [{"benefit_coverages": {"key_name": "Benefit Coverages", "request_type": "benefit_coverage", "required_key": "employee_benefit_id", "empty_message": "There aren't any Benefit Coverages.", "actions": ["view", "new", "edit"], "api": "benefit_coverages?employee_id=[EMPLOYEE_ID]&employee_benefit_id=[EMPLOYEE_BENEFIT_ID]", "widget_type": "Analysis", "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "required": true, "actions": ["view", "edit"]}, "expires_at": {"type": "Disabled", "name": "Expires at", "actions": ["view"]}}}}]}}, "beneficiaries": {"key_name": "Beneficiaries", "request_type": "beneficiary", "required_key": "beneficiary_type", "empty_message": "There are no Beneficiaries posted.", "actions": ["view", "new", "edit"], "api": "beneficiaries?employee_id=[EMPLOYEE_ID]", "widget_type": "Details", "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "beneficiary_type": {"type": "DropDown", "name": "Type", "required": true, "value": {"Primary": "Primary", "Secondary": "Secondary"}, "actions": ["view", "edit"]}, "percentage": {"type": "Number", "name": "Percentage", "required": true, "actions": ["view", "edit"]}, "file": {"type": "FileField", "name": "Upload", "selection_type": "single", "max_file_size": 10, "total_files": 1, "actions": ["view", "edit"]}}}}, "notification": {"key_name": "Notifications", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "to": "<EMAIL>", "subject": "UTLO: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "api": "employees/update_password"}}}