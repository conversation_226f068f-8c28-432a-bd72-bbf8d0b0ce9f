{"schema": {"contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"], "associated_model": {"benefit_coverages": ["name"], "beneficiaries": ["name"]}}}, "units": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "departments": {"key_name": "Mailing List Category", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"key_name": "Mailing List Category", "required_fields": ["department_id", "employee_id"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status"}, "devices": {"key_name": "Push notification"}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message", "change_request_notification": true, "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "officer_statuses": {"key_name": "Officer Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Delegate Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "age": "Age", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "Social Security Number", "social_security_number_format": "9", "veteran_status": "Veteran Status", "a_number": "A Number", "shield_number": "Shield Number", "placard_number": "Placard Number", "placard_multiple": true, "placard_customize": "Placard", "placard_number_years_customize": true, "start_date": "Start Date", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "allow_multiple_present_status": "false", "username": "Username", "app_downloaded": "App Downloaded", "required_fields": ["name", "address", "first_name", "last_name", "birthday", "city", "marital_status_id", "shield_number", "social_security_number", "state", "street", "zipcode", "gender_id"], "unique_fields": ["username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "placard_number", "street", "start_date", "birthday"], "associated_model": {"officer_statuses": ["name"], "benefit_coverages": ["name"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "officer_status_id", "start_date"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "employee_benefits": {"section_III": ["deceased", "transferred", "terminated", "resigned", "promoted_to_clerk", "opt_out", "no_longer_member", "bad_address"]}, "auto_expire_coverages": true, "required_fields": ["office_id", "employee_id", "start_date"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id", "start_date"]}, "delegate_employees": {"key_name": "Delegate Name"}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"]}, "firearm_range_scores": {"key_name": "Firearm Range Scores", "test_type": "Test type", "test_date": "Date", "score": "Score", "notes": "Notes", "required_fields": ["test_date", "test_type", "score", "employee_id"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "employee_status": {"section_III": ["Deceased"]}, "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "first_name": "First Name", "last_name": "Last Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "age": "Age", "effective_date": "Start Date", "expires_at": "Expiration", "coverage_expire_age": 23, "optical_coverage_expire_age": 23, "dental_coverage_expire_age": 23, "update_unexpire": true, "disabled_child_edit": true, "expire_relationship_types": ["child"], "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}], "required_fields": ["first_name", "last_name", "employee_id", "address", "employee_benefit_id", "birthday", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"], "is_new_year_addition": true}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_grievances": {"key_name": "Grievances", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["grievance_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "reports": {"single_employee": "Single Member", "benefit_coverage_name": "Dependent Name", "employee_name": "Member Name", "benefit_coverages": "Benefit Dependents", "relationship": "Relationship", "birthday": "DOB", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "dependents_not_found": "Dependents not found", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "officer_statuses_start_date": "Officer Status Start Date", "officer_statuses_start_date_from": "Officer Status Start From", "officer_statuses_start_date_to": "Union Status Start To", "show_disbursements": "Show Disbursements", "show_dependents": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "dependent_count": "Dependent Count", "show_coverages": "Show Dependents", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "app_downloaded": "App Downloaded", "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "common_terms": {"employee_analytics": "Analytics", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}}, "ui": {"user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}, "employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["placard_number"], "table_headers": ["name", "officer_status_name", "shield_number", "placard_number", "birthday", "start_date", "address"], "tabs": ["profile", "firearm_statuses", "employee_analytics", "benefits", "awards", "discipline_settings", "grievances", "meeting_types", "uploads", "legislative_addresses"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "only_two_placards": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "precinct", "do_not_mail", "birthday", "age", "social_security_number", "genders", "marital_statuses", "veteran_status", "a_number", "shield_number", "placard_number", "start_date", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_employment_statuses", "employee_officer_statuses", "employee_offices", "delegate_assignments", "employee_positions", "employee_ranks", "employees.app_downloaded"], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "employee_departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["date", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "firearm_statuses", "ranks", "employment_statuses", "officer_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "meeting_types", "genders", "units", "departments"]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "benefit_coverages", "sick_bank", "lodi", "union_meetings", "disciplines", "grievances", "employee_delegate_assignment"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employees.placard_number"], ["offices", "genders"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.app_downloaded"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "offices", "ranks", "employment_statuses", "officer_statuses", "reports.officer_statuses_start_date", "employees.email", "firearm_statuses", "marital_statuses", "positions", "employees.social_security_number", "employees.placard_customize", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "genders", "reports.dependent_count", "employees.app_downloaded"], "default_columns": ["employees", "employees.shield_number", "ranks", "employment_statuses", "offices", "firearm_statuses", "marital_statuses"], "actions": ["single_mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", "reports.show_dependents"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.shield_number", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees", "contact_persons"], ["offices", "ranks"], ["officer_statuses", "employment_statuses"], ["firearm_statuses", "positions"], ["marital_statuses", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["reports.officer_statuses_start_date_from", "reports.officer_statuses_start_date_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.placard_number"], ["employees.city", "employees.state"], ["employees.zipcode", "units"], ["departments", "employees.app_downloaded"], ["notifications.congress_district_id", "notifications.assembly_district_id"], ["notifications.senate_district_id", "notifications.council_district_id"]]}}, "mobile": {"social_media_feed": {"key_name": "Social Media Feed", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/social-media.png", "widget_type": "SocialMediaFeed", "web_view_script": "<script> if (typeof crypto.randomUUID !== 'function') { crypto.randomUUID = function () { const template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'; return template.replace(/[xy]/g, function (c) { const r = Math.random() * 16 | 0; const v = c === 'x' ? r : (r & 0x3 | 0x8); return v.toString(16); }); }; }  </script> <script src='https://static.elfsight.com/platform/platform.js' async></script> <div class='elfsight-app-7c80f5db-953e-41a4-a519-6ffd3ac27882' data-elfsight-app-lazy></div>", "header_text": "FOLLOW US ON SOCIAL MEDIA"}, "employees": {"key_name": "Profile", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/memberprofile.png", "tabs": ["profile", "contacts", "employee_employment_statuses", "employee_officer_statuses", "employee_offices", "delegate_assignments", "employee_positions", "employee_ranks", "legislation"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apartment", "required": true, "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "required": true, "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "required": true, "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "ZipCode", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "Date of Birth", "required": true, "actions": ["view", "edit"]}, "gender_name": {"type": "DropDown", "name": "Gender", "api": "genders", "api_key": "gender_id", "actions": ["view", "edit"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "veteran_status": {"type": "Radio", "name": "Veteran Status", "actions": ["view", "edit"]}, "shield_number": {"type": "SingleLineText", "name": "Shield Number", "actions": ["view", "edit"]}, "a_number": {"type": "SingleLineText", "name": "A Number", "actions": ["view", "edit"]}, "placard_number": {"type": "SingleLineText", "name": "Placard Number", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"work_phone": {"type": "PhoneNumber", "name": "Work", "contact_for": "work", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "home_phone": {"type": "PhoneNumber", "name": "Home", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_phone": {"type": "PhoneNumber", "name": "Personal", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"work_email": {"type": "Email", "name": "Work", "contact_for": "work", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}}, "employee_employment_statuses": {"key_name": "Employment Status", "request_type": "employee_employment_status", "empty_message": "No Data Available", "actions": ["view", "new"], "api": "employee_employment_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"employment_status_name": {"type": "DropDown", "name": "Employment Status", "required": true, "api": "employment_statuses", "api_key": "employment_status_id", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_officer_statuses": {"key_name": "Officer Status", "request_type": "employee_officer_status", "empty_message": "No Data Available", "actions": ["view", "new", "edit"], "api": "employee_officer_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"officer_status_name": {"type": "DropDown", "name": "Officer Status", "api": "officer_statuses", "api_key": "officer_status_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_offices": {"key_name": "Commands", "request_type": "employee_office", "empty_message": "No Data Available", "actions": ["view", "new", "edit"], "api": "employee_offices?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"office_name": {"type": "DropDown", "name": "Command", "api": "offices", "api_key": "office_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "delegate_assignments": {"key_name": "Delegate Assignments", "request_type": "delegate_assignment", "empty_message": "No Data Available", "actions": ["view", "new"], "api": "delegate_assignments?employee_id=[EMPLOYEE_ID]", "widget_type": "Assignments", "attributes": {"delegate_employee_name": {"type": "DropDown", "name": "Delegate Name", "api": "employees/delegate_employees", "api_key": "delegate_employee_id", "required": true, "actions": ["view", "edit"]}, "office_name": {"type": "DropDown", "name": "Command", "api": "offices", "api_key": "office_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "employee_positions": {"key_name": "Positions", "request_type": "employee_position", "empty_message": "No Data Available", "actions": ["view", "new"], "api": "employee_positions?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"position_name": {"type": "DropDown", "name": "Position", "api": "positions", "api_key": "position_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "employee_ranks": {"key_name": "Ranks", "request_type": "employee_rank", "empty_message": "No Data Available", "actions": ["view", "new", "edit"], "api": "employee_ranks?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"rank_name": {"type": "DropDown", "name": "Ranks", "api": "ranks", "api_key": "rank_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "legislation": {"key_name": "Legislation", "widget_type": "legislation", "empty_message": "No Legislative Details Found", "api": "legislative_addresses/employee_legislative_address?employee_id=[EMPLOYEE_ID]", "sub_sections": ["county_details", "congress_member_details", "assembly_member_details", "senate_member_details", "council_member_details", "comptroller_member_details", "executive_member_details", "attorney_member_details"], "county_details": {"key_name": "County Details", "attributes": {"county_name": {"type": "SingleLineText", "name": "County Name", "actions": ["view"]}}}, "congress_member_details": {"key_name": "Congress Member Det<PERSON>", "attributes": {"congress_member_name": {"type": "SingleLineText", "name": "Congress Member Name", "actions": ["view"]}, "congress_district_name": {"type": "SingleLineText", "name": "Congress Member District Name", "actions": ["view"]}, "congress_web_url": {"type": "Url", "name": "Congress Member Web URL", "actions": ["view"]}}}, "assembly_member_details": {"key_name": "Assembly Member Details", "attributes": {"assembly_member_name": {"type": "SingleLineText", "name": "Assembly Member Name", "actions": ["view"]}, "assembly_district_name": {"type": "SingleLineText", "name": "Assembly Member District Name", "actions": ["view"]}, "assembly_web_url": {"type": "Url", "name": "Assembly Member Web URL", "actions": ["view"]}}}, "senate_member_details": {"key_name": "Senate Member <PERSON><PERSON>", "attributes": {"senate_member_name": {"type": "SingleLineText", "name": "Senate Member Name", "actions": ["view"]}, "senate_district_name": {"type": "SingleLineText", "name": "Senate Member District Name", "actions": ["view"]}, "senate_web_url": {"type": "Url", "name": "Senate Member Web URL", "actions": ["view"]}}}, "council_member_details": {"key_name": "Council Member Details", "attributes": {"council_member_name": {"type": "SingleLineText", "name": "Council Member Name", "actions": ["view"]}, "council_district_name": {"type": "SingleLineText", "name": "Council Member District Name", "actions": ["view"]}, "council_web_url": {"type": "Url", "name": "Council Member Web URL", "actions": ["view"]}}}, "comptroller_member_details": {"key_name": "Comptroller Member Details", "attributes": {"comptroller_member_name": {"type": "SingleLineText", "name": "Comptroller Member Name", "actions": ["view"]}, "comptroller_web_url": {"type": "Url", "name": "Comptroller Member Web URL", "actions": ["view"]}}}, "executive_member_details": {"key_name": "Executive Member Details", "attributes": {"executive_member_name": {"type": "SingleLineText", "name": "Executive Member Name", "actions": ["view"]}, "executive_web_url": {"type": "Url", "name": "Executive Member Web URL", "actions": ["view"]}}}, "attorney_member_details": {"key_name": "District Attorney Member Details", "attributes": {"attorney_member_name": {"type": "SingleLineText", "name": "District Attorney Member Name", "actions": ["view"]}, "attorney_web_url": {"type": "Url", "name": "District Attorney Web URL", "actions": ["view"]}}}}}, "analytics": {"key_name": "Analytics", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/analysis.png", "tabs": ["sick_bank", "lodi"], "sick_bank": {"key_name": "Sick Bank", "empty_message": "There are no Sick Bank hours posted.", "actions": ["view"], "api": "leaves?employee_id=[EMPLOYEE_ID]&leave_type=sick", "widget_type": "Analysis", "header": {"title": "Sick Bank", "value_from": "API", "value_key": "analytics_total.sick"}, "attributes": {"started_at": {"type": "Date", "name": "From", "actions": ["view"]}, "ended_at": {"type": "Date", "name": "To", "actions": ["view"]}, "hours_used": {"type": "Number", "name": "Number of hours", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view"]}}}, "lodi": {"key_name": "<PERSON><PERSON>", "empty_message": "There are no LODI incidents posted.", "actions": ["view"], "api": "lodis?employee_id=[EMPLOYEE_ID]&lodi_type=lodi", "widget_type": "Analysis", "header": {"title": "<PERSON><PERSON>", "value_from": "API", "value_key": "analytics_total.lodis"}, "attributes": {"office_name": {"type": "DropDown", "name": "Location", "api": "offices", "api_key": "office_id", "actions": ["view"]}, "incident_date": {"type": "Date", "name": "Date of incident", "actions": ["view"]}, "return_date": {"type": "Date", "name": "Date of return", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view"]}}}}, "benefits": {"key_name": "Benefits", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/benefits.png", "tabs": ["employee_benefits", "beneficiaries"], "employee_benefits": {"key_name": "Benefits", "request_type": "employee_benefit", "empty_message": "There are no Benefits posted.", "actions": ["view"], "api": "employee_benefits?employee_id=[EMPLOYEE_ID]", "widget_type": "Action", "attributes": {"benefit_name": {"type": "DropDown", "name": "Type", "api": "benefits", "api_key": "benefit_id", "actions": ["view"]}, "start_date": {"type": "Date", "name": "From", "actions": ["view"]}, "end_date": {"type": "Date", "name": "To", "actions": ["view"]}, "description": {"type": "MultiLineText", "name": "Description", "actions": ["view"]}, "action_items": [{"benefit_disbursements": {"key_name": "Disbursement", "request_type": "benefit_disbursement", "empty_message": "There aren't any Benefit Disbursements.", "actions": ["view"], "api": "benefit_disbursements?employee_id=[EMPLOYEE_ID]&employee_benefit_id=[EMPLOYEE_BENEFIT_ID]", "widget_type": "Details", "header": {"title": "employee_benefits.benefit_name"}, "attributes": {"year": {"type": "Number", "name": "Year", "actions": ["view"]}, "date": {"type": "Date", "name": "Date", "actions": ["view"]}, "payment_type_name": {"type": "DropDown", "name": "Payment Type", "api": "payment_types", "api_key": "payment_type_id", "actions": ["view"]}, "reference_number": {"type": "SingleLineText", "name": "Reference Number", "actions": ["view"]}, "amount": {"type": "Number", "name": "Amount", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view"]}}}, "benefit_coverages": {"key_name": "Coverages", "request_type": "benefit_coverage", "required_key": "employee_benefit_id", "empty_message": "There aren't any Benefit Coverages.", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "For a change in Dependents to be valid and finalized please submit a marriage certificate or birth certificate to the union"}, "actions": ["view", "new", "edit"], "api": "benefit_coverages?employee_id=[EMPLOYEE_ID]&employee_benefit_id=[EMPLOYEE_BENEFIT_ID]", "widget_type": "Analysis", "header": {"title": "employee_benefits.benefit_name"}, "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "required": true, "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "required": true, "actions": ["view", "edit"]}, "expires_at": {"type": "Disabled", "name": "Expiration", "actions": ["view"]}}}}]}}, "beneficiaries": {"key_name": "Beneficiaries", "request_type": "beneficiary", "required_key": "beneficiary_type", "empty_message": "There are no Beneficiaries posted.", "actions": ["view", "new", "edit"], "api": "beneficiaries?employee_id=[EMPLOYEE_ID]", "widget_type": "Details", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "For a change in Beneficiary to be valid and finalized please submit a green enrollment card to the union"}, "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "beneficiary_type": {"type": "DropDown", "name": "Type", "required": true, "value": {"Primary": "Primary", "Secondary": "Secondary"}, "actions": ["view", "edit"]}, "percentage": {"type": "Number", "name": "Percentage", "required": true, "actions": ["view", "edit"]}, "file": {"type": "FileField", "name": "Upload", "selection_type": "single", "max_file_size": 10, "total_files": 1, "actions": ["view", "edit"]}}}}, "workers_comp": {"key_name": "P.C. Richard & Son Discount", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/pc_richard_and_son.png", "empty_message": "There are no P.C. Richard & Son Discount", "widget_type": "WebView", "web_view_url": "https://fuse-3d1d34-830ad806e32335fc4e04bf148e3.webflow.io/pc-richard-son", "actions": ["view"]}, "employee_meeting_types": {"key_name": "Union Meetings", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/meeting.png", "empty_message": "There are no Union Meetings posted.", "actions": ["view"], "api": "employee_meeting_types?employee_id=[EMPLOYEE_ID]", "widget_type": "Analysis", "attributes": {"meeting_type_name": {"type": "DropDown", "name": "Union Meetings Type", "api": "meeting_types", "api_key": "meeting_type_id", "actions": ["view"]}, "meeting_date": {"type": "Date", "name": "Date", "actions": ["view"]}, "attended": {"type": "Radio", "name": "Attended", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view"]}}}, "uploads": {"key_name": "Uploads", "empty_message": "No Uploads.", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/upload.png", "actions": ["view", "new"], "attributes": {"file": {"type": "FileField", "name": "File", "selection_type": "single", "max_file_size": 10, "total_files": 1, "required": true, "actions": ["view", "edit"]}, "date": {"type": "FromAPI", "name": "Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "notification": {"key_name": "Notifications", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/notification.png", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/contact_us.png", "to": "<EMAIL>", "subject": "NYSSCOA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/reset-password.png", "api": "employees/update_password"}}}