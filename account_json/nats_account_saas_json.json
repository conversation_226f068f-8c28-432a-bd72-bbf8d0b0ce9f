{"super_account": true, "schema": {"super_users": {"key_name": "Super Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "allowed_accounts": "Allowed Accounts", "required_fields": ["username", "email", "password", "password_confirmation", "allowed_accounts", "role_id"]}, "roles": {"key_name": "Roles"}}, "ui": {"super_users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "allowed_accounts", "roles"]}, "utlo_permitted_components_or_fields": {"hide_login_information": true, "profile": {"employees": ["name", "address", "shield_number"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_offices", "employee_employment_statuses", "employee_departments", "employee_sections", "employee_ranks"]}, "single_employee_report": {"columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employment_statuses", "employees.email", "employees.apartment", "employees.street", "employees.city", "employees.state", "employees.zipcode", "departments", "sections", "ranks", "offices"], "secondary_filters": [["employment_statuses", "offices"], ["departments", "sections"], ["ranks", ""]]}}}}