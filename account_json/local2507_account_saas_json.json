{"schema": {"contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Home Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "placard_number": "Placard Number", "placard_multiple": true, "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city", "placard_number"]}}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Home Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip", "birthday": "DOB", "age": "Age", "social_security_number": "SSN", "veteran_status": "Veteran Status", "responder_911": "9/11 Re<PERSON>onder", "placard_number": "Placard Number", "placard_multiple": true, "placard_number_odd_years_only": true, "a_number": "FDNY Reference #", "shield_number": "Shield #", "title_code": "License Plate #", "rdo": "Vehicle Make", "payroll_id": "Vehicle Year", "prescription": "DC 37 Reference #", "maiden_name": "Vehicle Color", "start_date": "City Start Date", "member_start_date": "Title Entry Date", "previous_shield_number": "Spouse Name", "mailing_address": "Mailing Address", "same_as_mailing_address": "Same as Mailing Address", "notes": "Notes", "cellphone": "Cell", "email": "Personal Email", "social_security_number_format": "4", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "username": "Username", "app_downloaded": "App Downloaded", "custom_messages": ["shield_number"], "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["username"], "custom_validations": {"integer_fields": ["shield_number"]}, "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "a_number", "shield_number", "placard_number", "street", "apartment", "city", "state", "zipcode"], "associated_model": {"department": ["name"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "required_fields": []}, "email_address": {"key_name": "Email address", "personal_email": "Personal", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}}, "mailing_address": {"key_name": "Mailing Address", "address": "Address", "apartment": "Apartment", "city": "City", "state": "State", "street": "Street", "zipcode": "Zipcode"}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employment_statuses": {"key_name": "Union Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "position_id"]}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "members": {"key_name": "Members"}, "platoons": {"key_name": "Class", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_units": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["unit_id", "employee_id"]}, "ranks": {"key_name": "Member Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "departments": {"key_name": "Work Location", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Division", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id"], "custom_validations": {"minimum_one_required_fields": ["section_id", "start_date", "end_date"]}}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "employee_id", "start_date"]}, "delegate_employees": {"key_name": "Delegate Name"}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_charges": {"key_name": "Discipline Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "charge": "Charge", "dan_number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "discipline_charge_id": "Status", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Grievances", "grievance_id": "Infraction", "grievance_status_id": "Status", "charge": "Charge", "number": "Case Number", "date": "Date", "filed_olr": "UOR/Aided", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "facilities": {"key_name": "Mailing List Category", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employee_facilities": {"key_name": "Mailing List Category", "start_date": "Start Date", "end_date": "End Date", "required_fields": ["facility_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Member", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meeting", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "delegates": "Delegates", "meetings": "Meetings", "case_and_abeyance": "Case and Abeyance", "abandonment_hearing": "Abandonment Hearing", "was_employee_pds": "Was Employee PDS", "ta_implemented": "TA implemented", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "filed_olr_from_date": "OLR From Date", "filed_olr_to_date": "OLR To Date", "step_1_from_date": "Step I From Date", "step_1_to_date": "Step I To Date", "step_2_from_date": "Step II From Date", "step_2_to_date": "Step II To Date", "step_3_from_date": "Step III From Date", "step_3_to_date": "Step III To Date", "arbritration_from_date": "Arbritration From Date", "arbritration_to_date": "Arbritration To Date", "oath_from_date": "Oath From Date", "oath_to_date": "Oath To Date", "win": "Win", "loss": "Loss", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "app_downloaded": "App Downloaded", "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "<PERSON>ath", "key": "oath"}, {"value": "Arbritration", "key": "arbritration"}]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "push": "false", "filter": "Filter", "sms_attachments": "SMS attachments", "sms_no_reply_text": "This is a no-reply text from Local 2507. Contact us if you need more information.", "change_request_notification": true, "default_email_signature": "<div><PERSON><PERSON><br>President<br>FDNY EMS Local 2507</div>", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "employee_analytics": {"key_name": "Analytics", "customization": true, "dashboard_stats": true, "required_tables": ["lodis", "assaults"]}, "lodis": {"key_name": "LODI", "incident_date": "Date of incident", "injury": "Injury Type", "return_date": "Date of return", "department_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["department_id", "employee_id", "incident_date"]}, "assaults": {"key_name": "ASSAULTS", "date": "Date of incident", "time": "Time", "location": "Location of Incident", "physical": "Physical Assault", "verbal": "Verbal Assault", "description": "Description of Incident", "incident_reported_to": "Who was the incident reported to?", "incident_report": "Incident Report", "lodi_pack": "LODI Package", "delegate": "Delegate", "required_fields": ["employee_id", "date"], "custom_validations": {"minimum_one_required_fields": ["date", "time", "incident_reported_to"]}}, "witnesses": {"name": "Name of Witness", "phone": "Phone number of Witness", "address": "Address of Witness", "required_fields": ["name"]}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "pacfs": {"key_name": "Payments", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "auto_dues_status": true, "required_fields": ["pacf_id", "employee_id"]}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "meeting_types": {"key_name": "Union Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "oath": "OATH", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration", "employee_analytics": "Analytics", "sick_bank": "Sick Bank"}, "change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status", "is_handling_name_from_json": true}, "devices": {"key_name": "Push notification"}, "forms": {"key_name": "Forms", "file": "Form", "name": "Form Name", "heading": "Heading"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"notes_timestamps": true, "notes_disabled_fields": ["notes", "description", "recommended_notes"], "employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["employees", "case", "grievance", "placard_number"], "employment_statuses_count": ["Active", "Inactive"], "table_headers": ["name", "shield_number", "a_number", "placard_number", "address", "departments"], "tabs": ["profile", "employee_analytics", "discipline_settings", "grievances", "pacfs", "awards", "meeting_types", "legislative_addresses", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["name", "address", "same_as_mailing_address", "mailing_address", "birthday", "age", "social_security_number", "marital_statuses", "previous_shield_number", "genders", "veteran_status", "responder_911", "placard_number", "a_number", "prescription", "shield_number", "title_code", "rdo", "payroll_id", "maiden_name", "start_date", "member_start_date", "platoons", "notes"], "contacts": [{"is_restrict_emergency_contact_details": true, "contact_number": ["personal_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_ranks", "employee_units", "employee_departments", "employee_sections", "employee_positions", "delegate_assignments", "employees.app_downloaded"], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}, "discipline_settings": {"status_search_field": true, "tabs": ["discipline_settings", "step_1", "oath", "step_2", "step_3", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "employee_facilities", "placard_number"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_facilities", "placard_number", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"], "is_filter": ["grievance"]}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "employment_statuses", "ranks", "facilities", "departments", "sections", "positions", "pacfs", "units", "discipline_settings", "grievances", "delegate_assignments", "meeting_types", "platoons", "discipline_charges", "grievance_statuses"]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "union_meetings", "employee_delegate_assignment", "lodi"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.shield_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "platoons"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.street", "employees.apartment", "employees.city", "employees.state", "employees.zipcode", "employees.social_security_number", "employees.a_number", "employees.shield_number", "units", "employment_statuses", "marital_statuses", "positions", "employees.email", "employees.birthday", "employees.cellphone", "departments", "sections", "pacfs", "employees.start_date", "employees.member_start_date", "genders", "platoons", "ranks", "facilities", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id", "employees.app_downloaded"], "default_columns": ["employees"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.oath_from_date", "reports.oath_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees", "contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.city", "employees.shield_number"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "platoons"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.settled_steps"]], "secondary_filters": [["employees", "contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.city", "employees.shield_number"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "platoons"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "report_columns": ["number"], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "arbritration"]}, "union_meetings": {"primary_filters": [["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees", "contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.city", "employees.shield_number"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "platoons"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees", "contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.city", "employees.shield_number"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "platoons"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees", "contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.city", "employees.shield_number"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees", "contact_persons"], ["sections", "departments"], ["employment_statuses", "ranks"], ["genders", "units"], ["positions", "marital_statuses"], ["employees.email", "employees.cellphone"], ["employees.a_number", "employees.social_security_number"], ["employees.city", "employees.shield_number"], ["employees.state", "employees.zipcode"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["facilities", "platoons"], ["notifications.congress_district_id", "notifications.assembly_district_id"], ["notifications.senate_district_id", "notifications.council_district_id"], ["employees.app_downloaded", ""]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}, "mobile": {"employees": {"key_name": "Profile", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/memberprofile.png", "welcome_message": {"header": "PRESIDENTS MESSAGE", "description": "WELCOME TO THE LOCAL 2507 MEMBER APP. THIS APP WAS CREATED BY THE EXECUTIVE BOARD TO BRING USEFUL RESOURCES TO OUR MEMBERS. THIS APP WILL ALSO BE USED TO COMMUNICATE WITH MEMBERS, SO <PERSON><PERSON><PERSON><PERSON> MAKE SURE NOTIFICATIONS ARE ENABLED. IF YOU HAVE ANY IDEAS FOR ADDITIONAL INFORMATION THAT CAN BE ADDED IN THE FUTURE, PLEASE EMAIL", "email": "<EMAIL>", "regards": "STAY SAFE,\nOREN BARZILAY", "button_name": "CLICK HERE TO ENTER"}, "tabs": ["profile", "contacts", "employee_ranks", "employee_departments", "employee_sections", "delegate_assignments", "legislation"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "suffix": {"type": "SingleLineText", "name": "Suffix", "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apartment", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "ZipCode", "actions": ["view", "edit"]}, "same_as_mailing_address": {"type": "CheckBox", "name": "Same as Mailing Address", "actions": ["edit"]}, "mailing_address_street": {"type": "SingleLineText", "name": "Mailing Address", "change_request_attribute": "mailing_address_attributes", "change_request_key": "street", "actions": ["view", "edit"]}, "mailing_address_apartment": {"type": "SingleLineText", "name": "Mailing Address Apt", "change_request_attribute": "mailing_address_attributes", "change_request_key": "apartment", "actions": ["view", "edit"]}, "mailing_address_city": {"type": "SingleLineText", "name": "Mailing Address City", "change_request_attribute": "mailing_address_attributes", "change_request_key": "city", "actions": ["view", "edit"]}, "mailing_address_state": {"type": "SingleLineText", "name": "Mailing Address State", "change_request_attribute": "mailing_address_attributes", "change_request_key": "state", "actions": ["view", "edit"]}, "mailing_address_zipcode": {"type": "ZipCode", "name": "Mailing Address Zip Code", "change_request_attribute": "mailing_address_attributes", "change_request_key": "zipcode", "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "required": true, "actions": ["view", "edit"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "veteran_status": {"type": "Radio", "name": "Veteran Status", "actions": ["view", "edit"]}, "responder_911": {"type": "Radio", "name": "9/11 Re<PERSON>onder", "actions": ["view", "edit"]}, "placard_number": {"type": "SingleLineText", "name": "Placard #", "actions": ["view"]}, "a_number": {"type": "SingleLineText", "name": "FDNY Reference #", "actions": ["view", "edit"]}, "prescription": {"type": "SingleLineText", "name": "DC 37 Reference #", "actions": ["view", "edit"]}, "title_code": {"type": "SingleLineText", "name": "License Plate #", "actions": ["view", "edit"]}, "rdo": {"type": "SingleLineText", "name": "Vehicle make", "actions": ["view", "edit"]}, "payroll_id": {"type": "SingleLineText", "name": "Vehicle Year", "actions": ["view", "edit"]}, "maiden_name": {"type": "SingleLineText", "name": "Vehicle Color", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "City Start Date", "actions": ["view"]}, "member_start_date": {"type": "Date", "name": "Title Entry Date", "actions": ["view"]}, "platoon_name": {"type": "DropDown", "name": "Class", "api": "platoons", "api_key": "platoon_id", "actions": ["view", "edit"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address", "emergency_contacts"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"personal_phone": {"type": "PhoneNumber", "name": "Mobile", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "emergency_contacts": {"key_name": "Emergency Contacts", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=emergency", "attributes": {"personal_emergency_name": {"type": "SingleLineText", "name": "Personal Emergency Contact Name", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "personal_emergency_phone": {"type": "PhoneNumber", "name": "Personal Emergency Contact Phone", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_emergency_relationship": {"type": "SingleLineText", "name": "Personal Emergency Contact Relationship", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}, "colleague_emergency_name": {"type": "SingleLineText", "name": "Colleague Emergency Contact Name", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "colleague_emergency_phone": {"type": "PhoneNumber", "name": "Colleague Emergency Contact Phone", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "colleague_emergency_relationship": {"type": "SingleLineText", "name": "Colleague Emergency Contact Relationship", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}}}}, "employee_ranks": {"key_name": "Member Titles", "request_type": "employee_rank", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_ranks?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"rank_name": {"type": "DropDown", "name": "Member Title", "api": "ranks", "api_key": "rank_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "employee_departments": {"key_name": "Work Location", "request_type": "employee_department", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_departments?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"department_name": {"type": "DropDown", "name": "Work Location", "api": "departments", "api_key": "department_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_sections": {"key_name": "Division", "request_type": "employee_section", "empty_message": "No Data Available", "actions": ["view", "edit", "new"], "api": "employee_sections?employee_id=[EMPLOYEE_ID]", "widget_type": "Assignments", "attributes": {"section_name": {"type": "DropDown", "name": "Division", "api": "sections", "api_key": "section_id", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}, "custom_validations": {"minimum_one_required_fields": ["section_id", "start_date", "end_date"]}}, "legislation": {"key_name": "Legislation", "widget_type": "legislation", "empty_message": "No Legislative Details Found", "api": "legislative_addresses/employee_legislative_address?employee_id=[EMPLOYEE_ID]", "sub_sections": ["county_details", "congress_member_details", "assembly_member_details", "senate_member_details", "council_member_details", "comptroller_member_details", "executive_member_details", "attorney_member_details"], "county_details": {"key_name": "County Details", "attributes": {"county_name": {"type": "SingleLineText", "name": "County Name", "actions": ["view"]}}}, "congress_member_details": {"key_name": "Congress Member Det<PERSON>", "attributes": {"congress_member_name": {"type": "SingleLineText", "name": "Congress Member Name", "actions": ["view"]}, "congress_district_name": {"type": "SingleLineText", "name": "Congress Member District Name", "actions": ["view"]}, "congress_web_url": {"type": "Url", "name": "Congress Member Web URL", "actions": ["view"]}}}, "assembly_member_details": {"key_name": "Assembly Member Details", "attributes": {"assembly_member_name": {"type": "SingleLineText", "name": "Assembly Member Name", "actions": ["view"]}, "assembly_district_name": {"type": "SingleLineText", "name": "Assembly Member District Name", "actions": ["view"]}, "assembly_web_url": {"type": "Url", "name": "Assembly Member Web URL", "actions": ["view"]}}}, "senate_member_details": {"key_name": "Senate Member <PERSON><PERSON>", "attributes": {"senate_member_name": {"type": "SingleLineText", "name": "Senate Member Name", "actions": ["view"]}, "senate_district_name": {"type": "SingleLineText", "name": "Senate Member District Name", "actions": ["view"]}, "senate_web_url": {"type": "Url", "name": "Senate Member Web URL", "actions": ["view"]}}}, "council_member_details": {"key_name": "Council Member Details", "attributes": {"council_member_name": {"type": "SingleLineText", "name": "Council Member Name", "actions": ["view"]}, "council_district_name": {"type": "SingleLineText", "name": "Council Member District Name", "actions": ["view"]}, "council_web_url": {"type": "Url", "name": "Council Member Web URL", "actions": ["view"]}}}, "comptroller_member_details": {"key_name": "Comptroller Member Details", "attributes": {"comptroller_member_name": {"type": "SingleLineText", "name": "Comptroller Member Name", "actions": ["view"]}, "comptroller_web_url": {"type": "Url", "name": "Comptroller Member Web URL", "actions": ["view"]}}}, "executive_member_details": {"key_name": "Executive Member Details", "attributes": {"executive_member_name": {"type": "SingleLineText", "name": "Executive Member Name", "actions": ["view"]}, "executive_web_url": {"type": "Url", "name": "Executive Member Web URL", "actions": ["view"]}}}, "attorney_member_details": {"key_name": "District Attorney Member Details", "attributes": {"attorney_member_name": {"type": "SingleLineText", "name": "District Attorney Member Name", "actions": ["view"]}, "attorney_web_url": {"type": "Url", "name": "District Attorney Web URL", "actions": ["view"]}}}}}, "analytics": {"key_name": "Assaults", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/analysis.png", "tabs": ["assaults"], "assaults": {"key_name": "Assaults", "group_by": "witnesses", "empty_message": "There are no Assaults posted.", "actions": ["view", "edit", "new"], "api": "assaults?employee_id=[EMPLOYEE_ID]", "widget_type": "Analysis", "request_type": "assault", "attributes": {"date": {"type": "Date", "name": "Date of incident", "required": true, "actions": ["view", "edit"]}, "time": {"type": "Time", "name": "Time", "actions": ["view", "edit"]}, "location": {"type": "SingleLineText", "name": "Location of Incident", "actions": ["view", "edit"]}, "physical": {"type": "CheckBox", "name": "Physical Assault", "actions": ["view", "edit"]}, "verbal": {"type": "CheckBox", "name": "Verbal Assault", "actions": ["view", "edit"]}, "description": {"type": "MultiLineText", "name": "Description", "actions": ["view", "edit"]}, "incident_reported_to": {"type": "SingleLineText", "name": "Who was the incident reported to?", "actions": ["view", "edit"]}, "incident_report": {"type": "CheckBox", "name": "Incident Report", "actions": ["view", "edit"]}, "lodi_pack": {"type": "CheckBox", "name": "LODI Package", "actions": ["view", "edit"]}, "delegate": {"type": "CheckBox", "name": "Delegate", "actions": ["view", "edit"]}}, "witnesses": {"key_name": "Witness", "empty_message": "There are no Witness posted.", "actions": ["view", "edit", "new"], "attributes": {"name": {"type": "SingleLineText", "name": "Name of Witness", "actions": ["view", "edit"]}, "phone": {"type": "PhoneNumber", "name": "Phone number of Witness", "actions": ["view", "edit"]}, "address": {"type": "MultiLineText", "name": "Address of Witness", "actions": ["view", "edit"]}}}}}, "forms": {"key_name": "Forms & Useful Resources", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/form.png", "empty_message": "There are no Forms.", "api": "forms?file_type=form", "widget_type": "Forms", "actions": ["view"], "attributes": {"file": {"type": "FileField", "name": "Form", "actions": ["view"]}, "name": {"type": "SingleLineText", "name": "Form Name", "actions": ["view"]}}}, "links": {"key_name": "Useful Links", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/link.png", "empty_message": "There are no Useful Links.", "api": "forms?file_type=useful_links", "widget_type": "Links", "actions": ["view"], "attributes": {"name": {"type": "SingleLineText", "name": "Name", "actions": ["view"]}, "link": {"type": "SingleLineText", "name": "Link", "actions": ["view"]}}}, "uploads": {"key_name": "Uploads", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/upload.png", "empty_message": "No Uploads", "api": "uploads?employee_id=[EMPLOYEE_ID]", "widget_type": "FileData", "actions": ["view", "new"], "attributes": {"file": {"type": "FileField", "name": "File", "selection_type": "single", "max_file_size": 10, "total_files": 1, "required": true, "actions": ["view", "edit"]}, "date": {"type": "FromAPI", "name": "Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "contracts": {"key_name": "Contract", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/local2507/contract.png", "empty_message": "There are no Contracts.", "api": "forms?file_type=salary_and_overtime_charts", "widget_type": "Forms", "actions": ["view"], "attributes": {"file": {"type": "FileField", "name": "Contract", "actions": ["view"]}, "name": {"type": "SingleLineText", "name": "Form Name", "actions": ["view"]}}}, "workers_comp": {"key_name": "Workers Comp", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/workers_comp_icon.png", "empty_message": "There are no Workers Comp.", "widget_type": "WebView", "web_view_url": "https://licomplaw.com/licomplaw-com-mdasr-ems-workers-compensation-landing-page/", "actions": ["view"]}, "notification": {"key_name": "Notifications", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/notification.png", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/contact_us.png", "to": "<EMAIL>", "subject": "LOCAL2507: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/reset-password.png", "api": "employees/update_password"}}}