{"schema": {"employees": {"key_name": "Students", "avatar": "Student Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apartment", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Email", "birthday": "DOB", "placard_number": "Student Identity Number", "shield_number": "University Board Number", "start_date": "Start Date", "notes": "Notes", "janus_card": "Course Opt-Out", "staff_member": "Alumni Member", "janus_card_opt_out_date": "Opt Out Date", "cellphone": "Personal Phone", "home_phone": "Home Phone", "email": "Email", "allow_multiple_present_status": "false", "required_fields": ["name", "address", "street", "city", "state", "zipcode", "birthday", "placard_number", "first_name", "last_name", "gender_id", "marital_status_id", "shield_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number"]}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Personal", "home_phone": "Home", "required_fields": ["home_phone"]}, "email_address": {"key_name": "Email address", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["department_id", "section_id", "title_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "awards": {"key_name": "Awards", "name": "Name", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_id": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "benefits": {"key_name": "Clubs", "name": "Name", "description": "Description", "required_fields": ["name"]}, "departments": {"key_name": "Departments", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sections", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "titles": {"key_name": "Grades", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["department_id", "section_id", "name", "title_code"]}, "employment_statuses": {"key_name": "Student Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Certificates", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Majors", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Lecturers", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["email", "username", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Student", "sick_bank": "Sick Bank", "janus": "Course", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "total_summary": "Total Summary", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "date_from": "Date From", "date_to": "Date To", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels"}, "common_terms": {"employee_analytics": "Leaves", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}}, "ui": {"employees": {"key_name": "Student List", "is_search": ["employees"], "table_headers": ["name", "placard_number", "shield_number", "birthday", "start_date", "address"], "tabs": ["profile", "employee_analytics", "awards", "discipline_settings", "benefits", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "birthday", "do_not_mail", "placard_number", "genders", "marital_statuses", "shield_number", "start_date", "notes"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_departments", "employee_sections", "employee_titles", "employee_positions", "employees.staff_member", "employees.janus_card", "employees.janus_card_opt_out_date"]}}, "settings": {"key_name": "Settings", "tabs": ["departments", "sections", "titles", "employment_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "genders", "benefits"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["disciplines", "single_employee", "sick_bank"], "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number", "employees.shield_number"], ["employment_statuses"], ["departments"], ["titles"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", ""]], "actions": ["pdf_report"]}, "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employees.placard_number"], ["employees.shield_number"], ["employment_statuses", "departments"], ["titles"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", ""]], "columns": ["employees", "titles", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.city", "employees.state", "employees.zipcode", "departments", "sections", "employees.start_date"], "default_columns": ["employees", "employees.shield_number", "titles", "employment_statuses", "marital_statuses", "employees.birthday"], "actions": ["single_mailing_label", "excel_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["employees.shield_number"], ["employment_statuses"], ["departments", "titles"], ["employees.home_phone", ""]], "actions": ["excel_report"]}}}}