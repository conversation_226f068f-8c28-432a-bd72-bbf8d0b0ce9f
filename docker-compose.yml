services:
  db:
    image: postgres:14.4
    volumes:
      - fuse_web_volume:/var/lib/postgresql/data
    working_dir: /var/lib/postgresql/data
    ports:
      - 5432:5432
    environment:
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: password

  webapp:
    build:
      dockerfile: Dockerfile
    volumes:
      - ./:/webapp
    ports:
      - 3001:3001
    env_file:
      - fuse_env_file.env
    #      - fuse_env_file.env
    depends_on:
      - db
    command: bash -c "rails s -p 3001 -b '0.0.0.0'"

  redis:
    image: redis:5-alpine
    command: redis-server
    ports:
      - 6379:6379
    volumes:
      - fuse_web_volume_redis:/data

  sidekiq:
    build: .
    depends_on:
      - db
      - redis
    command: bash -c "bundle exec sidekiq -c 1 -q mailer, 2 -q default -q mailers -q active_storage_analysis -q report -q notification -q active_storage_purge"
    env_file:
      - fuse_env_file.env
    volumes:
      - ./:/webapp
    environment:
      - REDIS_URL=redis://redis:6379/0

volumes:
  fuse_web_volume:
  fuse_web_volume_redis:



