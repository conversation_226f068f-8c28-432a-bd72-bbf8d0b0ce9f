class AddDiscardedToModels < ActiveRecord::Migration[5.2]
  disable_ddl_transaction!

  def change
    add_column :benefits, :discarded_at, :datetime
    add_index :benefits, :discarded_at, algorithm: :concurrently

    add_column :employees, :discarded_at, :datetime
    add_index :employees, :discarded_at, algorithm: :concurrently

    add_column :employment_statuses, :discarded_at, :datetime
    add_index :employment_statuses, :discarded_at, algorithm: :concurrently

    add_column :firearm_statuses, :discarded_at, :datetime
    add_index :firearm_statuses, :discarded_at, algorithm: :concurrently

    add_column :marital_statuses, :discarded_at, :datetime
    add_index :marital_statuses, :discarded_at, algorithm: :concurrently

    add_column :meeting_types, :discarded_at, :datetime
    add_index :meeting_types, :discarded_at, algorithm: :concurrently

    add_column :offices, :discarded_at, :datetime
    add_index :offices, :discarded_at, algorithm: :concurrently

    add_column :officer_statuses, :discarded_at, :datetime
    add_index :officer_statuses, :discarded_at, algorithm: :concurrently

    add_column :payment_types, :discarded_at, :datetime
    add_index :payment_types, :discarded_at, algorithm: :concurrently

    add_column :positions, :discarded_at, :datetime
    add_index :positions, :discarded_at, algorithm: :concurrently

    add_column :ranks, :discarded_at, :datetime
    add_index :ranks, :discarded_at, algorithm: :concurrently
  end
end
