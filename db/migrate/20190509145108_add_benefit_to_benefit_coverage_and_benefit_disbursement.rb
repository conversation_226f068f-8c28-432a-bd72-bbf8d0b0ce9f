class AddBenefitToBenefitCoverageAndBenefitDisbursement < ActiveRecord::Migration[5.2]
  disable_ddl_transaction!

  def change
    add_column :benefit_coverages, :benefit_id, :bigint
    add_column :benefit_disbursements, :benefit_id, :bigint
    add_index :benefit_coverages, :benefit_id, algorithm: :concurrently
    add_index :benefit_disbursements, :benefit_id, algorithm: :concurrently
    safety_assured { add_foreign_key :benefit_coverages, :benefits }
    safety_assured { add_foreign_key :benefit_disbursements, :benefits }
  end
end
