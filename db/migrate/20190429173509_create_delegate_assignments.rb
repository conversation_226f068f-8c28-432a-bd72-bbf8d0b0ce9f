class CreateDelegateAssignments < ActiveRecord::Migration[5.2]
  def change
    create_table :delegate_assignments do |t|
      t.references :employee, foreign_key: true
      t.references :office, foreign_key: true

      t.date :end_date
      t.date :start_date
      t.datetime :discarded_at, index: true
      
      t.string :delegate_name, default: '', null: false

      t.text :notes

      t.timestamps
    end
  end
end
