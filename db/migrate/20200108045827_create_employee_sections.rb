class CreateEmployeeSections < ActiveRecord::Migration[6.0]
  def change
    create_table :employee_sections do |t|
      t.references :employee, foreign_key: true
      t.references :section, foreign_key: true
      t.references :department, foreign_key: true

      t.string :name, null: false, default: ''
      t.string :phone, null: false, default: ''
      t.text :notes, null: false, default: ''
      t.datetime :discarded_at, index: true
      t.string :slug

      t.timestamps
    end
  end
end
