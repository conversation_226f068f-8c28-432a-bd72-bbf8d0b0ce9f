class EnablePgTrgmExtension < ActiveRecord::Migration[6.0]
  def up
    safety_assured do
      # execute "CREATE EXTENSION IF NOT EXISTS btree_gin;"
      # execute "CREATE EXTENSION IF NOT EXISTS btree_gist;"
      # execute "CREATE EXTENSION IF NOT EXISTS pg_trgm;"
    end
  end

  def down
    safety_assured do
      # execute "DROP EXTENSION IF EXISTS btree_gin;"
      # execute "DROP EXTENSION IF EXISTS btree_gist;"
      # execute "DROP EXTENSION IF EXISTS pg_trgm;"
    end
  end
end
