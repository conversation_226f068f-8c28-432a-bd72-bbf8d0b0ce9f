class CreateLodis < ActiveRecord::Migration[5.2]
  def change
    create_table :lodis do |t|
      t.references :employee, foreign_key: true

      t.date :incident_date 
      t.date :return_date
      t.datetime :discarded_at, index: true

      t.float :hours_used

      t.string :location, default: '', null: false
      t.string :return_to_work_status, default: '', null: false

      t.text :notes

      t.timestamps
    end
  end
end
