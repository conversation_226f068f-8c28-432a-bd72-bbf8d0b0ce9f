class UpdateGrievanceChargeReferenceToEmployeeGrievances < ActiveRecord::Migration[6.0]
  disable_ddl_transaction!

  def change
    safety_assured { remove_reference :employee_grievances, :grievance_charges, foreign_key: true,
                                      index: { algorithm: :concurrently } }

    add_reference :employee_grievances, :grievance_charge, foreign_key: true,
                  index: { algorithm: :concurrently }
  end
end
