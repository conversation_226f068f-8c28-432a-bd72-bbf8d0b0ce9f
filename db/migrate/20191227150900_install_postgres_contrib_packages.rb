class InstallPostgresContribPackages < ActiveRecord::Migration[5.2]
  def up
    safety_assured {
      execute "CREATE EXTENSION IF NOT EXISTS pg_trgm;"
      execute "CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;"
      execute "CREATE EXTENSION IF NOT EXISTS unaccent;"
    }
  end

  def down
    safety_assured {
      execute "DROP EXTENSION IF EXISTS pg_trgm;"
      execute "DROP EXTENSION IF EXISTS fuzzystrmatch;"
      execute "DROP EXTENSION IF EXISTS unaccent;"
    }
  end
end
