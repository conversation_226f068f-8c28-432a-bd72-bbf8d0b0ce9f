class CreateBounceAndComplaints < ActiveRecord::Migration[6.0]
  def change
    create_table :bounce_and_complaints do |t|
      t.references :employee, index: true
      t.string :email
      t.boolean :bounce, default: false
      t.string :notification_type
      t.string :bounce_type
      t.string :bounce_sub_type
      t.boolean :complaint, default: false
      t.string :message_id
      t.string :subject
      t.timestamp :timestamp

      t.timestamps
    end
  end
end
