# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `rails
# db:schema:load`. When creating a new database, `rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_06_17_050851) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "btree_gin"
  enable_extension "btree_gist"
  enable_extension "fuzzystrmatch"
  enable_extension "pg_stat_statements"
  enable_extension "pg_trgm"
  enable_extension "plpgsql"
  enable_extension "unaccent"

  create_table "accounts", force: :cascade do |t|
    t.string "name"
    t.string "subdomain"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.json "saas_json", default: {}
    t.string "domain_name"
    t.string "plivo_phone_number"
    t.string "plivo_auth_id"
    t.string "plivo_auth_token"
    t.text "accessible_accounts", default: [], array: true
    t.jsonb "app_version_details"
    t.string "ios_arn"
    t.string "android_arn"
    t.jsonb "user_audit_models", default: {}
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "affiliations", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_affiliations_on_discarded_at"
  end

  create_table "analytics_configurations", force: :cascade do |t|
    t.bigint "employee_id"
    t.integer "days_earned", default: 0
    t.text "notes", default: "", null: false
    t.string "analytics_type", default: "", null: false
    t.date "duration_from"
    t.date "duration_to"
    t.boolean "active", default: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "days_used", default: 0
    t.index ["discarded_at"], name: "index_analytics_configurations_on_discarded_at"
    t.index ["employee_id"], name: "index_analytics_configurations_on_employee_id"
  end

  create_table "assaults", force: :cascade do |t|
    t.boolean "physical"
    t.boolean "verbal"
    t.text "description"
    t.text "incident_reported_to"
    t.bigint "employee_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "date"
    t.string "location"
    t.boolean "incident_report"
    t.boolean "lodi_pack"
    t.boolean "delegate"
    t.time "time"
    t.datetime "cod_report_date"
    t.string "cod"
    t.string "inmate_name"
    t.string "bookcase"
    t.string "nysid"
    t.boolean "was_inmate_rearrested"
    t.text "charges"
    t.boolean "suspension"
    t.integer "suspension_days_count"
    t.string "who_affected_suspension"
    t.bigint "type_of_incident_id"
    t.bigint "office_id"
    t.index ["employee_id"], name: "index_assaults_on_employee_id"
  end

  create_table "awards", force: :cascade do |t|
    t.bigint "employee_id"
    t.date "awarded_on"
    t.datetime "discarded_at"
    t.string "name", default: "", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_awards_on_discarded_at"
    t.index ["employee_id"], name: "index_awards_on_employee_id"
  end

  create_table "beneficiaries", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "address", default: "", null: false
    t.string "name", default: "", null: false
    t.string "relationship", default: "", null: false
    t.datetime "discarded_at"
    t.float "percentage"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "beneficiary_type"
    t.string "phone"
    t.date "birthday"
    t.bigint "gender_id"
    t.string "social_security_number", default: ""
    t.index ["discarded_at"], name: "index_beneficiaries_on_discarded_at"
    t.index ["employee_id"], name: "index_beneficiaries_on_employee_id"
    t.index ["gender_id"], name: "index_beneficiaries_on_gender_id"
  end

  create_table "benefit_coverages", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "name", default: ""
    t.string "relationship", default: ""
    t.string "social_security_number", default: ""
    t.string "address", default: ""
    t.date "birthday"
    t.date "expires_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "employee_benefit_id"
    t.string "phone"
    t.integer "age"
    t.integer "dependent"
    t.date "effective_date"
    t.bigint "gender_id"
    t.boolean "unexpire", default: false
    t.string "first_name", default: ""
    t.string "last_name", default: ""
    t.string "suffix", default: ""
    t.integer "expiration_type", default: 0
    t.date "serviced_expiration"
    t.boolean "add_dependent_to_all_benefits", default: false
    t.string "semester"
    t.boolean "student"
    t.string "school_status"
    t.string "slug"
    t.integer "person_code"
    t.index ["discarded_at"], name: "index_benefit_coverages_on_discarded_at"
    t.index ["employee_benefit_id"], name: "index_benefit_coverages_on_employee_benefit_id"
    t.index ["employee_id"], name: "index_benefit_coverages_on_employee_id"
    t.index ["first_name"], name: "index_benefit_coverages_on_first_name_trgm", opclass: :gin_trgm_ops, using: :gin
    t.index ["gender_id"], name: "index_benefit_coverages_on_gender_id"
    t.index ["last_name"], name: "index_benefit_coverages_on_last_name_trgm", opclass: :gin_trgm_ops, using: :gin
    t.index ["name"], name: "index_benefit_coverages_on_name_trgm", opclass: :gin_trgm_ops, using: :gin
  end

  create_table "benefit_disbursements", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "payment_type_id"
    t.date "date"
    t.datetime "discarded_at"
    t.float "amount"
    t.integer "year"
    t.string "reference_number", default: "", null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "employee_benefit_id"
    t.string "relationship"
    t.bigint "benefit_coverage_id"
    t.boolean "is_member"
    t.date "entry_date"
    t.index ["benefit_coverage_id"], name: "index_benefit_disbursements_on_benefit_coverage_id"
    t.index ["discarded_at"], name: "index_benefit_disbursements_on_discarded_at"
    t.index ["employee_benefit_id"], name: "index_benefit_disbursements_on_employee_benefit_id"
    t.index ["employee_id"], name: "index_benefit_disbursements_on_employee_id"
    t.index ["payment_type_id"], name: "index_benefit_disbursements_on_payment_type_id"
  end

  create_table "benefits", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "slug"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_benefits_on_discarded_at"
    t.index ["slug"], name: "index_benefits_on_slug", unique: true
  end

  create_table "blacklisted_tokens", force: :cascade do |t|
    t.string "token"
    t.integer "expire_at"
    t.bigint "user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["user_id"], name: "index_blacklisted_tokens_on_user_id"
  end

  create_table "bounce_and_complaints", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "email"
    t.boolean "bounce", default: false
    t.string "notification_type"
    t.string "bounce_type"
    t.string "bounce_sub_type"
    t.boolean "complaint", default: false
    t.string "message_id"
    t.string "subject"
    t.datetime "timestamp"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["employee_id"], name: "index_bounce_and_complaints_on_employee_id"
  end

  create_table "change_request_uploads", force: :cascade do |t|
    t.bigint "change_request_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "timestamp"
    t.index ["change_request_id"], name: "index_change_request_uploads_on_change_request_id"
  end

  create_table "change_requests", force: :cascade do |t|
    t.bigint "employee_id"
    t.integer "request_type", default: 0
    t.integer "status", default: 0
    t.jsonb "requested_changes"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "approver_one", default: {}
    t.jsonb "approver_two", default: {}
    t.index ["employee_id"], name: "index_change_requests_on_employee_id"
  end

  create_table "contacts", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "contact_type"
    t.string "contact_for"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.string "value"
    t.string "contact_name"
    t.string "contact_relationship"
    t.index ["discarded_at"], name: "index_contacts_on_discarded_at"
    t.index ["employee_id"], name: "index_contacts_on_employee_id"
  end

  create_table "delegate_assignments", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "office_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.string "delegate_name", default: "", null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "delegate_employee_id"
    t.index ["delegate_employee_id"], name: "index_delegate_assignments_on_delegate_employee_id"
    t.index ["discarded_at"], name: "index_delegate_assignments_on_discarded_at"
    t.index ["employee_id"], name: "index_delegate_assignments_on_employee_id"
    t.index ["office_id"], name: "index_delegate_assignments_on_office_id"
  end

  create_table "delegate_series", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_delegate_series_on_discarded_at"
  end

  create_table "denied_reasons", force: :cascade do |t|
    t.string "name"
    t.text "insufficient_reason"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "departments", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.string "address", default: "", null: false
    t.string "phone", default: "", null: false
    t.text "notes", default: "", null: false
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_departments_on_discarded_at"
  end

  create_table "dependents", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "life_insurance_id"
    t.string "name", default: "", null: false
    t.string "relationship", default: "", null: false
    t.float "amount"
    t.string "address", default: "", null: false
    t.date "date"
    t.integer "age"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.float "spouse_contribution"
    t.index ["discarded_at"], name: "index_dependents_on_discarded_at"
    t.index ["employee_id"], name: "index_dependents_on_employee_id"
    t.index ["life_insurance_id"], name: "index_dependents_on_life_insurance_id"
  end

  create_table "devices", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "device_token"
    t.string "os_type"
    t.string "os_version"
    t.string "endpoint_arn"
    t.integer "status", default: 0
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_devices_on_discarded_at"
    t.index ["employee_id"], name: "index_devices_on_employee_id"
  end

  create_table "disabilities", force: :cascade do |t|
    t.date "from_date"
    t.date "to_date"
    t.date "last_working_date"
    t.date "last_paid_date"
    t.date "return_to_work"
    t.bigint "employee_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "duration"
    t.date "expires_at"
    t.boolean "auto_expire", default: true
    t.string "check_number"
    t.date "check_date"
    t.index ["discarded_at"], name: "index_disabilities_on_discarded_at"
    t.index ["employee_id"], name: "index_disabilities_on_employee_id"
  end

  create_table "discipline_charges", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_discipline_charges_on_discarded_at"
  end

  create_table "discipline_settings", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_discipline_settings_on_discarded_at"
  end

  create_table "discipline_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_discipline_statuses_on_discarded_at"
  end

  create_table "disciplines", force: :cascade do |t|
    t.bigint "employee_id"
    t.date "disciplined_on"
    t.datetime "discarded_at"
    t.string "name", default: "", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_disciplines_on_discarded_at"
    t.index ["employee_id"], name: "index_disciplines_on_employee_id"
  end

  create_table "employee_benefits", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "benefit_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "expiration_type", default: 0
    t.date "serviced_expiration"
    t.boolean "expire_all_benefits", default: false
    t.index ["benefit_id"], name: "index_employee_benefits_on_benefit_id"
    t.index ["discarded_at"], name: "index_employee_benefits_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_benefits_on_employee_id"
  end

  create_table "employee_departments", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "department_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["department_id"], name: "index_employee_departments_on_department_id"
    t.index ["discarded_at"], name: "index_employee_departments_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_departments_on_employee_id"
  end

  create_table "employee_discipline_settings", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "discipline_setting_id"
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "date"
    t.bigint "discipline_charge_id"
    t.string "dan_number", default: ""
    t.string "recommended_penalty", default: ""
    t.boolean "was_employee_pds", default: false
    t.boolean "case_and_abeyance", default: false
    t.boolean "ta_implemented", default: false
    t.boolean "abandonment_hearing", default: false
    t.datetime "filed_olr"
    t.string "charge"
    t.index ["discarded_at"], name: "index_employee_discipline_settings_on_discarded_at"
    t.index ["discipline_charge_id"], name: "index_employee_discipline_settings_on_discipline_charge_id"
    t.index ["discipline_setting_id"], name: "index_employee_discipline_settings_on_discipline_setting_id"
    t.index ["employee_id"], name: "index_employee_discipline_settings_on_employee_id"
  end

  create_table "employee_discipline_steps", force: :cascade do |t|
    t.datetime "date"
    t.text "recommended_notes"
    t.boolean "is_settled"
    t.integer "step"
    t.bigint "employee_discipline_setting_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.integer "discipline_status_id"
    t.boolean "is_pending"
    t.boolean "win"
    t.boolean "loss"
    t.boolean "hearing"
    t.index ["employee_discipline_setting_id"], name: "index_employee_discipline_steps_on_employee_discipline_setting"
  end

  create_table "employee_employment_statuses", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "employment_status_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_employee_employment_statuses_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_employment_statuses_on_employee_id"
    t.index ["employment_status_id"], name: "index_employee_employment_statuses_on_employment_status_id"
  end

  create_table "employee_facilities", force: :cascade do |t|
    t.date "start_date"
    t.date "end_date"
    t.datetime "discarded_at"
    t.bigint "employee_id", null: false
    t.bigint "facility_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_employee_facilities_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_facilities_on_employee_id"
    t.index ["facility_id"], name: "index_employee_facilities_on_facility_id"
  end

  create_table "employee_firearm_statuses", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "firearm_status_id"
    t.date "status_date"
    t.datetime "discarded_at"
    t.string "firearm_type", default: "", null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "amount"
    t.index ["employee_id"], name: "index_employee_firearm_statuses_on_employee_id"
    t.index ["firearm_status_id"], name: "index_employee_firearm_statuses_on_firearm_status_id"
  end

  create_table "employee_forms", force: :cascade do |t|
    t.date "date"
    t.string "name"
    t.string "reference"
    t.string "address"
    t.string "cell_phone"
    t.string "work_location"
    t.string "officers_present"
    t.string "charges_preferred"
    t.date "date_of_incident"
    t.string "discussion"
    t.string "disposition"
    t.boolean "accepted_by_member"
    t.text "non_acceptance_reason"
    t.boolean "emt"
    t.boolean "paramedic"
    t.boolean "fire_inspector"
    t.integer "type_of_work"
    t.string "title"
    t.string "work_phone"
    t.string "incident_location"
    t.integer "educational_qualification"
    t.time "work_start_time"
    t.string "delegate"
    t.time "available_time"
    t.integer "circle_one"
    t.boolean "lodi_package_filed"
    t.boolean "grievable_incident"
    t.jsonb "optional_text_fields"
    t.datetime "discarded_at"
    t.bigint "employee_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_employee_forms_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_forms_on_employee_id"
  end

  create_table "employee_grievance_steps", force: :cascade do |t|
    t.datetime "date"
    t.text "recommended_notes"
    t.boolean "is_settled"
    t.integer "step"
    t.bigint "employee_grievance_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.integer "grievance_status_id"
    t.boolean "is_pending"
    t.boolean "win"
    t.boolean "loss"
    t.string "olr"
    t.string "arbitration"
    t.string "decision"
    t.datetime "show_button_on"
    t.index ["employee_grievance_id"], name: "index_employee_grievance_steps_on_employee_grievance"
  end

  create_table "employee_grievances", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "grievance_id"
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "date"
    t.string "number", default: ""
    t.string "violation_alleged", default: ""
    t.datetime "filed_olr"
    t.string "charge"
    t.bigint "grievance_status_id"
    t.text "provisions"
    t.integer "status"
    t.integer "arbitrator_id"
    t.integer "pa_attorney_id"
    t.bigint "office_id"
    t.text "employee_ids", default: [], array: true
    t.index ["discarded_at"], name: "index_employee_grievances_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_grievances_on_employee_id"
    t.index ["grievance_id"], name: "index_employee_grievances_on_grievance_id"
    t.index ["grievance_status_id"], name: "index_employee_grievances_on_grievance_status_id"
    t.index ["office_id"], name: "index_employee_grievances_on_office_id"
  end

  create_table "employee_meeting_types", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "meeting_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.boolean "attended"
    t.date "meeting_date"
    t.text "notes"
    t.index ["discarded_at"], name: "index_employee_meeting_types_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_meeting_types_on_employee_id"
    t.index ["meeting_type_id"], name: "index_employee_meeting_types_on_meeting_type_id"
  end

  create_table "employee_officer_statuses", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "officer_status_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_employee_officer_statuses_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_officer_statuses_on_employee_id"
    t.index ["officer_status_id"], name: "index_employee_officer_statuses_on_officer_status_id"
  end

  create_table "employee_offices", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "office_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_employee_offices_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_offices_on_employee_id"
    t.index ["office_id"], name: "index_employee_offices_on_office_id"
  end

  create_table "employee_pacfs", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "pacf_id"
    t.date "date"
    t.datetime "discarded_at"
    t.text "notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "amount", precision: 10, scale: 2, default: "0.0"
    t.bigint "payment_type_id"
    t.decimal "gross_amount", precision: 10, scale: 2, default: "0.0"
    t.decimal "tax_amount", precision: 10, scale: 2, default: "0.0"
    t.string "payee"
    t.string "percentage"
    t.string "check_no"
    t.string "payee_ssn"
    t.boolean "received_application"
    t.boolean "check_void"
    t.string "check_valid"
    t.string "check_no_add"
    t.index ["discarded_at"], name: "index_employee_pacfs_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_pacfs_on_employee_id"
    t.index ["pacf_id"], name: "index_employee_pacfs_on_pacf_id"
    t.index ["payment_type_id"], name: "index_employee_pacfs_on_payment_type_id"
  end

  create_table "employee_positions", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "position_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "delegate_series_id"
    t.index ["delegate_series_id"], name: "index_employee_positions_on_delegate_series_id"
    t.index ["discarded_at"], name: "index_employee_positions_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_positions_on_employee_id"
    t.index ["position_id"], name: "index_employee_positions_on_position_id"
  end

  create_table "employee_ranks", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "rank_id"
    t.date "end_date"
    t.date "start_date"
    t.datetime "discarded_at"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_employee_ranks_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_ranks_on_employee_id"
    t.index ["rank_id"], name: "index_employee_ranks_on_rank_id"
  end

  create_table "employee_sections", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "section_id"
    t.bigint "department_id"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "start_date"
    t.date "end_date"
    t.index ["department_id"], name: "index_employee_sections_on_department_id"
    t.index ["discarded_at"], name: "index_employee_sections_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_sections_on_employee_id"
    t.index ["section_id"], name: "index_employee_sections_on_section_id"
  end

  create_table "employee_titles", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "section_id"
    t.bigint "department_id"
    t.bigint "title_id"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "start_date"
    t.date "end_date"
    t.text "notes"
    t.index ["department_id"], name: "index_employee_titles_on_department_id"
    t.index ["discarded_at"], name: "index_employee_titles_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_titles_on_employee_id"
    t.index ["section_id"], name: "index_employee_titles_on_section_id"
    t.index ["title_id"], name: "index_employee_titles_on_title_id"
  end

  create_table "employees", force: :cascade do |t|
    t.bigint "marital_status_id"
    t.date "birthday"
    t.string "first_name", default: "", null: false
    t.string "middle_name", default: "", null: false
    t.string "last_name", default: "", null: false
    t.string "city", default: "", null: false
    t.string "state", default: "", null: false
    t.string "zipcode", default: "", null: false
    t.string "social_security_number", default: "", null: false
    t.string "gender", default: "", null: false
    t.string "slug"
    t.text "notes", default: ""
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.string "apartment", default: ""
    t.string "street", default: ""
    t.boolean "veteran_status"
    t.string "shield_number", default: "", null: false
    t.string "a_number", default: "", null: false
    t.string "placard_number"
    t.date "start_date"
    t.date "member_start_date"
    t.string "title_code"
    t.date "prom_prov"
    t.date "prom_perm"
    t.bigint "gender_id"
    t.bigint "unit_id"
    t.date "member_since"
    t.boolean "staff_member", default: false
    t.boolean "janus_card", default: false
    t.boolean "do_not_mail", default: false
    t.date "janus_card_opt_out_date"
    t.integer "user_profile_id"
    t.boolean "email_opt_out", default: false
    t.boolean "sms_opt_out", default: false
    t.bigint "affiliation_id"
    t.bigint "tour_of_duty_id"
    t.string "rdo"
    t.string "payroll_id"
    t.date "longevity_date"
    t.bigint "platoon_id"
    t.date "ncc_date"
    t.date "leave_progression_date"
    t.string "primary_work_location"
    t.boolean "responder_911"
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.datetime "password_changed_at"
    t.string "username"
    t.integer "t_shirt_size"
    t.date "marital_status_date"
    t.string "suffix", default: "", null: false
    t.integer "category", default: 0
    t.boolean "enable_mobile_access", default: true
    t.bigint "department_id"
    t.string "forgot_password_otp"
    t.datetime "forgot_password_otp_send_at"
    t.string "prescription"
    t.string "maiden_name"
    t.string "previous_shield_number"
    t.boolean "same_as_mailing_address", default: false
    t.boolean "legislation_rake_update", default: false
    t.string "county"
    t.boolean "register_vote"
    t.boolean "app_downloaded", default: false
    t.integer "legislative_address_id"
    t.index ["affiliation_id"], name: "index_employees_on_affiliation_id"
    t.index ["department_id"], name: "index_employees_on_department_id"
    t.index ["discarded_at"], name: "index_employees_on_discarded_at"
    t.index ["first_name"], name: "index_employees_on_first_name_trgm", opclass: :gin_trgm_ops, using: :gin
    t.index ["gender_id"], name: "index_employees_on_gender_id"
    t.index ["last_name"], name: "index_employees_on_last_name_trgm", opclass: :gin_trgm_ops, using: :gin
    t.index ["marital_status_id"], name: "index_employees_on_marital_status_id"
    t.index ["middle_name"], name: "index_employees_on_middle_name_trgm", opclass: :gin_trgm_ops, using: :gin
    t.index ["platoon_id"], name: "index_employees_on_platoon_id"
    t.index ["reset_password_token"], name: "index_employees_on_reset_password_token", unique: true
    t.index ["slug"], name: "index_employees_on_slug", unique: true
    t.index ["tour_of_duty_id"], name: "index_employees_on_tour_of_duty_id"
    t.index ["unit_id"], name: "index_employees_on_unit_id"
    t.index ["user_profile_id"], name: "index_employees_on_user_profile_id"
  end

  create_table "employment_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_employment_statuses_on_discarded_at"
    t.index ["slug"], name: "index_employment_statuses_on_slug", unique: true
  end

  create_table "facilities", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.string "address", default: "", null: false
    t.string "phone", default: "", null: false
    t.string "fax", default: "", null: false
    t.string "slug"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "firearm_range_scores", force: :cascade do |t|
    t.bigint "employee_id"
    t.date "test_date"
    t.datetime "discarded_at"
    t.string "score", default: "", null: false
    t.string "test_type", default: "", null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_firearm_range_scores_on_discarded_at"
    t.index ["employee_id"], name: "index_firearm_range_scores_on_employee_id"
  end

  create_table "firearm_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.string "firearm_type", default: "", null: false
    t.string "firearm_test_type", default: "", null: false
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.text "description"
    t.index ["discarded_at"], name: "index_firearm_statuses_on_discarded_at"
    t.index ["slug"], name: "index_firearm_statuses_on_slug", unique: true
  end

  create_table "forms", force: :cascade do |t|
    t.text "description"
    t.integer "file_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "heading"
    t.string "name"
    t.string "number"
    t.text "address"
    t.text "link"
  end

  create_table "genders", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_genders_on_discarded_at"
  end

  create_table "grievance_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_grievance_statuses_on_discarded_at"
  end

  create_table "grievances", force: :cascade do |t|
    t.text "description", default: "", null: false
    t.string "name", default: "", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.index ["discarded_at"], name: "index_grievances_on_discarded_at"
  end

  create_table "hearings", force: :cascade do |t|
    t.bigint "employee_grievance_step_id", null: false
    t.date "hearing_date"
    t.text "notes"
    t.text "description"
    t.date "brief_date"
    t.text "brief"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["employee_grievance_step_id"], name: "index_hearings_on_employee_grievance_step_id"
  end

  create_table "leaves", force: :cascade do |t|
    t.bigint "employee_id"
    t.datetime "discarded_at"
    t.date "ended_at"
    t.date "started_at"
    t.integer "hours_used", default: 0
    t.string "leave_type", default: "", null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "days"
    t.boolean "cashed_out", default: false
    t.date "pay_period_ending"
    t.index ["discarded_at"], name: "index_leaves_on_discarded_at"
    t.index ["employee_id"], name: "index_leaves_on_employee_id"
  end

  create_table "legislative_addresses", force: :cascade do |t|
    t.string "street"
    t.string "city"
    t.string "state"
    t.string "zipcode"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "legislation_details"
  end

  create_table "life_insurances", force: :cascade do |t|
    t.bigint "employee_id"
    t.integer "insurance_type"
    t.float "amount"
    t.date "start_date"
    t.text "notes"
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_life_insurances_on_discarded_at"
    t.index ["employee_id"], name: "index_life_insurances_on_employee_id"
  end

  create_table "lodi_denied_reasons", id: false, force: :cascade do |t|
    t.bigint "lodi_id"
    t.bigint "denied_reason_id"
    t.index ["denied_reason_id"], name: "index_lodi_denied_reasons_on_denied_reason_id"
    t.index ["lodi_id"], name: "index_lodi_denied_reasons_on_lodi_id"
  end

  create_table "lodi_request_tabs", force: :cascade do |t|
    t.date "date"
    t.text "remarks"
    t.integer "request_type"
    t.integer "status"
    t.integer "reason"
    t.datetime "discarded_at"
    t.bigint "lodi_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["lodi_id"], name: "index_lodi_request_tabs_on_lodi"
  end

  create_table "lodis", force: :cascade do |t|
    t.bigint "employee_id"
    t.date "incident_date"
    t.date "return_date"
    t.datetime "discarded_at"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "office_id"
    t.string "injury"
    t.string "wcb"
    t.string "carrier_case"
    t.boolean "approved"
    t.boolean "denied"
    t.date "notified_date"

    t.string "lodi_type", default: "lodi", null: false
    t.index ["discarded_at"], name: "index_lodis_on_discarded_at"
    t.index ["employee_id"], name: "index_lodis_on_employee_id"
    t.index ["office_id"], name: "index_lodis_on_office_id"
  end

  create_table "mailing_addresses", force: :cascade do |t|
    t.string "apartment"
    t.string "city"
    t.string "state"
    t.string "street"
    t.integer "zipcode"
    t.datetime "discarded_at"
    t.bigint "employee_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_mailing_addresses_on_discarded_at"
    t.index ["employee_id"], name: "index_mailing_addresses_on_employee_id"
  end

  create_table "maillogs", force: :cascade do |t|
    t.date "date_received"
    t.string "received_from"
    t.string "entered_by"
    t.string "going_to"
    t.string "maillog_type"
    t.date "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "no_of_items"
  end

  create_table "marital_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_marital_statuses_on_discarded_at"
    t.index ["slug"], name: "index_marital_statuses_on_slug", unique: true
  end

  create_table "meeting_types", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_meeting_types_on_discarded_at"
    t.index ["slug"], name: "index_meeting_types_on_slug", unique: true
  end

  create_table "member_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_member_statuses_on_discarded_at"
  end

  create_table "notification_trackers", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "notification_id"
    t.boolean "email", default: false
    t.string "notification_type"
    t.boolean "sent", default: false
    t.boolean "delivered", default: false
    t.boolean "opened", default: false
    t.boolean "clicked", default: false
    t.boolean "failed", default: false
    t.boolean "rejected", default: false
    t.boolean "sms", default: false
    t.boolean "sms_sent", default: false
    t.boolean "sms_delivered", default: false
    t.boolean "sms_failed", default: false
    t.string "message_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "bounced", default: false
    t.boolean "push", default: false
    t.boolean "push_sent", default: false
    t.boolean "sms_undelivered"
    t.integer "sms_error_code"
    t.index ["employee_id"], name: "index_notification_trackers_on_employee_id"
    t.index ["message_id"], name: "index_notification_trackers_on_message_id"
    t.index ["notification_id"], name: "index_notification_trackers_on_notification_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.string "subject", default: "", null: false
    t.text "sms_message", default: "", null: false
    t.text "email_message", default: "", null: false
    t.boolean "sms", default: false
    t.boolean "email", default: false
    t.json "filters", default: ""
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "email_to"
    t.boolean "is_mms", default: false
    t.string "subdomain"
    t.boolean "push"
    t.text "push_message"
    t.integer "sms_to"
    t.boolean "change_request_notification", default: false
    t.bigint "user_id"
    t.boolean "is_scheduled", default: false
    t.date "scheduled_date"
    t.time "scheduled_time"
    t.string "job_id"
    t.datetime "discarded_at"
    t.integer "status", default: 0
    t.index ["discarded_at"], name: "index_notifications_on_discarded_at"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.integer "application_id"
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "scopes", default: "", null: false
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
  end

  create_table "oauth_access_tokens", force: :cascade do |t|
    t.bigint "resource_owner_id"
    t.integer "application_id"
    t.text "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.string "scopes"
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
  end

  create_table "oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "officer_statuses", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_officer_statuses_on_discarded_at"
    t.index ["slug"], name: "index_officer_statuses_on_slug", unique: true
  end

  create_table "offices", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.string "address", default: "", null: false
    t.string "phone", default: "", null: false
    t.string "fax", default: "", null: false
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_offices_on_discarded_at"
    t.index ["slug"], name: "index_offices_on_slug", unique: true
  end

  create_table "pacfs", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_pacfs_on_discarded_at"
  end

  create_table "payment_types", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_payment_types_on_discarded_at"
    t.index ["slug"], name: "index_payment_types_on_slug", unique: true
  end

  create_table "peshes", force: :cascade do |t|
    t.string "complaint"
    t.date "date"
    t.text "remarks"
    t.bigint "office_id"
    t.bigint "employee_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_peshes_on_discarded_at"
    t.index ["employee_id"], name: "index_peshes_on_employee_id"
    t.index ["office_id"], name: "index_peshes_on_office_id"
  end

  create_table "pg_search_documents", force: :cascade do |t|
    t.text "content"
    t.string "searchable_type"
    t.bigint "searchable_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["searchable_type", "searchable_id"], name: "index_pg_search_documents_on_searchable_type_and_searchable_id"
  end

  create_table "pghero_query_stats", force: :cascade do |t|
    t.text "database"
    t.text "user"
    t.text "query"
    t.bigint "query_hash"
    t.float "total_time"
    t.bigint "calls"
    t.datetime "captured_at"
    t.index ["database", "captured_at"], name: "index_pghero_query_stats_on_database_and_captured_at"
  end

  create_table "platoons", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_platoons_on_discarded_at"
  end

  create_table "poly_notes", force: :cascade do |t|
    t.text "notes"
    t.string "notable_type", null: false
    t.bigint "notable_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "user_id"
    t.index ["notable_type", "notable_id"], name: "index_poly_notes_on_notable_type_and_notable_id"
    t.index ["user_id"], name: "index_poly_notes_on_user_id"
  end

  create_table "positions", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_positions_on_discarded_at"
    t.index ["slug"], name: "index_positions_on_slug", unique: true
  end

  create_table "ranks", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.boolean "ignore_rank", default: false
    t.index ["discarded_at"], name: "index_ranks_on_discarded_at"
    t.index ["slug"], name: "index_ranks_on_slug", unique: true
  end

  create_table "reminder_trackers", force: :cascade do |t|
    t.bigint "reminder_id", null: false
    t.bigint "user_id", null: false
    t.integer "email_delivery_status", default: 0
    t.integer "status", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "web_unread_time"
    t.datetime "discarded_at"
    t.index ["reminder_id"], name: "index_reminder_trackers_on_reminder_id"
    t.index ["user_id"], name: "index_reminder_trackers_on_user_id"
  end

  create_table "reminders", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.bigint "user_id", null: false
    t.datetime "reminder_start_date"
    t.datetime "reminder_end_date"
    t.time "time"
    t.text "schedule"
    t.jsonb "repeat"
    t.string "job_id"
    t.datetime "discarded_at"
    t.integer "status", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "admin_only", default: false
    t.bigint "employee_grievance_id"
    t.index ["employee_grievance_id"], name: "index_reminders_on_employee_grievance_id"
    t.boolean "admin_only", default: false
    t.bigint "employee_grievance_id"
    t.index ["employee_grievance_id"], name: "index_reminders_on_employee_grievance_id"
    t.index ["user_id"], name: "index_reminders_on_user_id"
  end

  create_table "reminders_users", id: false, force: :cascade do |t|
    t.bigint "reminder_id", null: false
    t.bigint "user_id", null: false
    t.index ["reminder_id", "user_id"], name: "index_reminders_users_on_reminder_id_and_user_id"
    t.index ["user_id"], name: "index_reminders_users_on_user_id"
  end

  create_table "reports", force: :cascade do |t|
    t.string "report_type", default: "", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "params"
    t.string "format", default: "", null: false
  end

  create_table "rights", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "rights_type"
    t.index ["discarded_at"], name: "index_rights_on_discarded_at"
  end

  create_table "rights_roles", id: false, force: :cascade do |t|
    t.bigint "role_id"
    t.bigint "right_id"
    t.index ["right_id"], name: "index_rights_roles_on_right_id"
    t.index ["role_id"], name: "index_rights_roles_on_role_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_roles_on_discarded_at"
  end

  create_table "sections", force: :cascade do |t|
    t.bigint "department_id"
    t.string "name", default: "", null: false
    t.string "phone", default: "", null: false
    t.text "notes", default: "", null: false
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "discarded_at"
    t.index ["department_id"], name: "index_sections_on_department_id"
    t.index ["discarded_at"], name: "index_sections_on_discarded_at"
  end

  create_table "test", id: false, force: :cascade do |t|
    t.integer "id", null: false
  end

  create_table "test1", id: false, force: :cascade do |t|
    t.integer "id", null: false
    t.string "name", limit: 50
  end

  create_table "titles", force: :cascade do |t|
    t.bigint "department_id"
    t.bigint "section_id"
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "title_code", default: ""
    t.index ["department_id"], name: "index_titles_on_department_id"
    t.index ["discarded_at"], name: "index_titles_on_discarded_at"
    t.index ["section_id"], name: "index_titles_on_section_id"
  end

  create_table "totalities", force: :cascade do |t|
    t.bigint "employee_id"
    t.datetime "discarded_at"
    t.float "value", default: 0.0
    t.string "totalable_type", default: "", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_totalities_on_discarded_at"
    t.index ["employee_id"], name: "index_totalities_on_employee_id"
  end

  create_table "tour_of_duties", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_tour_of_duties_on_discarded_at"
  end

  create_table "units", force: :cascade do |t|
    t.string "name", default: "", null: false
    t.text "description", default: "", null: false
    t.datetime "discarded_at"
    t.string "slug"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["discarded_at"], name: "index_units_on_discarded_at"
  end

  create_table "uploads", force: :cascade do |t|
    t.bigint "employee_id"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_uploads_on_employee_id"
  end

  create_table "user_contacts", force: :cascade do |t|
    t.bigint "user_id"
    t.string "contact_type"
    t.string "contact_for"
    t.string "value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_contacts_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "username"
    t.string "first_name"
    t.string "last_name"
    t.boolean "notification"
    t.string "access_role"
    t.string "apartment"
    t.string "street"
    t.string "city"
    t.string "state"
    t.string "zipcode"
    t.date "birthday"
    t.bigint "role_id"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.datetime "password_changed_at"
    t.text "allowed_accounts", default: [], array: true
    t.boolean "user_audit_logging", default: true
    t.boolean "restrict_login_out_of_office", default: false
    t.integer "restrict_type"
    t.time "allow_login_from_time"
    t.time "allow_login_to_time"
    t.index ["email"], name: "conditional_index_users_on_email", unique: true, where: "(discarded_at IS NULL)"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["role_id"], name: "index_users_on_role_id"
    t.index ["username"], name: "conditional_index_users_on_username", unique: true, where: "(discarded_at IS NULL)"
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object"
    t.datetime "created_at"
    t.text "object_changes"
    t.integer "employee_id"
    t.text "attachment_name"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_versions_on_discarded_at"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
    t.index ["item_type", "whodunnit"], name: "index_versions_on_item_type_and_whodunnit"
    t.index ["whodunnit"], name: "index_versions_on_whodunnit"
  end

  create_table "witnesses", force: :cascade do |t|
    t.text "name"
    t.text "phone"
    t.text "address"
    t.bigint "assault_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["assault_id"], name: "index_witnesses_on_assault_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "analytics_configurations", "employees"
  add_foreign_key "assaults", "employees"
  add_foreign_key "awards", "employees"
  add_foreign_key "beneficiaries", "employees"
  add_foreign_key "beneficiaries", "genders"
  add_foreign_key "benefit_coverages", "employee_benefits"
  add_foreign_key "benefit_coverages", "employees"
  add_foreign_key "benefit_coverages", "genders"
  add_foreign_key "benefit_disbursements", "benefit_coverages"
  add_foreign_key "benefit_disbursements", "employee_benefits"
  add_foreign_key "benefit_disbursements", "employees"
  add_foreign_key "benefit_disbursements", "payment_types"
  add_foreign_key "blacklisted_tokens", "users"
  add_foreign_key "change_request_uploads", "change_requests"
  add_foreign_key "contacts", "employees"
  add_foreign_key "delegate_assignments", "employees"
  add_foreign_key "delegate_assignments", "offices"
  add_foreign_key "dependents", "employees"
  add_foreign_key "dependents", "life_insurances"
  add_foreign_key "devices", "employees"
  add_foreign_key "disabilities", "employees"
  add_foreign_key "disciplines", "employees"
  add_foreign_key "employee_benefits", "benefits"
  add_foreign_key "employee_benefits", "employees"
  add_foreign_key "employee_departments", "departments"
  add_foreign_key "employee_departments", "employees"
  add_foreign_key "employee_discipline_settings", "discipline_charges"
  add_foreign_key "employee_discipline_settings", "discipline_settings"
  add_foreign_key "employee_discipline_settings", "employees"
  add_foreign_key "employee_discipline_steps", "employee_discipline_settings"
  add_foreign_key "employee_employment_statuses", "employees"
  add_foreign_key "employee_employment_statuses", "employment_statuses"
  add_foreign_key "employee_facilities", "employees"
  add_foreign_key "employee_facilities", "facilities"
  add_foreign_key "employee_firearm_statuses", "employees"
  add_foreign_key "employee_firearm_statuses", "firearm_statuses"
  add_foreign_key "employee_forms", "employees"
  add_foreign_key "employee_grievance_steps", "employee_grievances"
  add_foreign_key "employee_grievances", "employees"
  add_foreign_key "employee_grievances", "grievances"
  add_foreign_key "employee_grievances", "offices"
  add_foreign_key "employee_meeting_types", "employees"
  add_foreign_key "employee_meeting_types", "meeting_types"
  add_foreign_key "employee_officer_statuses", "employees"
  add_foreign_key "employee_officer_statuses", "officer_statuses"
  add_foreign_key "employee_offices", "employees"
  add_foreign_key "employee_offices", "offices"
  add_foreign_key "employee_pacfs", "employees"
  add_foreign_key "employee_pacfs", "pacfs"
  add_foreign_key "employee_pacfs", "payment_types"
  add_foreign_key "employee_positions", "delegate_series"
  add_foreign_key "employee_positions", "employees"
  add_foreign_key "employee_positions", "positions"
  add_foreign_key "employee_ranks", "employees"
  add_foreign_key "employee_ranks", "ranks"
  add_foreign_key "employee_sections", "departments"
  add_foreign_key "employee_sections", "employees"
  add_foreign_key "employee_sections", "sections"
  add_foreign_key "employee_titles", "departments"
  add_foreign_key "employee_titles", "employees"
  add_foreign_key "employee_titles", "sections"
  add_foreign_key "employee_titles", "titles"
  add_foreign_key "employees", "affiliations"
  add_foreign_key "employees", "departments"
  add_foreign_key "employees", "genders"
  add_foreign_key "employees", "marital_statuses"
  add_foreign_key "employees", "platoons"
  add_foreign_key "employees", "tour_of_duties"
  add_foreign_key "employees", "units"
  add_foreign_key "firearm_range_scores", "employees"
  add_foreign_key "hearings", "employee_grievance_steps"
  add_foreign_key "leaves", "employees"
  add_foreign_key "life_insurances", "employees"
  add_foreign_key "lodi_denied_reasons", "denied_reasons"
  add_foreign_key "lodi_denied_reasons", "lodis"
  add_foreign_key "lodi_request_tabs", "lodis"
  add_foreign_key "lodis", "employees"
  add_foreign_key "lodis", "offices"
  add_foreign_key "mailing_addresses", "employees"
  add_foreign_key "notifications", "users"
  add_foreign_key "oauth_access_tokens", "employees", column: "resource_owner_id"
  add_foreign_key "peshes", "employees"
  add_foreign_key "peshes", "offices"
  add_foreign_key "poly_notes", "users"
  add_foreign_key "reminder_trackers", "reminders"
  add_foreign_key "reminder_trackers", "users"
  add_foreign_key "reminders", "employee_grievances"
  add_foreign_key "reminders", "users"
  add_foreign_key "reminders_users", "reminders"
  add_foreign_key "reminders_users", "users"
  add_foreign_key "rights_roles", "rights"
  add_foreign_key "rights_roles", "roles"
  add_foreign_key "sections", "departments"
  add_foreign_key "titles", "departments"
  add_foreign_key "titles", "sections"
  add_foreign_key "totalities", "employees"
  add_foreign_key "uploads", "employees"
  add_foreign_key "user_contacts", "users"
  add_foreign_key "users", "roles"
  add_foreign_key "witnesses", "assaults"
end
