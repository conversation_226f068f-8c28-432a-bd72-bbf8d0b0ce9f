{"schema": {"change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status", "is_handling_name_from_json": true}, "life_insurances": {"key_name": "Life Insurance", "insurance_type": "Type", "amount": "Amount", "start_date": "As of Date", "age": "Age", "notes": "Notes", "files": "Uploads", "member_contributions": "Member Contribution", "age_group_type": "Age Group", "required_fields": ["insurance_type", "employee_id", "amount", "start_date"], "member_contribution": [{"low": 1, "high": 29, "amount": {"50000": 1.2, "100000": 2.4, "200000": 4.8}}, {"low": 30, "high": 34, "amount": {"50000": 1.37, "100000": 2.74, "200000": 5.47}}, {"low": 35, "high": 39, "amount": {"50000": 1.68, "100000": 3.36, "200000": 6.72}}, {"low": 40, "high": 44, "amount": {"50000": 2.4, "100000": 4.8, "200000": 9.6}}, {"low": 45, "high": 49, "amount": {"50000": 3.6, "100000": 7.2, "200000": 14.4}}, {"low": 50, "high": 54, "amount": {"50000": 5.52, "100000": 11.04, "200000": 22.08}}, {"low": 55, "high": 59, "amount": {"50000": 9.96, "100000": 19.92, "200000": 39.84}}, {"low": 60, "high": 64, "amount": {"50000": 15.05, "100000": 30.1, "200000": 60.2}}, {"low": 65, "high": 69, "amount": {"50000": 25.6, "100000": 51.22, "200000": 102.43}}, {"low": 70, "high": 99, "amount": {"50000": 48.38, "100000": 96.77, "200000": 193.53}}], "life_insurances_premium": {"key_name": "Premium", "member_contribution": "Member Contribution", "total_contribution": "Total Contribution"}, "insurance_amount": {"supplemental": [50000, 100000, 200000], "basic": [15000]}, "insurance_types": [{"value": "Basic", "key": "basic"}, {"value": "Supplemental", "key": "supplemental"}]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"], "benefits_name": ["Optical", "Dental", "Prescription"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "push_message": "Push Message", "push": "false", "filter": "Filter"}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "officer_statuses": {"key_name": "Officer Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Units", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "Social Security Number", "veteran_status": "Veteran", "a_number": "Pass Number", "shield_number": "BSC Number", "placard_number": "Placard Number", "start_date": "TA Start Date", "prom_prov": "Prom Prov", "prom_perm": "Prom Perm", "member_since": "Member Since", "notes": "Notes", "janus_card": "<PERSON><PERSON>t Out", "staff_member": "Staff Member", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "cellphone": "Cell Phone", "home_phone": "Home Phone", "work_phone": "Work Phone", "email": "Email", "social_security_number_format": "4", "allow_multiple_present_status": "true", "allow_multiple_delegate_assignments": "true", "visible_social_security_number": true, "safeguard_notes_alert": true, "ssn_unique_search": true, "payroll_auto_align": true, "member_id_autoload": true, "app_downloaded": "App Downloaded", "required_fields": ["name", "first_name", "last_name", "a_number", "shield_number", "unit_id"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "a_number", "member_since"]}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"]}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["department_id", "section_id", "title_id", "employee_id"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "officer_status_id", "start_date"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "peshes": {"key_name": "PESH", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "date": "Date", "remarks": "Remarks", "office_id": "Command", "files": "Files", "required_fields": ["employee_id"], "custom_validations": {"minimum_one_required_fields": ["office_id", "complaint", "date", "remarks"]}}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id", "start_date"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "files": "Uploads", "additional_field": ["prescription", "maiden_name", "previous_shield_number"], "customization": true, "required_fields": ["firearm_status_id", "employee_id"], "required_tables": ["firearm_statuses"]}, "firearm_range_scores": {"key_name": "Firearm Range Scores", "test_type": "Test type", "test_date": "Date", "score": "Score", "notes": "Notes", "required_fields": ["test_date", "test_type", "score", "employee_id"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "serviced_expiration": "Serviced Expiration", "description": "Description", "benefit_customization": true, "auto_expire_benefits": true, "employee_status": {"section_I": ["Active", "Retired"], "section_II": ["Cobra Active", "Cobra Retiree"], "section_III": ["Deceased", "Terminated", "Inactive", "Promoted", "Resigned", "Bad Standing", "Provisional Sgt"]}, "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone Number", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"], "required_auto_fields": ["address"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "first_name": "First Name", "last_name": "Last Name", "suffix": "Suffix", "gender_id": "Gender", "relationship": "Relationship", "dependent": "Dependent", "social_security_number": "employees.social_security_number", "address": "Address", "phone": "Phone Number", "birthday": "DOB", "age": "Age", "effective_date": "Effective Date", "expires_at": "Expiration", "serviced_expiration": "Serviced Expiration", "marital_status_divorced": true, "auto_dependent_code": true, "disabled_child_edit": true, "coverage_expire_age": 26, "optical_coverage_expire_age": 19, "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "first_name", "last_name", "social_security_number", "relationship"], "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Step Child", "key": "step_child"}, {"value": "Disabled <PERSON> Child", "key": "disabled_step_child"}]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "relationship": "Relationship", "benefit_coverage_id": "Person Serviced", "reference_number": "Reference Number", "optical_coverage_expiration": true, "calendar_year_expiration": false, "amount": "Amount", "notes": "Notes", "order_by_date": true, "required_fields": ["employee_id", "employee_benefit_id", "date"], "relationship_value": [{"value": "Self", "key": "self"}, {"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Penalty Imposed", "charge": "Charge", "dan_number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Class Action Grievance", "charge": "Charge", "number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "grievance_status_id": "Status", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "meeting_types": {"key_name": "Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "departments": {"key_name": "Departments", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sections", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "pacfs": {"key_name": "Annuity", "name": "Name", "description": "Description", "hide_status": true, "required_fields": ["name"]}, "titles": {"key_name": "Titles", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["department_id", "section_id", "name", "title_code"]}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "platoons": {"key_name": "Membership Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "reports": {"single_employee": "Single Member", "life_insurances": "Life Insurances", "benefit_coverage_name": "Dependent Name", "benefit_coverages": "Benefit Dependents", "workers_comp": "Workers Comp", "age_group_type": "Type", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "beneficiary": "Beneficiary", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "show_dependents": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "app_downloaded": "App Downloaded", "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "common_terms": {"workers_comp": "Workers Comp", "life_insurances": "Life Insurances", "employee_analytics": "Analytics", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}, "maillogs": {"key_name": "Maillogs", "date_received": "Date Received", "received_from": "Received From", "entered_by": "Entered By", "going_to": "Going to", "maillog_type": "Type of mail", "type_options": ["Certified Mail", "Return Receipt", "Letter", "Package"], "required_fields": ["date_received"]}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "spouse_contribution": "Spouse Contribution", "relationship_amount_validation": {"basic": {"spouse": [15000], "child": [5000]}, "supplemental": {"spouse": [25000, 50000, 100000]}}, "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "address": "Address", "date": "DOB", "age": "Age", "required_fields": ["employee_id", "name", "relationship", "amount"], "spouse_contribution_value": {"amount": {"25000": 1.82, "50000": 3.63, "100000": 7.26, "1000000": 999.99}}, "dependents_premium": {"key_name": "Premium", "spouse_contribution": "Spouse Contribution", "total_contribution": "Total Contribution"}}, "devices": {"key_name": "Push notification"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "employment_statuses_count": ["ACTIVE", "COBRA", "Retired", "DISABILITY RETIRED"], "is_filter": ["employees", "grievance", "ssn"], "table_headers": ["name", "social_security_number", "a_number", "shield_number", "employment_status_name"], "tabs": ["profile", "pacfs", "firearm_statuses", "employee_analytics", "benefits", "awards", "discipline_settings", "peshes", "grievances", "legislative_addresses", "meeting_types", "uploads"], "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}, "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "social_security_number", "genders", "marital_statuses", "veteran_status", "a_number", "units", "shield_number", "placard_number", "start_date", "prom_prov", "prom_perm", "member_since", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "analytics": {"confirm_only_delete_this": true, "singular_headers": true, "create_new": true}, "others": ["employee_employment_statuses", "employee_departments", "employee_sections", "employee_titles", "employees.title_code", "employees.janus_card", "employees.janus_card_opt_out_date", "employee_officer_statuses", "employee_offices", "delegate_assignments", "employee_positions", "employee_ranks", "employees.staff_member", "employees.app_downloaded"]}, "life_insurances": {"table_headers": ["insurance_type", "amount", "start_date", "files", "notes"], "insurance_type": ["basic", "premium", "supplemental", "none"], "insurance_amount": [50000, 100000, 200000], "tabs": ["life_insurances", "premium"]}}, "employee_grievances": {"key_name": "Class Action Grievance", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "employee_departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "beneficiary", "benefit_coverages", "sick_bank", "lodi", "union_meetings", "disciplines", "grievances", "employee_delegate_assignment", "janus", "workers_comp", "life_insurances"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.email", "employees.a_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "affiliations"], ["platoons", "employees.app_downloaded"]], "columns": ["employees", "offices", "ranks", "titles", "employment_statuses", "officer_statuses", "employees.email", "firearm_statuses", "marital_statuses", "positions", "employees.social_security_number", "employees.placard_number", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "departments", "sections", "pacfs", "employees.member_since", "units", "employees.prom_prov", "employees.prom_perm", "employees.app_downloaded"], "default_columns": ["employees", "employees.shield_number", "ranks", "employment_statuses", "offices", "firearm_statuses", "marital_statuses"], "actions": ["single_mailing_label", "multiple_mailing_label", "mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", "reports.show_dependents"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}, "beneficiary": {"primary_filters": [], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.a_number"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.a_number"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}, "workers_comp": {"primary_filters": [["workers_comp"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employment_statuses", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.shield_number"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "life_insurances": {"primary_filters": [["reports.age_group_type", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["excel_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "alternative_email": true, "is_notification_sms_to": true, "filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["offices", "ranks"], ["officer_statuses", "positions"], ["firearm_statuses", "marital_statuses"], ["employees.a_number", "units"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.placard_number"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.app_downloaded"]]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "departments", "sections", "pacfs", "titles", "firearm_statuses", "ranks", "employment_statuses", "officer_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "grievance_statuses", "meeting_types", "genders", "units", "affiliations", "platoons"]}, "maillogs": {"key_name": "Maillogs", "table_headers": ["date_received", "received_from", "entered_by", "going_to", "type"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}}}