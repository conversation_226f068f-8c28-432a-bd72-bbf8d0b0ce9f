# Basic procfile for rails-related part of dev processes
# using a webpack development server to load JavaScript and CSS

# Development rails requires both rails and rails-assets
# (and rails-server-assets if server rendering)
web: rails s -p 3001 -b 0.0.0.0

sidekiq: bundle exec sidekiq -c 1 -q mailer, 2 -q default -q mailers -q active_storage_analysis -q active_storage_purge -L log/sidekiq.log
sidekiq: bundle exec sidekiq -c 1 -q report, -q notification, 2 -L log/sidekiq_report.log

