detectors:
  Attribute:
    exclude:
      - 'Leave'
      - 'BenefitCoverage#is_discarded'
      - 'BenefitDisbursement#is_discarded'

  DuplicateMethodCall:
    max_calls: 2
    exclude:
      - 'Generators::EmployeesQueryGenerator#generate'
      - 'js_errors_driver'
      - 'ReportGenerator::Pdf::MultipleEmployeesPdf#pdf'

  FeatureEnvy:
    exclude:
      - 'EmployeesQuery#process_condition'
      - 'Generators::EmployeeRankGenerator#rank_str'
      - 'LodiDecorator#total_hours_used'
      - 'Importers::ContactsImporter#process_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeLodisWorksheet#create_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeSickBanksWorksheet#create_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeUnionMeetingsWorksheet#create_rows'
      - 'ReportGenerator::Pdf::MultipleEmployeesLabelPdf#pdf'
      - 'ReportGenerator::Pdf::MultipleEmployeesPdf#pdf'
      - 'ValidateAttachmentOfMatcher#matches?'

  InstanceVariableAssumption:
    enabled: false

  IrresponsibleModule:
    enabled: false

  LongParameterList:
    exclude:
      - 'ReportGenerator::Pdf::EmployeeBenefitsPdf#self.create'
      - 'ReportGenerator::Pdf::SingleEmployeePdf#self.create'

  NestedIterators:
    exclude:
      - 'Beneficiary#percentage_less_than_or_equal_100'
      - 'Importers::ApplicationImporter#generate_csv_import_errors'
      - 'ReportGenerator::Excel::Worksheets::EmployeeLodisWorksheet#create_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeSickBanksWorksheet#create_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeUnionMeetingsWorksheet#create_rows'

  NilCheck:
    exclude:
      - 'EmployeeDecorator#cellphone'
      - 'EmployeeDecorator#home_phone'
      - 'EmployeeDecorator#personal_email'
      - 'EmployeeDecorator#work_phone'
      - 'Generators::EmployeeDataGenerator#generate'
      - 'Generators::EmployeeOfficerStatusGenerator#officer_status'
      - 'UserDecorator#cellphone'
      - 'UserDecorator#home_phone'
      - 'UserDecorator#work_phone'

  TooManyStatements:
    exclude:
      - 'Api::ApiAuthentication#authenticate_and_set_user'
      - 'Admin::BenefitsController#render_index'
      - 'Beneficiary#percentage_less_than_or_equal_100'
      - 'DateFormatterHelper#format_import_date'
      - 'DriverRegistration#self.register_selenium_chrome'
      - 'DriverRegistration#self.register_selenium_chrome_headless'
      - 'DriverRegistration#self.register_selenium_firefox'
      - 'EmployeeDecorator#method_missing'
      - 'EmployeeQueryGenerator#generate'
      - 'EmployeesQuery#conditions'
      - 'EmployeesWithBenefitDisbursementsQuery#cal'
      - 'EmployeesWithContactsQuery#call'
      - 'EmployeesWithUnionMeetingsQuery#call'
      - 'Generators::AwardGenerator#generate'
      - 'Generators::BeneficiaryGenerator#generate'
      - 'Generators::BenefitCoverageGenerator#generate'
      - 'Generators::BenefitDisbursementGenerator#generate'
      - 'Generators::ContactGenerator#generate'
      - 'Generators::EmployeeBenefitGenerator#generate'
      - 'Generators::EmployeeDataGenerator#generate'
      - 'Generators::EmployeeFirearmStatusGenerator#generate'
      - 'Generators::EmployeeGenerator#generate'
      - 'Generators::EmployeeOfficeGenerator#generate'
      - 'Generators::EmployeeOfficerStatusGenerator#generate'
      - 'Generators::EmployeePositionGenerator#generate'
      - 'Generators::EmployeeRankGenerator#generate'
      - 'Generators::EmployeeRankGenerator#rank_str'
      - 'Generators::EmployeesQueryGenerator#generate'
      - 'Generators::LodiGenerator#generate'
      - 'Generators::SickBankGenerator#generate'
      - 'Importers::ApplicationImporter#generate_csv_import_errors'
      - 'Importers::AwardsImporter#process_rows'
      - 'Importers::BeneficiariesImporter#process_rows'
      - 'Importers::BenefitCoveragesImporter#process_rows'
      - 'Importers::BenefitDisbursementsImporter#process_rows'
      - 'Importers::ContactsImporter#process_rows'
      - 'Importers::EmployeeBenefitsImporter#process_rows'
      - 'Importers::EmployeeEmploymentStatusesImporter#process_rows'
      - 'Importers::EmployeeFirearmStatusesImporter#process_rows'
      - 'Importers::EmployeeOfficerStatusesImporter#process_rows'
      - 'Importers::EmployeeOfficesImporter#process_rows'
      - 'Importers::EmployeePositionsImporter#process_rows'
      - 'Importers::EmployeeRanksImporter#process_rows'
      - 'Importers::EmployeesImporter#process_rows'
      - 'Importers::LodisImporter#process_rows'
      - 'Importers::SickBanksImporter#process_rows'
      - 'RenderRespondHelper#resource_name'
      - 'ReportGenerator::Excel::Worksheets::EmployeeLodisWorksheet#create_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeSickBanksWorksheet#create_rows'
      - 'ReportGenerator::Excel::Worksheets::EmployeeUnionMeetingsWorksheet#create_rows'
      - 'ReportGenerator::Pdf::MultipleEmployeesLabelPdf#pdf'
      - 'ReportGenerator::Pdf::MultipleEmployeesPdf#pdf'
      - 'UserDecorator#method_missing'

  UtilityFunction:
    exclude:
      - 'Api::JsonWebToken#decode'
      - 'Api::ResponseFormatter#active_record_object?'
      - 'Api::ResponseFormatter#build_fast_jsonapi_options'
      - 'Api::ResponseFormatter#compute_collection_type'
      - 'Api::ResponseFormatter#compute_serializer_name'
      - 'Apartment::CustomElevator#parse_tenant_name'
      - 'DateFormatterHelper#format_date'
      - 'DateFormatterHelper#format_import_date'
      - 'js_errors_driver'
      - 'js_selenium_driver'
      - 'ReportGenerator::LodiReport#columns'
      - 'ReportGenerator::SickBankReport#columns'
      - 'ReportGenerator::Pdf::MultipleEmployeesLabelPdf#initialize_label_type'
      - 'ReportGenerator::Pdf::MultipleEmployeesPdf#initialize_label_type'
      - 'ReportGenerator::UnionMeetingReport#columns'

  TooManyMethods:
    exclude:
      - 'Admin::EmployeesController'
      - 'Admin::UsersController'

  UncommunicativeMethodName:
    exclude:
      - 'Beneficiary#percentage_less_than_or_equal_100'

  UncommunicativeVariableName:
    exclude:
      - 'Api::ApiAuthentication#authenticate_and_set_user'
      - 'Api::ResponseFormatter#active_record_object?'
      - 'Beneficiary#percentage_less_than_or_equal_100'

  TooManyConstants:
    exclude:
      - 'Report::ReportTypes'

  ManualDispatch:
    exclude:
      - 'Api::ResponseFormatter#active_record_object?'

exclude_paths:
  - db
