# frozen_string_literal: true

ENV['RAILS_ENV'] ||= 'test'

# require 'coveralls'
# Coveralls.wear!('rails') # must occur before any of your application code is required

require File.expand_path('../config/environment', __dir__)

abort('The Rails environment is running in production mode!') if Rails.env.production?

require 'spec_helper'
require 'rspec/rails'
require 'shoulda_helper'
require 'helpers'
require 'support/api_authentication'
require 'support/shared_authentication_contexts'

require 'capybara/rspec'
require 'capybara/poltergeist'
require 'capybara-screenshot/rspec'
require 'webmock/rspec'
require 'rake'
require 'support/shared_authorization_contexts'

WebMock.disable_net_connect!(allow_localhost: true)

Dir[Rails.root.join('spec/support/**/*.rb')].sort.each { |f| require f }

RSpec.configure do |config|
  # Next line will ensure that assets are built if webpack -w is not running

  # Because we're using some CSS Webpack files, we need to ensure the webpack files are generated
  # for all feature specs. https://github.com/shakacode/react_on_rails/issues/792
  config.define_derived_metadata(file_path: %r{spec/(features|requests)}) do |metadata|
    metadata[:requires_webpack_assets] = true
  end

  config.include Helpers

  # config.infer_base_class_for_anonymous_controllers = false

  config.infer_spec_type_from_file_location!

  config.filter_rails_from_backtrace!

  Dir[Rails.root.join('spec/support/helpers/*.rb')].each do |f|
    config.include f.split('/').last.split('.rb').last.classify.constantize
  end

  config.include Devise::Test::ControllerHelpers, type: :controller
  config.include Devise::Test::ControllerHelpers, type: :view
  # config.include ControllerHelpers

  config.use_transactional_fixtures = false

  config.before(:suite) do
    if config.use_transactional_fixtures?
      raise(<<-MSG)
        Delete line `config.use_transactional_fixtures = true` from rails_helper.rb
        (or set it to false) to prevent uncommitted transactions being used in
        JavaScript-dependent specs.

        During testing, the app-under-test that the browser driver connects to
        uses a different database connection to the database connection used by
        the spec. The app's database connection would not be able to access
        uncommitted transaction data setup over the spec's database connection.
      MSG
    end

    DatabaseCleaner.strategy = :transaction
    DatabaseCleaner.clean_with(:truncation)

    # Testing multi-tenant applications - https://github.com/influitive/apartment/wiki/Testing-Your-Application
    begin
      Apartment::Tenant.drop('mallow')
    rescue StandardError
      nil
    end
    account = Fabricate(:mallow)
    Apartment::Tenant.switch!(account.subdomain)
  end

  config.before(:each) do
    DatabaseCleaner.strategy = :transaction
    DatabaseCleaner.start
  end

  config.before(:each, type: :feature) do
    # :rack_test driver's Rack app under test shares database connection
    # with the specs, so continue to use transaction strategy for speed.
    driver_shares_db_connection_with_specs = Capybara.current_driver == :rack_test

    unless driver_shares_db_connection_with_specs
      # Driver is probably for an external browser with an app
      # under test that does *not* share a database connection with the
      # specs, so use truncation strategy.
      DatabaseCleaner.strategy = :truncation
    end
  end

  config.append_after(:each) do
    DatabaseCleaner.clean
  end

  # For Poltergeist
  # Turning animations off results in about a 10 sec difference:

  # Using errors_ok as there is a timing issue causing crashes without this setting
  # https://github.com/teampoltergeist/poltergeist/issues/830

  default_driver = :poltergeist_no_animations
  supported_drivers = %i[ poltergeist poltergeist_errors_ok
                          poltergeist_no_animations webkit
                          selenium_chrome selenium_firefox selenium]
  driver = ENV['DRIVER'].try(:to_sym) || default_driver
  Capybara.default_driver = driver

  raise "Unsupported driver: #{driver} (supported = #{supported_drivers})" unless supported_drivers.include?(driver)

  case driver
  when :poltergeist, :poltergeist_errors_ok, :poltergeist_no_animations
    basic_opts = {
      window_size: [1300, 1800],
      screen_size: [1400, 1900],
      phantomjs_options: ['--load-images=no', '--ignore-ssl-errors=true'],
      timeout: 180
    }

    Capybara.register_driver :poltergeist do |app|
      Capybara::Poltergeist::Driver.new(app, basic_opts)
    end

    no_animation_opts = basic_opts.merge( # Leaving animations off, as a sleep was still needed.
      extensions: [Rails.root.join('spec', 'support', 'phantomjs-disable-animations.js').to_s]
    )

    Capybara.register_driver :poltergeist_no_animations do |app|
      Capybara::Poltergeist::Driver.new(app, no_animation_opts)
    end

    Capybara.register_driver :poltergeist_errors_ok do |app|
      Capybara::Poltergeist::Driver.new(app, no_animation_opts.merge(js_errors: false))
    end

    Capybara::Screenshot.register_driver(:poltergeist) do |js_driver, path|
      js_driver.browser.save_screenshot(path)
    end

    Capybara::Screenshot.register_driver(:poltergeist_no_animations) do |js_driver, path|
      js_driver.render(path, full: true)
    end

    Capybara::Screenshot.register_driver(:poltergeist_errors_ok) do |js_driver, path|
      js_driver.render(path, full: true)
    end
  when :selenium_chrome
    DriverRegistration.register_selenium_chrome
  when :selenium_firefox, :selenium
    DriverRegistration.register_selenium_firefox
    driver = :selenium_firefox
  end

  Capybara.javascript_driver = driver
  Capybara.default_driver = driver

  # Capybara.register_server(Capybara.javascript_driver) do |app, port|
  #  require "rack/handler/puma"
  #  Rack::Handler::Puma.run(app, Port: port)
  # end

  # Capybara.default_max_wait_time = 15
  puts "Capybara using driver: #{Capybara.javascript_driver}"

  Capybara.save_path = Rails.root.join('tmp', 'capybara')
  Capybara::Screenshot.prune_strategy = { keep: 10 }

  Capybara.asset_host = 'http://localhost:3002'

  def js_errors_driver
    Capybara.javascript_driver == :poltergeist ? :poltergeist_errors_ok : Capybara.javascript_driver
  end

  def js_selenium_driver
    driver = Capybara.javascript_driver == :selenium_firefox ? :selenium_firefox : :selenium_chrome
    if driver == :selenium_firefox
      DriverRegistration.register_selenium_firefox
    else
      DriverRegistration.register_selenium_chrome
    end
    driver
  end
end
