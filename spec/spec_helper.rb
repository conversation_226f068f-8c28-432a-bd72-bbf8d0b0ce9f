# frozen_string_literal: true

RSpec.configure do |config|
  config.expect_with :rspec do |expectations|
    expectations.include_chain_clauses_in_custom_matcher_descriptions = true
  end

  config.before do
    WebMock.disable_net_connect!(allow: %r{https://content-civicinfo.googleapis.com/civicinfo/v2})
  end

  config.before(:each, type: :view) do
    config.mock_with :rspec do |mocks|
      mocks.verify_partial_doubles = false
    end
  end

  config.after(:each, type: :view) do
    config.mock_with :rspec do |mocks|
      mocks.verify_partial_doubles = true
    end
  end

  config.shared_context_metadata_behavior = :apply_to_host_groups

  config.disable_monkey_patching!

  config.default_formatter = 'doc' if config.files_to_run.one?

  config.profile_examples = 10

  # config.filter_run_when_matching :focus

  # config.example_status_persistence_file_path = "spec/examples.txt"

  config.order = :random

  config.before(:each) do
    DatabaseCleaner.start
  end

  config.after(:each) do
    DatabaseCleaner.clean
  end

  Kernel.srand config.seed
end
