# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::UserRightsController, type: :controller do
  let(:user) { Fabricate(:user) }
  let(:params) { Fabricate.attributes_for(:user) }

  describe 'GET #index' do

    context 'Valid cases', authenticated: true do

      it 'Checks the success response' do
        get :index
        success_response
      end
    end
  end

  describe 'GET #roles_list' do

    context 'Valid cases', authenticated: true do

      it 'Checks the success response' do
        get :roles_list
        success_response
      end
    end
  end
end