# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::UserAuditsController, type: :controller do
  let(:employee) { Fabricate(:employee) }
  let(:params) { Fabricate.attributes_for(:employee) }

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'user_audit', rights: 'user_audit' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :employee)
      end

      it 'Respond User Audit list with default pagination count' do
        get :index, params: { page: 1 }
        expect(parsed_response.count == 25)
        success_response
      end

      it 'Display list of employee page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count == 5)
      end
    end
  end
end
