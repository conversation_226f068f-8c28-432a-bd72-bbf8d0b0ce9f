require 'rails_helper'

RSpec.describe Api::ReminderTrackersController, type: :controller do
  let(:reminder) { Fabricate(:reminder) }
  let(:user) { Fabricate(:user) }
  let(:reminder_tracker) do
    Fabricate(:reminder_tracker, user_id: user.id, reminder_id: reminder.id)
  end
  let(:params) { Fabricate.attributes_for(:reminder_tracker) }

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'reminder' ,
                                                                                   object_name: 'reminder_tracker' ,
                                                                                   params: { reminder_id: 1 } }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :reminder_tracker, reminder: reminder, user: user)
      end

      it 'Respond reminder tracker list' do
        get :index, params: { reminder_id: reminder.id }
        success_response
      end

      it 'Respond reminder tracker list with default pagination count' do
        get :index, params: { reminder_id: reminder.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of reminder tracker page 2' do
        get :index, params: { reminder_id: reminder.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end
    end

    context 'Valid email filter cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(5, :reminder_tracker, reminder: reminder, user: user, email_delivery_status: 1)
        Fabricate.times(6, :reminder_tracker, reminder: reminder, user: user, email_delivery_status: 2)
        Fabricate.times(5, :reminder_tracker, reminder: reminder, user: user, email_delivery_status: 5)
        Fabricate(:reminder_tracker, reminder: reminder, user: user, email_delivery_status: 2)
      end

      it 'Display reminder tracker by search email sent' do
        get :index, params: { reminder_id: reminder.id, email_status: 'sent' }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display reminder tracker by search email delivered' do
        get :index, params: { reminder_id: reminder.id, email_status: 'delivered' }
        expect(parsed_response.count).to eq(7)
      end

      it 'Display reminder tracker by search email failed' do
        get :index, params: { reminder_id: reminder.id, email_status: 'failed' }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display reminder tracker by search email status all' do
        get :index, params: { reminder_id: reminder.id }
        expect(parsed_response.count).to eq(17)
      end

      it 'Display reminder tracker by no filter' do
        get :index, params: { reminder_id: reminder.id }
        expect(parsed_response.count).to eq(17)
      end
    end
  end

  describe 'PATCH #update' do
    let(:reminder_tracker) { Fabricate(:reminder_tracker, user_id: user.id, reminder_id: reminder.id, status: 'pending') }

    context 'Valid cases - Update Reminder status', authenticated: true do
      it 'Update reminder tracker ' do
        patch :update, params: { id: reminder_tracker.id, reminder_tracker: { status: 'completed' } }, format: :json
        reminder_tracker.reload
        expect(reminder_tracker.status).to eq('completed')
        success_response
      end

      it 'Returns not found for invalid reminder id' do
        patch :update, params: { id: -1, reminder_tracker: { status: 'completed' } }, format: :json
        not_found
      end
    end

  end

end
