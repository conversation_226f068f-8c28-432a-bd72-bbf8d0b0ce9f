# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::NotificationTrackersController, type: :controller do
  let(:notification) { Fabricate(:notification) }
  let(:employee) { Fabricate(:employee) }
  let(:notification_tracker) do
    Fabricate(:notification_tracker, notification_id: notification_id.id, employee_id: employee.id)
  end
  let(:params) { Fabricate.attributes_for(:notification_tracker) }

  def notify_params(types, notification_tracker)
    { "Type": 'Notification',
      "Message": { "notificationType": types,
                   "mail": { "headers": [{ "name": 'employee-id', "value": notification_tracker.employee_id },
                                         { "name": 'notification-id', "value": notification_tracker.notification_id },
                                         { "name": 'subdomain', "value": Account.first.subdomain }] } }.to_json }
  end

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'notification',
                                                                                   object_name: 'notification_tracker',
                                                                                   params: { notification_id: 1 } }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :notification_tracker, notification: notification, employee: employee)
      end

      it 'Respond notification tracker list' do
        get :index, params: { notification_id: notification.id }
        success_response
      end

      it 'Respond notification tracker list with default pagination count' do
        get :index, params: { notification_id: notification.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of notification tracker page 2' do
        get :index, params: { notification_id: notification.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid email filter cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(5, :notification_tracker, notification: notification, employee: employee,
                                                  email: true, sent: true)
        Fabricate.times(6, :notification_tracker, notification: notification, employee: employee,
                                                  email: true, delivered: true)
        Fabricate.times(5, :notification_tracker, notification: notification, employee: employee,
                                                  email: true, rejected: true)
        Fabricate(:notification_tracker, notification: notification, employee: employee, email: true, sent: true,
                                         delivered: true)
      end

      it 'Display notification tracker by search email sent' do
        get :index, params: { notification_id: notification.id, status: 'sent', type: 'email' }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display notification tracker by search email delivered' do
        get :index, params: { notification_id: notification.id, status: 'delivered', type: 'email' }
        expect(parsed_response.count).to eq(7)
      end

      it 'Display notification tracker by search email failed' do
        get :index, params: { notification_id: notification.id, status: 'failed', type: 'email' }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display notification tracker by search email status all' do
        get :index, params: { notification_id: notification.id, status: 'all', type: 'email' }
        expect(parsed_response.count).to eq(17)
      end

      it 'Display notification tracker by no filter' do
        get :index, params: { notification_id: notification.id }
        expect(parsed_response.count).to eq(17)
      end
    end

    context 'Valid sms filter cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(5, :notification_tracker, notification: notification, employee: employee,
                                                  sms: true, sms_sent: true)
        Fabricate.times(6, :notification_tracker, notification: notification, employee: employee,
                                                  sms: true, sms_delivered: true)
        Fabricate.times(5, :notification_tracker, notification: notification, employee: employee,
                                                  sms: true, sms_failed: true)
        Fabricate(:notification_tracker, notification: notification, employee: employee, sms: true, sms_sent: true,
                                         sms_delivered: true)
      end

      it 'Display notification tracker by search sms sent' do
        get :index, params: { notification_id: notification.id, status: 'sent', type: 'sms' }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display notification tracker by search sms delivered' do
        get :index, params: { notification_id: notification.id, status: 'delivered', type: 'sms' }
        expect(parsed_response.count).to eq(7)
      end

      it 'Display notification tracker by search sms failed' do
        get :index, params: { notification_id: notification.id, status: 'failed', type: 'sms' }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display notification tracker by search sms status all' do
        get :index, params: { notification_id: notification.id, status: 'all', type: 'sms' }
        expect(parsed_response.count).to eq(17)
      end

      it 'Display notification tracker by no sms filter' do
        get :index, params: { notification_id: notification.id }
        expect(parsed_response.count).to eq(17)
      end
    end

    context 'Invalid params', authenticated: true do
      it 'notification id invalid params' do
        get :index
        expect(error_response).to eq(['Notification id is missing'])
      end
    end
  end

  describe 'GET #Create' do
    before(:each) do
      @notification_tracker = NotificationTracker.create(employee_id: employee.id, notification_id: notification.id,
                                                         notification_type: 'email', email: true)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Update notification tracker for sent email message' do
        post :create, body: notify_params('send', @notification_tracker).to_json
        expect(NotificationTracker.last.sent).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent email delivered' do
        post :create, body: notify_params('delivery', @notification_tracker).to_json
        expect(NotificationTracker.last.delivered).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent email opened' do
        post :create, body: notify_params('open', @notification_tracker).to_json
        expect(NotificationTracker.last.opened).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent email clicked' do
        post :create, body: notify_params('click', @notification_tracker).to_json
        expect(NotificationTracker.last.clicked).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent email reject' do
        post :create, body: notify_params('reject', @notification_tracker).to_json
        expect(NotificationTracker.last.rejected).to eq(true)
        success_response
      end
    end
  end
end
