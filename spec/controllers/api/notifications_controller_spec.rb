# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::NotificationsController do
  include ActiveSupport::Testing::TimeHelpers

  let(:notification) { Fabricate(:notification) }

  let(:params) { Fabricate.attributes_for(:notification) }

  let(:sms_attachments) do
    [Rack::Test::UploadedFile.new('spec/assets/image.jpeg', 'image/jpeg'),
     Rack::Test::UploadedFile.new('spec/assets/image.jpeg', 'image/jpeg')]
  end

  let(:add_sms_restriction) do
    account = Account.find_by(subdomain: Apartment::Tenant.current)
    account.saas_json['restrictions'] = { "notifications": { "allowed_mms_count": '2' } }
    account.save
  end

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'notification' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        @notifications = Fabricate.times(30, :notification)
      end

      it 'Respond notification list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of notification page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of notification page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display notification by search invalid date range' do
        get :index, params: { from_date: '3020-11-10' }
        expect(parsed_response.count).to eq(0)
      end

      it 'Display notification by search valid date range' do
        get :index, params: { from_date: @notifications.first.created_at }
        expect(parsed_response.count).to eq(25)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'notification' }

    context 'Valid cases - authenticated user', authenticated: true do
      after :each do
        success_response
      end

      it 'creates notification with valid params' do
        expect do
          post :create, params: { notification: params }, format: :json
        end.to change(Notification, :count).by(1)
        expect(parsed_response['attributes']['subject']).to eq(params['subject'])
        expect(parsed_response['attributes']['sms_message']).to eq(params['sms_message'])
        expect(parsed_response['attributes']['email_message']).to eq(params['email_message'])
        expect(parsed_response['attributes']['push_message']).to eq(params['push_message'])
      end

      it 'creates notification for personal email' do
        employee = Fabricate(:employee)
        Fabricate(:contact, employee_id: employee.id, contact_for: 'personal')
        expect do
          post :create, params: { notification: params.merge(email: true, sms: false, push: false, email_to: 'personal') }, format: :json
        end.to change(Notification, :count).by(1)
        expect(parsed_response['attributes']['subject']).to eq(params['subject'])
        expect(parsed_response['attributes']['sms_message']).to eq(params['sms_message'])
        expect(parsed_response['attributes']['email_message']).to eq(params['email_message'])
        expect(parsed_response['attributes']['push_message']).to eq(params['push_message'])
      end

      it 'enqueues notifications job' do
        expect do
          post :create, params: { notification: params.merge(email: true, sms: false, push: false, email_to: 'personal') }, format: :json
        end.to have_enqueued_job
      end

      it 'creates notification for sms' do
        employee = Fabricate(:employee)
        Fabricate(:contact, employee_id: employee.id, contact_type: 'phone', contact_for: 'personal', value: '(121) 221 - 2121')
        expect do
          post :create, params: { notification: params.merge(email: false, sms: true, push: false) }, format: :json
        end.to change(Notification, :count).by(1)
      end

      it 'creates notification for push' do
        employee = Fabricate(:employee)
        Fabricate(:contact, employee_id: employee.id, contact_type: 'phone', contact_for: 'personal', value: '(121) 221 - 2121')
        expect do
          post :create, params: { notification: params.merge(email: false, sms: false, push: true) }, format: :json
        end.to change(Notification, :count).by(1)
      end

      it 'creates notification for sms, email and push' do
        employee = Fabricate(:employee)
        Fabricate(:contact, employee_id: employee.id, contact_type: 'phone', contact_for: 'personal', value: '(121) 221 - 2121')
        expect do
          post :create, params: { notification: params.merge(email: true, sms: true, push: true) }, format: :json
        end.to change(Notification, :count).by(1)
      end

      it 'creates notification for work email' do
        employee = Fabricate(:employee)
        Fabricate(:contact, employee_id: employee.id)
        expect do
          post :create, params: { notification: params.merge(email: true, sms: false, push: false, email_to: 'work') }, format: :json
        end.to change(Notification, :count).by(1)
        expect(parsed_response['attributes']['subject']).to eq(params['subject'])
        expect(parsed_response['attributes']['sms_message']).to eq(params['sms_message'])
        expect(parsed_response['attributes']['email_message']).to eq(params['email_message'])
        expect(parsed_response['attributes']['push_message']).to eq(params['push_message'])
      end

      context 'MMS' do
        it 'creates notification for MMS' do
          expect do
            post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                               sms_attachments: sms_attachments) }, format: :json
          end.to change(Notification, :count).by(1)
        end

        it 'creates MMS attachments' do
          expect do
            post :create, params: { notification: params.merge(email: false, sms: true,
                                                               sms_attachments: sms_attachments) }, format: :json
          end.to change(ActiveStorage::Blob, :count).by sms_attachments.length
          expect(parsed_response['attributes']['sms_attachments'].length).to eq sms_attachments.length
        end

        it 'Sets the mms flag when the SMS notification is created with attachments' do
          post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                             sms_attachments: sms_attachments) }, format: :json
          expect(parsed_response['attributes']['is_mms']).to eq(true)
        end

        it 'allows to upload png, jpg, jpeg and gif attachments' do
          attachments = [Rack::Test::UploadedFile.new('spec/assets/Small-shakin-it.gif', 'image/gif'),
                         Rack::Test::UploadedFile.new('spec/assets/image.jpeg', 'image/jpeg'),
                         Rack::Test::UploadedFile.new('spec/assets/2mb_image.jpg', 'image/jpg'),
                         Rack::Test::UploadedFile.new('spec/assets/image.png', 'image/png')]

          post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                             sms_attachments: attachments) }, format: :json
          expect(parsed_response['attributes']['is_mms']).to eq(true)
        end
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      context 'MMS' do
        it 'not allowing to create MMS when attachments length is more than 10' do
          invalid_attachments = sms_attachments
          10.times do
            invalid_attachments << Rack::Test::UploadedFile.new('spec/assets/image.jpeg', 'image/jpeg')
          end
          post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                             sms_attachments: invalid_attachments) }, format: :json
          expect(error_response).to eq([{ 'sms_attachments' => ['- Only 10 attachments are supported'] }])
        end

        it 'Not allowing to upload attachment with more than 2 MB' do
          invalid_attachments = [Rack::Test::UploadedFile.new('spec/assets/6mb_image.png', 'image/jpeg')]
          post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                             sms_attachments: invalid_attachments) }, format: :json
          expect(error_response).to eq([{ 'sms_attachments' => ['File size should be less than 2 MB',
                                                                '- Maximum of 5 MB can be attached'] }])
        end

        it 'Not allowing to upload pdf attachments' do
          invalid_attachments = [Rack::Test::UploadedFile.new('spec/assets/sample.pdf',
                                                              'application/pdf')]
          post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                             sms_attachments: invalid_attachments) }, format: :json
          expect(error_response).to eq([{ 'sms_attachments' => ['is not a valid file format'] }])
        end

        it 'Not allowing to upload more than 5MB of total attachments size' do
          invalid_attachments = []
          4.times do
            invalid_attachments << Rack::Test::UploadedFile.new('spec/assets/2mb_image.jpg',
                                                                'image/jpg')
          end

          post :create, params: { notification: params.merge(email: false, sms: true, push: false,
                                                             sms_attachments: invalid_attachments) }, format: :json
          expect(error_response).to eq([{ 'sms_attachments' => ['- Maximum of 5 MB can be attached'] }])
        end

        it 'Not allowing to create more than allowed limit' do
          Fabricate.times(2, :notification, sms: true, sms_attachments: sms_attachments)
          add_sms_restriction
          post :create, params: {
            notification: params
              .merge(email: false, sms: true, push: false,
                     sms_attachments: [Rack::Test::UploadedFile.new('spec/assets/image.png',
                                                                    'image/png')])
          }, format: :json

          expect(error_response).to eq([{ 'sms_attachments' =>
                                           ['You have exceeded the monthly limit for the MMS. Please contact admin.'] }])

          # Check whether the app allows to create the MMS notifications in the next month.
          travel_to Time.now + 12.months

          Fabricate.times(2, :notification, sms: true, sms_attachments: sms_attachments)
        end
      end

      it 'Should not create notification with subject blank' do
        post :create, params: { notification: params.merge(email: true, subject: '') }, format: :json
        expect(error_response).to eq([{ 'subject' => ["can't be blank"] }])
      end

      it 'Should not create notification with email_message blank' do
        post :create, params: { notification: params.merge(email: true, email_message: '') }, format: :json
        expect(error_response).to eq([{ 'email_message' => ["can't be blank"] }])
      end

      it 'Should not create notification with sms_message blank' do
        post :create, params: { notification: params.merge(sms: true, sms_message: '') }, format: :json
        expect(error_response).to eq([{ 'sms_message' => ["can't be blank"] }])
      end

      it 'Should not create notification with sms_message blank' do
        post :create, params: { notification: params.merge(email: true, email_to: '') }, format: :json
        expect(error_response).to eq([{ 'email_to' => ["can't be blank"] }])
      end

      it 'Should not create notification with push_message blank' do
        post :create, params: { notification: params.merge(push: true, push_message: '') }, format: :json
        expect(error_response).to eq([{ 'push_message' => ["can't be blank"] }])
      end
    end
  end

  describe 'GET #push_notification_index' do
    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        (1..30).each do |i|
          @notification = Fabricate(:notification)
          @notification_tracker = Fabricate(:notification_tracker, notification_id: @notification.id)
        end
        NotificationTracker.update_all(employee_id: @employee.id)
        Notification.update_all(push: true)
      end

      it 'Respond push notification list with default pagination count' do
        get :push_notification_index
        expect(parsed_response.count).to eq(25)
        success_response
      end
    end
  end

  describe 'GET #push_notification_show' do
    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond push notification show' do
        notification = Fabricate(:notification)
        get :push_notification_show, params: {notification_id: notification.id}
        success_response
      end
    end
  end
end
