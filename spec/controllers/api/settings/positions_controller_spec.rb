# frozen_string_literal: true

RSpec.describe Api::Settings::PositionsController do
  let(:position) { Fabricate(:position) }
  let(:params) { Fabricate.attributes_for(:position) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'position' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :position)
      end

      it 'Respond positions list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of position page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of position page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond position list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond position list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of positions ordered by name ascending" do
        sort_positions = Position.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_positions.ids)
      end
    end

    context 'Position list - Search by name with authenticated user', authenticated: true do
      it 'Filters the positions by name' do
        Fabricate.times(5, :position)
        get :index, params: { search_text: position.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :position)
      end

      it 'Respond positions list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of position page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of position page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond position list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond position list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of positions ordered by name ascending" do
        sort_positions = Position.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_positions.ids)
      end
    end

    context 'Position list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the positions by name' do
        Fabricate.times(5, :position)
        get :index, params: { search_text: position.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'position' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create position with valid params' do
        expect { post :create, params: { position: params, format: :json } }.to change(Position, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create position with empty params' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'positions', fields: validation_columns)
        post :create, params: { position: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create position with empty params description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'positions', fields: validation_columns)
        post :create, params: { position: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check position validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'positions', fields: validation_columns)
        post :create, params: { position: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check position validation if required fields not present' do
        modify_account_schema_validations(model: 'positions', action: 'clear')
        expect do
          post :create, params: { position: params.merge(name: '', description: ''), format: :json }
        end.to change(Position, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create position with invalid same name' do
        position
        post :create, params: { position: params.merge(name: position.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create position' do
        post :create, params: { position: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'position' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update position with valid params' do
        patch :update, params: { id: position.id, position: { name: 'position' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('position')
        success_response
      end

      it 'Should update position with valid params slug' do
        patch :update, params: { id: position.slug, position: { name: 'position' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('position')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update position with empty params' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'positions', fields: validation_columns)
        patch :update, params: { id: position.id, position: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update position with empty params description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'positions', fields: validation_columns)
        patch :update, params: { id: position.id, position: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check position validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'positions', fields: validation_columns)
        patch :update, params: { id: position.id, position: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check position validation if required fields not present' do
        modify_account_schema_validations(model: 'positions', action: 'clear')
        patch :update, params: { id: position.id, position: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update position with invalid same name' do
        patch :update, params: { id: Fabricate(:position).id, position: params.merge(name: position.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update position with blank params' do
        patch :update, params: { id: '', position: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update position' do
        patch :update, params: { id: position.id, position: { name: 'position' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'position' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete position with valid params slug' do
        delete :destroy, params: { id: position.slug, format: :json }
        expect(assigns(:position).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete position with valid params id' do
        delete :destroy, params: { id: position.id, format: :json }
        expect(assigns(:position).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete position with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete position with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete position' do
        delete :destroy, params: { id: position.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
