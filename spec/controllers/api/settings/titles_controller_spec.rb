# frozen_string_literal: true

RSpec.describe Api::Settings::TitlesController do
  let(:title) { Fabricate(:title) }
  let(:section) { Fabricate(:section) }
  let(:department) { Fabricate(:department) }
  let(:params) { Fabricate.attributes_for(:title) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'title' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :title)
      end

      it 'Respond titles list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of title page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Display list of title page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Respond titles list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
        success_response
      end

      it 'Respond titles list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of titles ordered by name ascending" do
        sort_titles = Title.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_titles.ids)
      end
    end

    context 'Title list - Search by name with authenticated user', authenticated: true do
      it 'Filters the titles by name' do
        Fabricate.times(5, :title)
        get :index, params: { search_text: title.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :title)
      end

      it 'Respond titles list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of title page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Display list of title page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Respond titles list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
        success_response
      end

      it 'Respond titles list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of titles ordered by name ascending" do
        sort_titles = Title.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_titles.ids)
      end
    end

    context 'Title list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the titles by name' do
        Fabricate.times(5, :title)
        get :index, params: { search_text: title.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'title' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create title with valid params' do
        modify_account_schema_validations(model: 'titles', action: 'clear')
        expect { post :create, params: { title: params, format: :json } }.to change(Title, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create title with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'titles', fields: validation_columns)
        post :create, params: { title: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create title with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'titles', fields: validation_columns)
        post :create, params: { title: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check title validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'titles', fields: validation_columns)
        post :create, params: { title: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check title validation if required fields not present' do
        modify_account_schema_validations(model: 'titles', action: 'clear')
        expect do
          post :create, params: { title: params.merge(name: '', description: ''), format: :json }
        end.to change(Title, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create title with invalid same name' do
        title = Fabricate(:title)
        post :create, params: { title: params.merge(name: title.name, section_id: title.section_id), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not create title with invalid same title_code' do
        title = Fabricate(:title)
        post :create, params: { title: params.merge(title_code: title.title_code, section_id: title.section_id), format: :json }
        expect(error_response).to eq([{ 'title_code' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not create title with blank department id' do
        post :create, params: { title: params.merge(department_id: ''), format: :json }
        expect(error_response).to eq([{ 'department' => ['must exist'], 'department_id' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should not create title with blank section id' do
        post :create, params: { title: params.merge(section_id: ''), format: :json }
        expect(error_response).to eq([{ 'section' => ['must exist'], 'section_id' => ["can't be blank"] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create title' do
        post :create, params: { title: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'title' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update title with valid params' do
        patch :update, params: { id: title.id, title: { name: 'title' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('title')
        success_response
      end

      it 'Should update title with valid params slug' do
        patch :update, params: { id: title.slug, title: { name: 'title' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('title')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update title with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'titles', fields: validation_columns)
        patch :update, params: { id: title.id, title: { name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update title with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'titles', fields: validation_columns)
        patch :update, params: { id: title.id, title: { description: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check title validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'titles', fields: validation_columns)
        patch :update, params: { id: title.id, title: { name: '', description: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check title validation if required fields not present' do
        modify_account_schema_validations(model: 'titles', action: 'clear')
        patch :update, params: { id: title.id, title: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update title with invalid same name' do
        Fabricate(:title, department: department, section: section, name: 'new_title')
        title2 = Fabricate(:title, department: department, section: section)
        patch :update, params: { id: title2.id,
                                 title: { name: 'new_title', section_id: title2.section_id }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update title with invalid same title_code' do
        Fabricate(:title, department: department, section: section, title_code: 'a123')
        title2 = Fabricate(:title, department: department, section: section)
        patch :update, params: { id: title2.id,
                                 title: { title_code: 'a123', section_id: title2.section_id }, format: :json }
        expect(error_response).to eq([{ 'title_code' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update title with blank id' do
        patch :update, params: { id: '', title: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update title' do
        patch :update, params: { id: title.id, title: { name: 'title' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'title' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete title with valid params slug' do
        delete :destroy, params: { id: title.slug, format: :json }
        expect(assigns(:title).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete title with valid params id' do
        delete :destroy, params: { id: title.id, format: :json }
        expect(assigns(:title).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete title with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete title' do
        delete :destroy, params: { id: title.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
