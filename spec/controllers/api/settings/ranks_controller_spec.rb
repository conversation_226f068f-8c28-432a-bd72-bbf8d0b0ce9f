# frozen_string_literal: true

RSpec.describe Api::Settings::RanksController do
  let(:rank) { Fabricate(:rank) }
  let(:params) { Fabricate.attributes_for(:rank) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :rank)
      end

      it 'Respond ranks list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of rank page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of rank page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond rank list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond ranks list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of ranks ordered by name ascending" do
        sort_ranks = Rank.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_ranks.ids)
      end
    end

    context 'Rank list - Search by name with authenticated user', authenticated: true do
      it 'Filters the ranks by name' do
        Fabricate.times(5, :rank)
        get :index, params: { search_text: rank.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :rank)
      end

      it 'Respond ranks list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of rank page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of rank page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond rank list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond ranks list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of ranks ordered by name ascending" do
        sort_ranks = Rank.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_ranks.ids)
      end
    end

    context 'Rank list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the ranks by name' do
        Fabricate.times(5, :rank)
        get :index, params: { search_text: rank.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create rank with valid params' do
        expect { post :create, params: { rank: params, format: :json } }.to change(Rank, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create rank with empty params' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'ranks', fields: ['name'])
        post :create, params: { rank: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create rank with empty params description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'ranks', fields: ['description'])
        post :create, params: { rank: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check rank validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'ranks', fields: %w[name description])
        post :create, params: { rank: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check rank validation if required fields not present' do
        modify_account_schema_validations(model: 'ranks', action: 'clear')
        expect do
          post :create, params: { rank: params.merge(name: '', description: ''), format: :json }
        end.to change(Rank, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create rank with invalid same name' do
        rank
        post :create, params: { rank: params.merge(name: rank.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create rank' do
        post :create, params: { rank: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update rank with valid params' do
        patch :update, params: { id: rank.id, rank: { name: 'rank' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('rank')
        success_response
      end

      it 'Should update rank with valid params slug' do
        patch :update, params: { id: rank.slug, rank: { name: 'rank' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('rank')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update rank with empty params' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'ranks', fields: ['name'])
        patch :update, params: { id: rank.id, rank: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update rank with empty description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'ranks', fields: ['description'])
        patch :update, params: { id: rank.id, rank: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check rank validation if required fields not present' do
        modify_account_schema_validations(model: 'ranks', action: 'clear')
        patch :update, params: { id: rank.id, rank: params.merge(name: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update rank with invalid same name' do
        rank1 = Fabricate(:rank)
        patch :update, params: { id: rank1.id, rank: params.merge(name: rank.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update rank with blank id' do
        patch :update, params: { id: '', rank: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update rank' do
        patch :update, params: { id: rank.id, rank: { name: 'rank' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete rank with valid params slug' do
        delete :destroy, params: { id: rank.slug, format: :json }
        expect(assigns(:rank).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete rank with valid params id' do
        delete :destroy, params: { id: rank.id, format: :json }
        expect(assigns(:rank).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete rank with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete rank with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete rank' do
        delete :destroy, params: { id: rank.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
