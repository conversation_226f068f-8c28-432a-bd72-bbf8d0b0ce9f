# frozen_string_literal: true

RSpec.describe Api::Settings::TourOfDutiesController, type: :controller do
  let(:tour_of_duty) { Fabricate(:tour_of_duty) }
  let(:params) { Fabricate.attributes_for(:tour_of_duty) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'tour_of_duty' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :tour_of_duty)
      end

      it 'Respond tour_of_duties list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of tour_of_duty page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of tour_of_duty page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond tour_of_duties list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond tour_of_duties list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of tour_of_duties ordered by name ascending" do
        sort_tour_of_duties = TourOfDuty.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_tour_of_duties.ids)
      end
    end

    context 'Tour_of_duty list - Search by name with authenticated user', authenticated: true do
      it 'Filters the tour_of_duties by name' do
        Fabricate.times(5, :tour_of_duty)
        get :index, params: { search_text: tour_of_duty.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :tour_of_duty)
        TourOfDuty.last.destroy
      end

      it 'Respond tour_of_duties list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of tour_of_duty page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of tour_of_duty page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond tour_of_duties list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond tour_of_duties list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of tour_of_duties ordered by name ascending" do
        sort_tour_of_duties = TourOfDuty.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_tour_of_duties.ids)
      end
    end

    context 'Tour_of_duty list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the tour_of_duties by name' do
        Fabricate.times(5, :tour_of_duty)
        get :index, params: { search_text: tour_of_duty.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'tour_of_duty' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create tour_of_duty with valid params' do
        expect { post :create, params: { tour_of_duty: params, format: :json } }.to change(TourOfDuty, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create tour_of_duty with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'tour_of_duties', fields: validation_columns)
        post :create, params: { tour_of_duty: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create tour_of_duty with empty description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'tour_of_duties', fields: validation_columns)
        post :create, params: { tour_of_duty: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check tour_of_duty validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'tour_of_duties', fields: validation_columns)
        post :create, params: { tour_of_duty: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check tour_of_duty validation if required fields not present' do
        modify_account_schema_validations(model: 'tour_of_duties', action: 'clear')
        expect do
          post :create, params: { tour_of_duty: params.merge(name: '', description: ''), format: :json }
        end.to change(TourOfDuty, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create tour_of_duty with invalid same name' do
        tour_of_duty
        post :create, params: { tour_of_duty: params.merge(name: tour_of_duty.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create tour_of_duty' do
        post :create, params: { tour_of_duty: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'tour_of_duty' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update tour_of_duty with valid params' do
        patch :update, params: { id: tour_of_duty.id, tour_of_duty: { name: 'tour_of_duty' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('tour_of_duty')
        success_response
      end

      it 'Should update tour_of_duty with valid params slug' do
        patch :update, params: { id: tour_of_duty.slug, tour_of_duty: { name: 'tour_of_duty' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('tour_of_duty')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update tour_of_duty with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'tour_of_duties', fields: validation_columns)
        patch :update, params: { id: tour_of_duty.id, tour_of_duty: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update tour_of_duty with empty description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'tour_of_duties', fields: validation_columns)
        patch :update, params: { id: tour_of_duty.id, tour_of_duty: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check tour_of_duty validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'tour_of_duties', fields: validation_columns)
        patch :update, params: { id: tour_of_duty.id, tour_of_duty: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check tour_of_duty validation if required fields not present' do
        modify_account_schema_validations(model: 'tour_of_duties', action: 'clear')
        patch :update, params: { id: tour_of_duty.id, tour_of_duty: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update tour_of_duty with invalid same name' do
        patch :update, params: { id: Fabricate(:tour_of_duty).id, tour_of_duty: { name: tour_of_duty.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update tour_of_duty with blank id' do
        patch :update, params: { id: '', tour_of_duty: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update tour_of_duty' do
        patch :update, params: { id: tour_of_duty.id, tour_of_duty: { name: 'tour_of_duty' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'tour_of_duty' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete tour_of_duty with valid params slug' do
        delete :destroy, params: { id: tour_of_duty.slug, format: :json }
        expect(assigns(:tour_of_duty).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete tour_of_duty with valid params id' do
        delete :destroy, params: { id: tour_of_duty.id, format: :json }
        expect(assigns(:tour_of_duty).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete tour_of_duty with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete tour_of_duty with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete tour_of_duty' do
        delete :destroy, params: { id: tour_of_duty.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
