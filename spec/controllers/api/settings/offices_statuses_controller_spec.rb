# frozen_string_literal: true

RSpec.describe Api::Settings::OfficerStatusesController do
  let(:officer_status) { Fabricate(:officer_status) }
  let(:params) { Fabricate.attributes_for(:officer_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :officer_status)
      end

      it 'Respond officer statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of officer_status page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of officer_status page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of per page limit' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond office statuses list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of office statuses ordered by name ascending" do
        sort_office_statuses = OfficerStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_office_statuses.ids)
      end
    end

    context 'Office status list - Search by name with authenticated user', authenticated: true do
      it 'Filters the office statuses by name' do
        Fabricate.times(5, :officer_status)
        get :index, params: { search_text: officer_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :officer_status)
      end

      it 'Respond officer statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of officer_status page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of officer_status page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of per page limit' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond office statuses list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of office statuses ordered by name ascending" do
        sort_office_statuses = OfficerStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_office_statuses.ids)
      end
    end

    context 'Office status list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the office statuses by name' do
        Fabricate.times(5, :officer_status)
        get :index, params: { search_text: officer_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create office status with valid params' do
        expect { post :create, params: { officer_status: params, format: :json } }.to change(OfficerStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create office status with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'officer_statuses', fields: validation_columns)
        post :create, params: { officer_status: params.merge(name: ''), format: :json }
        expect(error_response).to eq([{ 'name' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should not create office status with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'officer_statuses', fields: validation_columns)
        post :create, params: { officer_status: params.merge(description: ''), format: :json }
        expect(error_response).to eq([{ 'description' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should check office statues validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'officer_statuses', fields: validation_columns)
        post :create, params: { officer_status: params.merge(name: '', description: ''), format: :json }
        expect(error_response).to eq([{ 'name' => ["can't be blank"], 'description' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should return 200 - Should not check office statues validation if required fields not present' do
        modify_account_schema_validations(model: 'officer_statuses', action: 'clear')
        expect do
          post :create, params: { officer_status: params.merge(name: '', description: ''), format: :json }
        end.to change(OfficerStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create office status with invalid same name' do
        officer_status
        post :create, params: { officer_status: params.merge(name: officer_status.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create office status' do
        post :create, params: { officer_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update office status with valid params' do
        patch :update, params: { id: officer_status.id, officer_status: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end

      it 'Should update office status with valid params slug' do
        patch :update, params: { id: officer_status.slug, officer_status: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update office status with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'officer_statuses', fields: validation_columns)
        patch :update, params: { id: officer_status.id, officer_status: params.merge(name: ''), format: :json }
        expect(error_response).to eq([{ 'name' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should not update office status with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'officer_statuses', fields: validation_columns)
        patch :update, params: { id: officer_status.id, officer_status: params.merge(description: ''), format: :json }
        expect(error_response).to eq([{ 'description' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should return 200 - Should not check office statues validation if required fields not present' do
        modify_account_schema_validations(model: 'officer_statuses', action: 'clear')
        patch :update, params: { id: officer_status.id, officer_status: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update office status with invalid same name' do
        patch :update, params: { id: Fabricate(:officer_status).id, officer_status: params.merge(name: officer_status.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update office status with blank id' do
        patch :update, params: { id: '', officer_status: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update office status' do
        patch :update, params: { id: officer_status.id, officer_status: { name: 'sample' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete office status with valid params slug' do
        delete :destroy, params: { id: officer_status.slug, format: :json }
        expect(assigns(:officer_status).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete office status with valid params id' do
        delete :destroy, params: { id: officer_status.id, format: :json }
        expect(assigns(:officer_status).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete office status with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete office status' do
        delete :destroy, params: { id: officer_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
