# frozen_string_literal: true

RSpec.describe Api::Settings::MeetingTypesController do
  let(:meeting_type) { Fabricate(:meeting_type) }
  let(:params) { Fabricate.attributes_for(:meeting_type) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :meeting_type)
      end

      it 'Respond meeting types list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of meeting_type page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of meeting_type page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond meeting_type list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond meeting type list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of meeting types ordered by name ascending" do
        sort_meeting_types = MeetingType.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_meeting_types.ids)
      end
    end

    context 'Meeting type list - Search by name with authenticated user', authenticated: true do
      it 'Filters the meeting types by name' do
        Fabricate.times(5, :meeting_type)
        get :index, params: { search_text: meeting_type.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :meeting_type)
      end

      it 'Respond meeting types list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of meeting_type page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of meeting_type page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond meeting_type list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond meeting type list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of meeting types ordered by name ascending" do
        sort_meeting_types = MeetingType.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_meeting_types.ids)
      end
    end

    context 'Meeting type list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the meeting types by name' do
        Fabricate.times(5, :meeting_type)
        get :index, params: { search_text: meeting_type.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create meeting type with valid params' do
        expect { post :create, params: { meeting_type: params, format: :json } }.to change(MeetingType, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create meeting type with empty params name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'meeting_types', fields: validation_columns)
        post :create, params: { meeting_type: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create meeting type with empty params description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'meeting_types', fields: validation_columns)
        post :create, params: { meeting_type: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check meeting type validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'meeting_types', fields: validation_columns)
        post :create, params: { meeting_type: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check meeting type validation if required fields not present' do
        modify_account_schema_validations(model: 'meeting_types', action: 'clear')
        expect do
          post :create, params: { meeting_type: params.merge(name: '', description: ''), format: :json }
        end.to change(MeetingType, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create meeting type with invalid same name' do
        meeting_type
        post :create, params: { meeting_type: params.merge(name: meeting_type.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create meeting type' do
        patch :create, params: { meeting_type: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update meeting type with valid params' do
        patch :update, params: { id: meeting_type.id, meeting_type: { name: 'meeting_types' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('meeting_types')
        success_response
      end

      it 'Should update meeting type with valid params slug' do
        patch :update, params: { id: meeting_type.slug, meeting_type: { name: 'meeting_types' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('meeting_types')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update meeting type with empty params name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'meeting_types', fields: validation_columns)
        patch :update, params: { id: meeting_type.id, meeting_type: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update meeting type with empty params description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'meeting_types', fields: validation_columns)
        patch :update, params: { id: meeting_type.id, meeting_type: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check meeting type validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'meeting_types', fields: validation_columns)
        patch :update, params: { id: meeting_type.id, meeting_type: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check meeting type validation if required fields not present' do
        modify_account_schema_validations(model: 'meeting_types', action: 'clear')
        patch :update, params: { id: meeting_type.id, meeting_type: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update meeting type with blank id' do
        patch :update, params: { id: '', meeting_type: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'Should not update meeting type with invalid same name' do
        meeting_type1 = Fabricate(:meeting_type)
        patch :update, params: { id: meeting_type1.id, meeting_type: params.merge(name: meeting_type.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update meeting type' do
        patch :update, params: { id: meeting_type.id, meeting_type: { name: 'meeting_types' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete meeting type with valid params slug' do
        delete :destroy, params: { id: meeting_type.slug, format: :json }
        expect(assigns(:meeting_type).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete meeting type with valid params id' do
        delete :destroy, params: { id: meeting_type.id, format: :json }
        expect(assigns(:meeting_type).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete meeting type and its related associations' do
        Fabricate(:employee_meeting_type, meeting_type: meeting_type)
        delete :destroy, params: { id: meeting_type.slug, format: :json }
        expect(assigns(:meeting_type).discarded_at).not_to eq(nil)
        expect(meeting_type.employee_meeting_types.first.reload.discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete meeting type with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete meeting type with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete meeting type' do
        delete :destroy, params: { id: meeting_type.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
