# frozen_string_literal: true

RSpec.describe Api::Settings::DelegateSeriesController do
  let(:delegate_series) { Fabricate(:delegate_series) }
  let(:params) { Fabricate.attributes_for(:delegate_series) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'delegate_series' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :delegate_series)
      end

      it 'Respond delegate_ series list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of delegate_ series page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of delegate_ series page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond delegate_ series list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond delegate_ series list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end
    end

    context 'Delegate series list - Search by name with authenticated user', authenticated: true do
      it 'Filters the delegate_series by name' do
        Fabricate.times(5, :delegate_series)
        get :index, params: { search_text: delegate_series.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :delegate_series)
      end

      it 'Respond delegate series list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of delegate_ series page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of delegate_ series page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond delegate_ series list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond delegate_ series list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end
    end

    context 'Delegate series list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the delegate_series by name' do
        Fabricate.times(5, :delegate_series)
        get :index, params: { search_text: delegate_series.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'delegate_series' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create delegate_ series with valid params' do
        expect { post :create, params: { delegate_series: params, format: :json } }.to change(DelegateSeries, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create delegate_ series with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'delegate_series', fields: validation_columns)
        post :create, params: { delegate_series: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create delegate_ series with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'delegate_series', fields: validation_columns)
        post :create, params: { delegate_series: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check delegate_ series validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'delegate_series', fields: validation_columns)
        post :create, params: { delegate_series: params.merge(description: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check delegate_ series validation if required fields not present' do
        modify_account_schema_validations(model: 'delegate_series', action: 'clear')
        expect do
          post :create, params: { delegate_series: params.merge(name: '', description: ''), format: :json }
        end.to change(DelegateSeries, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create delegate_ series with invalid same name' do
        post :create, params: { delegate_series: { name: delegate_series.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create delegate series' do
        post :create, params: { delegate_series: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'delegate_series' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update delegate_ series with valid params id' do
        patch :update, params: { id: delegate_series.id, delegate_series: { name: 'delegate_ series' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('delegate_ series')
        success_response
      end

      it 'Should update delegate_ series with valid params slug' do
        patch :update, params: { id: delegate_series.slug, delegate_series: { name: 'delegate_ series' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('delegate_ series')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update delegate_ series with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'delegate_series', fields: validation_columns)
        patch :update, params: { id: delegate_series.id, delegate_series: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update delegate_ series with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'delegate_series', fields: validation_columns)
        patch :update, params: { id: delegate_series.id, delegate_series: params.merge(description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check delegate_series validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'delegate_series', fields: validation_columns)
        patch :update, params: { id: delegate_series.id, delegate_series: params.merge(name: '', description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check delegate_series validation if required fields not present' do
        modify_account_schema_validations(model: 'delegate_series', action: 'clear')
        patch :update, params: { id: delegate_series.id, delegate_series: params.merge(name: '', description: ''),
                                 format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update delegate_ series with same name' do
        patch :update, params: { id: Fabricate(:delegate_series).id, delegate_series: { name: delegate_series.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update delegate_ series with blank id' do
        patch :update, params: { id: '', delegate_series: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update delegate series' do
        patch :update, params: { id: delegate_series.id, delegate_series: { name: 'delegate_ series' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'delegate_series' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete delegate_ series with valid params slug' do
        delete :destroy, params: { id: delegate_series.slug, format: :json }
        expect(assigns(:delegate_series).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete delegate_ series with valid params id' do
        delete :destroy, params: { id: delegate_series.id, format: :json }
        expect(assigns(:delegate_series).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete delegate_ series with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete delegate_ series  with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete delegate series' do
        delete :destroy, params: { id: delegate_series.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
