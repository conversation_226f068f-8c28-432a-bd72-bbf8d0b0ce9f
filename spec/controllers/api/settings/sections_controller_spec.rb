# frozen_string_literal: true

RSpec.describe Api::Settings::SectionsController do
  let(:department) { Fabricate(:department) }
  let(:section) { Fabricate(:section) }
  let(:params) { Fabricate.attributes_for(:section) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'section' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :section)
      end

      it 'Respond sections list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of section page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of section page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond section list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond section list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of sections ordered by name ascending" do
        sort_sections = Section.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_sections.ids)
      end
    end

    context 'Section list - Search by name with authenticated user', authenticated: true do
      it 'Filters the sections by name' do
        Fabricate.times(5, :section)
        get :index, params: { search_text: section.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :section)
      end

      it 'Respond sections list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of section page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of section page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond section list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond section list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of sections ordered by name ascending" do
        sort_sections = Section.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_sections.ids)
      end
    end

    context 'Section list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the sections by name' do
        Fabricate.times(5, :section)
        get :index, params: { search_text: section.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'section' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create section with valid params' do
        expect do
          post :create, params: { section: { name: 'sample', department_id: department.id }, format: :json }
        end.to change(Section, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create section with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        post :create, params: { department_id: department.id, section: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create section with empty params phone' do
        validation_columns = ['phone']
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        post :create, params: { department_id: department.id, section: params.merge(phone: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create section with empty params notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        post :create, params: { department_id: department.id, section: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check section validation if required fields present' do
        validation_columns = %w[name phone notes]
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        post :create, params: { department_id: department.id, section: params.merge(name: '', phone: '', notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check section validation if required fields not present' do
        modify_account_schema_validations(model: 'sections', action: 'clear')
        expect do
          post :create, params: { section: params.merge(name: '', phone: '', notes: ''), format: :json }
        end.to change(Section, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['phone']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create section with invalid same name' do
        section = Fabricate(:section)
        post :create, params: { section: params.merge(name: section.name, department_id: section.department_id), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create section' do
        post :create, params: { section: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'section' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update section with valid params' do
        patch :update, params: { department_id: section.department.id, id: section.id, section: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end

      it 'Should update section with valid params slug' do
        patch :update, params: { department_id: section.department.slug, id: section.id, section: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update section with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        patch :update, params: { department_id: section.department.id, id: section.id, section: { name: '' },
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update section with empty params phone' do
        validation_columns = ['phone']
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        patch :update, params: { department_id: section.department.id, id: section.id, section: { phone: '' },
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update section with empty params notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        patch :update, params: { department_id: section.department.id, id: section.id, section: { notes: '' },
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check section validation if required fields present' do
        validation_columns = %w[name phone notes]
        modify_account_schema_validations(model: 'sections', fields: validation_columns)
        patch :update, params: { department_id: section.department.id, id: section.id, section: { notes: '', name: '', phone: '' },
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check section validation if required fields not present' do
        modify_account_schema_validations(model: 'sections', action: 'clear')
        patch :update, params: { id: section.id, section: params.merge(name: '', notes: '', phone: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['phone']).to eq('')
        expect(parsed_response['attributes']['notes']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update section with invalid same name' do
        Fabricate(:section, department: department, name: 'new_name')
        section2 = Fabricate(:section, department: department)
        patch :update, params: { id: section2.id, section: params.merge(name: 'new_name', department_id: section2.department_id),
                                 format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update section with blank id' do
        patch :update, params: { department_id: section.department.id, id: '', section: '',
                                 format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'Should not update section with blank id' do
        patch :update, params: { department_id: '', id: '', section: params,
                                 format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update section' do
        patch :update, params: { department_id: section.department.id, id: section.id, section: { name: 'sample' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'section' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete section with valid params slug' do
        delete :destroy, params: { id: section.slug, format: :json }
        expect(assigns(:section).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete section with valid params id' do
        delete :destroy, params: { id: section.id, format: :json }
        expect(assigns(:section).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete section with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete section with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete section' do
        delete :destroy, params: { id: section.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
