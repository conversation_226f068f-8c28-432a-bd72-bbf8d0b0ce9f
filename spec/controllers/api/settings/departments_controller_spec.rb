# frozen_string_literal: true

RSpec.describe Api::Settings::DepartmentsController do
  let(:department) { Fabricate(:department) }
  let(:params) { Fabricate.attributes_for(:department) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'department' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :department)
      end

      it 'Respond departments list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of departments page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of departments page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond departments list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond departments list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of departments ordered by name ascending" do
        sort_departments = Department.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_departments.ids)
      end
    end

    context 'Departments list - Search by name with authenticated user', authenticated: true do
      it 'Filters the departments by name' do
        Fabricate.times(5, :department)
        get :index, params: { search_text: department.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :department)
      end

      it 'Respond departments list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of departments page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of departments page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond departments list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond departments list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of departments ordered by name ascending" do
        sort_departments = Department.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_departments.ids)
      end
    end

    context 'Departments list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the departments by name' do
        Fabricate.times(5, :department)
        get :index, params: { search_text: department.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'department' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Create department with valid params' do
        expect { post :create, params: { department: params, format: :json } }.to change(Department, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create department with same name' do
        department
        post :create, params: { department: params.merge(name: department.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    # The validations are dynamically added based on the account preference(saas_json column of accounts will
    # contain the customizations like validations/renaming/reordering of fields)
    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create department with with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        post :create, params: { department: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create department with with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        post :create, params: { department: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create department with with blank phone' do
        validation_columns = ['phone']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        post :create, params: { department: params.merge(phone: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create department with with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        post :create, params: { department: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check department validation if required fields present' do
        validation_columns = %w[name phone notes address]
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        post :create, params: { department: params.merge(name: '', address: '', notes: '', phone: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check department validation if required fields not present' do
        modify_account_schema_validations(model: 'departments', action: 'clear')
        expect do
          post :create, params: { department: params.merge(name: '', phone: '', address: '', notes: ''), format: :json }
        end.to change(Department, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['phone']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['notes']).to eq('')
        success_response
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create department' do
        post :create, params: { department: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'department' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'should update department with valid params slug' do
        patch :update, params: { id: department.slug, department: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end

      it 'should update department with valid params' do
        patch :update, params: { id: department.id, department: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update department with with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        patch :update, params: { id: department.slug, department: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update department with with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        patch :update, params: { id: department.slug, department: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update department with with blank phone' do
        validation_columns = ['phone']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        patch :update, params: { id: department.slug, department: params.merge(phone: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update department with with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        patch :update, params: { id: department.slug, department: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check department validation if required fields present' do
        validation_columns = %w[name phone notes address]
        modify_account_schema_validations(model: 'departments', fields: validation_columns)
        patch :update, params: { id: department.slug, department: params.merge(name: '', phone: '', address: '',
                                                                               notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check department validation if required fields not present' do
        modify_account_schema_validations(model: 'departments', action: 'clear')
        patch :update, params: { id: department.id, department: params.merge(name: '', address: '', phone: '',
                                                                             notes: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['phone']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['notes']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update department with with blank id' do
        patch :update, params: { id: '', department: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'Should not update department with same name' do
        patch :update, params: { id: Fabricate(:department).id, department: { name: department.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update affiliation' do
        patch :update, params: { id: department.slug, department: { name: 'sample' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'department' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Delete department with valid params slug' do
        delete :destroy, params: { id: department.slug, format: :json }
        expect(assigns(:department).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete department with valid params id' do
        delete :destroy, params: { id: department.id, format: :json }
        expect(assigns(:department).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'delete department  with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete department  with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete affiliation' do
        delete :destroy, params: { id: department.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
