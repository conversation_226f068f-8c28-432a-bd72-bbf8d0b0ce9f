# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeEmploymentStatusesController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_employment_status) { Fabricate(:employee_employment_status) }
  let(:params) { Fabricate.attributes_for(:employee_employment_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'employee',
                                                                                   object_name: 'employee_employment_status',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_employment_status, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond employee employment status list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee employment status page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond employee employment status list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee employment status page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { rights: 'employee', object_name: 'employee_employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee employment status with valid params' do
        expect { post :create, params: { employee_employment_status: params, format: :json } }.to change(EmployeeEmploymentStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end

      it 'creates employee benefits against the first employee if the status is active or retired' do
        Fabricate(:benefit, name: "Optical")
        Fabricate(:benefit, name: "Dental")
        Fabricate(:benefit, name: "Prescription")
        employment_status = Fabricate(:employment_status, name: 'Active')
        employee_benefits_count = EmployeeBenefit.count
        post :create, params: {employee_employment_status: params.merge(end_date: '', employment_status_id: employment_status.id), format: :json}
        expect(EmployeeBenefit.count).to eq(employee_benefits_count + 3)
      end

      it 'creates employee benefits(avoid duplication) against the first employee if the status is active or retired' do
        Fabricate(:benefit, name: "Optical")
        Fabricate(:benefit, name: "Dental")
        Fabricate(:benefit, name: "Prescription")
        Fabricate(:employee_benefit, benefit: Benefit.first, employee: Employee.first, end_date: '')
        employment_status = Fabricate(:employment_status, name: 'Active')
        employee_benefits_count = EmployeeBenefit.count
        post :create, params: {employee_employment_status: params.merge(end_date: '', employment_status_id: employment_status.id), format: :json}
        expect(EmployeeBenefit.count).to eq(employee_benefits_count + 2)
      end

      it 'updates expire date for benefit coverages against the first employee if the status is Deceased' do
        Fabricate(:benefit_coverage, employee: Employee.first, expires_at: '')
        employment_status = Fabricate(:employment_status, name: 'Deceased')
        post :create, params: {employee_employment_status: params.merge(end_date: '', employment_status_id: employment_status.id), format: :json}
        coverage_expire_date = Employee.first.benefit_coverages.last.expires_at
        expect(coverage_expire_date).to eq(parsed_response['attributes']['start_date'].to_date)
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee employment status with blank employment status' do
        validation_columns = ['employment_status_id']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        post :create, params: { employee_employment_status: params.merge(employment_status_id: ''), format: :json }
        expect(error_response[0]).to include({ employment_status: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee employment status with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        post :create, params: { employee_employment_status: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee employment status with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        post :create, params: { employee_employment_status: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee employment status with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        post :create, params: { employee_employment_status: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee employment status validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        post :create, params: { employee_employment_status: params.merge(notes: '', start_date: '', end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee employment status validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_employment_statuses', action: 'clear')
        expect do
          post :create, params: { employee_employment_status: params.merge(start_date: '', end_date: ''), format: :json }
        end.to change(EmployeeEmploymentStatus, :count).by(1)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end

      it 'creates employment_status without any error if employment status name is in upcase' do
        modify_account_schema_validations(model: 'employee_employment_statuses', action: 'clear')
        Fabricate(:employee_employment_status, end_date: '', employee: Employee.first)
        EmploymentStatus.last.update_attributes(name: 'ACTIVE')

        post :create, params: { employee_employment_status: params.merge(start_date: '', end_date: ''), format: :json }

        expect(response).to have_http_status(:success)
      end

      it 'updates the end_date for active_status records against the first employee' do
        modify_account_schema_validations(model: 'employee_employment_statuses', action: 'clear')

        Fabricate(:employment_status, name: 'active')
        Fabricate(:employee_employment_status, employment_status: EmploymentStatus.last, end_date: '', employee: Employee.first)
        employee_position = Fabricate(:employee_position, end_date: '', employee: Employee.first)
        employee_office = Fabricate(:employee_office, end_date: '', employee: Employee.first)
        employee_rank = Fabricate(:employee_rank, end_date: '', employee: Employee.first)

        post :create, params: { employee_employment_status: params.merge(end_date: ''), format: :json }

        expect(employee_position.reload.end_date).not_to eq(nil)
        expect(employee_office.reload.end_date).not_to eq(nil)
        expect(employee_rank.reload.end_date).not_to eq(nil)
      end

      it 'not updates the end_date for active_status records if start_date is greater than end_date' do
        modify_account_schema_validations(model: 'employee_employment_statuses', action: 'clear')

        Fabricate(:employee_employment_status, start_date: Time.current + 20.days, end_date: '', employee: Employee.first)
        employee_position = Fabricate(:employee_position, start_date: Time.current + 20.days, end_date: '', employee: Employee.first)
        employee_office = Fabricate(:employee_office, start_date: Time.current + 20.days, end_date: '', employee: Employee.first)
        employee_rank = Fabricate(:employee_rank, start_date: Time.current + 20.days, end_date: '', employee: Employee.first)

        post :create, params: { employee_employment_status: params.merge(end_date: ''), format: :json }

        expect(employee_position.reload.end_date).to eq(nil)
        expect(employee_office.reload.end_date).to eq(nil)
        expect(employee_rank.reload.end_date).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create employee employment status with invalid date range' do
        post :create, params: { employee_employment_status: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee employment status' do
        post :create, params: { employee_employment_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { rights: 'employee', object_name: 'employee_employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee employment status with valid params' do
        employee_employment_status1 = Fabricate(:employee_employment_status)
        patch :update, params: { id: employee_employment_status1.id, employee_employment_status: params, format: :json }
        expect(parsed_response['attributes']['employment_status_id']).to eq(params[:employment_status_id])
        success_response
      end

      it 'creates employee benefits against the first employee update the status is active or retired' do
        employee_employment_status1 = Fabricate(:employee_employment_status)

        Fabricate(:benefit, name: "Optical")
        Fabricate(:benefit, name: "Dental")
        Fabricate(:benefit, name: "Prescription")
        employment_status = Fabricate(:employment_status, name: 'Active')
        employee_benefits_count = EmployeeBenefit.count
        patch :update, params: { id: employee_employment_status1.id, employee_employment_status: params.merge(employment_status_id: employment_status.id), format: :json }
        expect(EmployeeBenefit.count).to eq(employee_benefits_count + 3)
      end

      it 'creates employee benefits(avoid duplication) against the first employee update the status is active or retired' do
        employee_employment_status1 = Fabricate(:employee_employment_status)

        Fabricate(:benefit, name: "Optical")
        Fabricate(:benefit, name: "Dental")
        Fabricate(:benefit, name: "Prescription")
        Fabricate(:employee_benefit, benefit: Benefit.first, employee: Employee.first, end_date: '')
        employment_status = Fabricate(:employment_status, name: 'Active')
        employee_benefits_count = EmployeeBenefit.count
        patch :update, params: { id: employee_employment_status1.id, employee_employment_status: params.merge(employment_status_id: employment_status.id), format: :json }
        expect(EmployeeBenefit.count).to eq(employee_benefits_count + 2)
      end

      it 'updates expire date for benefit coverages against the first employee update the status is Deceased' do
        employee_employment_status1 = Fabricate(:employee_employment_status)

        Fabricate(:benefit_coverage, employee: Employee.first, expires_at: '')
        employment_status = Fabricate(:employment_status, name: 'Deceased')
        patch :update, params: { id: employee_employment_status1.id, employee_employment_status: params.merge(end_date: '', employment_status_id: employment_status.id), format: :json }
        coverage_expire_date = Employee.first.benefit_coverages.last.expires_at
        expect(coverage_expire_date).to eq(parsed_response['attributes']['start_date'].to_date)
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee employment status with blank Employment status' do
        validation_columns = ['employment_status_id']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        patch :update, params: { employee_employment_status: { employment_status_id: '' }, id: employee_employment_status.id, format: :json }
        expect(error_response[0]).to include({ employment_status: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee employment status with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        patch :update, params: { employee_employment_status: { employee_id: '' }, id: employee_employment_status.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee employment status with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        patch :update, params: { id: employee_employment_status.id, employee_employment_status: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee employment status with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        patch :update, params: { id: employee_employment_status.id, employee_employment_status: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee employment status validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_employment_statuses', fields: validation_columns)
        patch :update, params: { id: employee_employment_status.id, employee_employment_status: { start_date: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee employment status validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_employment_statuses', action: 'clear')
        patch :update, params: { id: employee_employment_status.id, employee_employment_status: { start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee employment status with invalid date range' do
        patch :update, params: { id: employee_employment_status.id, employee_employment_status: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee employment status' do
        employee_employment_status1 = Fabricate(:employee_employment_status)
        patch :update, params: { id: employee_employment_status1.id, employee_employment_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { rights: 'employee', object_name: 'employee_employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee employment status with valid params' do
        delete :destroy, params: { id: employee_employment_status.id, format: :json }
        expect(assigns(:employee_employment_status).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee employment status with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee employment status' do
        delete :destroy, params: { id: employee_employment_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
