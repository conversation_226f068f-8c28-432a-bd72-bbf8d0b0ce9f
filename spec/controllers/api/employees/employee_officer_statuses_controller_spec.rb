# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeOfficerStatusesController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_officer_status) { Fabricate(:employee_officer_status) }
  let(:params) { Fabricate.attributes_for(:employee_officer_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'employee',
                                                                                   object_name: 'employee_officer_status',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_officer_status, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of employee officer status' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee officer status page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of employee officer status' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee officer status page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { rights: 'employee', object_name: 'employee_officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee officer status with valid params' do
        expect { post :create, params: { employee_officer_status: params, format: :json } }.to change(EmployeeOfficerStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee officer status with blank officer status' do
        validation_columns = ['officer_status_id']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        post :create, params: { employee_officer_status: params.merge(officer_status_id: ''), format: :json }
        expect(error_response[0]).to include({ officer_status: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee officer status with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        post :create, params: { employee_officer_status: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee officer status with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        post :create, params: { employee_officer_status: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee officer status with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        post :create, params: { employee_officer_status: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee officer status validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        post :create, params: { employee_officer_status: params.merge(start_date: '', end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee officer status validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_officer_statuses', action: 'clear')
        expect do
          post :create, params: { employee_officer_status: params.merge(start_date: '', end_date: ''), format: :json }
        end.to change(EmployeeOfficerStatus, :count).by(1)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create employee officer status with invalid date range' do
        post :create, params: { employee_officer_status: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee officer status' do
        post :create, params: { employee_officer_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { rights: 'employee', object_name: 'employee_officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee officer status with valid params' do
        employee_officer_status1 = Fabricate(:employee_officer_status)
        patch :update, params: { id: employee_officer_status1.id, employee_officer_status: params, format: :json }
        expect(parsed_response['attributes']['officer_status_id']).to eq(params[:officer_status_id])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee officer status with blank officer status' do
        validation_columns = ['officer_status_id']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        patch :update, params: { employee_officer_status: { officer_status_id: '' }, id: employee_officer_status.id, format: :json }
        expect(error_response[0]).to include({ officer_status: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee officer status with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        patch :update, params: { employee_officer_status: { employee_id: '' }, id: employee_officer_status.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee officer status with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        patch :update, params: { id: employee_officer_status.id, employee_officer_status: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee officer status with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        patch :update, params: { id: employee_officer_status.id, employee_officer_status: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee officer status validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_officer_statuses', fields: validation_columns)
        patch :update, params: { id: employee_officer_status.id, employee_officer_status: { start_date: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee officer status validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_officer_statuses', action: 'clear')
        patch :update, params: { id: employee_officer_status.id, employee_officer_status: { start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee officer status with invalid date range' do
        patch :update, params: { id: employee_officer_status.id, employee_officer_status: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee officer status' do
        employee_officer_status1 = Fabricate(:employee_officer_status)
        patch :update, params: { id: employee_officer_status1.id, employee_officer_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { rights: 'employee', object_name: 'employee_officer_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee officer status with valid params' do
        delete :destroy, params: { id: employee_officer_status.id, format: :json }
        expect(assigns(:employee_officer_status).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee officer status with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee officer status' do
        delete :destroy, params: { id: employee_officer_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
