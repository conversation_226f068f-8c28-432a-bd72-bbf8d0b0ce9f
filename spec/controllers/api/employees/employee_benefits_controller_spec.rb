# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeBenefitsController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_benefit) { Fabricate(:employee_benefit) }
  let(:params) { Fabricate.attributes_for(:employee_benefit) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'employee_benefit',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_benefit, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond employee benefit list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee benefit page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond employee benefit list with custom pagination count specified(per_page count)' do
        get :index, params: { employee_id: employee.id, per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond employee benefit list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee benefit page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond employee benefit list with custom pagination count specified(per_page count)' do
        get :index, params: { employee_id: employee.id, per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee benefit with valid params' do
        expect { post :create, params: { employee_benefit: params, format: :json } }.to change(EmployeeBenefit, :count).by(1)
        expect(parsed_response['attributes']['description']).to eq(params['description'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee benefit with blank benefit' do
        validation_columns = ['benefit_id']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        post :create, params: { employee_benefit: params.merge(benefit_id: ''), format: :json }
        expect(error_response[0]).to include({ benefit: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee benefit with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        post :create, params: { employee_benefit: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee benefit with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        post :create, params: { employee_benefit: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee benefit with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        post :create, params: { employee_benefit: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee benefit with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        post :create, params: { employee_benefit: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee benefit validation if required fields present' do
        validation_columns = %w[description start_date end_date]
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        post :create, params: { employee_benefit: params.merge(description: '', start_date: '', end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee benefit validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_benefits', action: 'clear')
        expect do
          post :create, params: { employee_benefit: params.merge(description: '', start_date: '', end_date: ''), format: :json }
        end.to change(EmployeeBenefit, :count).by(1)
        expect(parsed_response['attributes']['description']).to eq('')
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create employee benefit with invalid date range' do
        post :create, params: { employee_benefit: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee benefit' do
        post :create, params: { employee_benefit: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee benefit with valid params' do
        employee_benefit1 = Fabricate(:employee_benefit)
        patch :update, params: { id: employee_benefit1.id, employee_benefit: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['description']).to eq(params[:description])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee benefit with blank Benefit' do
        validation_columns = ['benefit_id']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        patch :update, params: { employee_benefit: { benefit_id: '' }, id: employee_benefit.id, format: :json }
        expect(error_response[0]).to include({ benefit: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee benefit with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        patch :update, params: { employee_benefit: { employee_id: '' }, id: employee_benefit.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee benefit with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        patch :update, params: { id: employee_benefit.id, employee_benefit: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee benefit with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        patch :update, params: { id: employee_benefit.id, employee_benefit: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee benefit with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        patch :update, params: { id: employee_benefit.id, employee_benefit: { description: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee benefit validation if required fields present' do
        validation_columns = %w[description start_date end_date]
        modify_account_schema_validations(model: 'employee_benefits', fields: validation_columns)
        patch :update, params: { id: employee_benefit.id, employee_benefit: { start_date: '', description: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee benefit validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_benefits', action: 'clear')
        patch :update, params: { id: employee_benefit.id, employee_benefit: { description: '', start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['description']).to eq('')
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee benefit with invalid date range' do
        patch :update, params: { id: employee_benefit.id, employee_benefit: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee benefit' do
        employee_benefit1 = Fabricate(:employee_benefit)
        patch :update, params: { id: employee_benefit1.id, employee_benefit: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee benefit with valid params' do
        delete :destroy, params: { id: employee_benefit.id, format: :json }
        expect(assigns(:employee_benefit).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee benefit with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee benefit' do
        delete :destroy, params: { id: employee_benefit.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
