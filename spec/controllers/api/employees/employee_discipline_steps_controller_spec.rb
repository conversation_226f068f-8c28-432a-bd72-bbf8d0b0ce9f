# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeDisciplineStepsController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_discipline_step) { Fabricate(:employee_discipline_step) }
  let(:params) { Fabricate.attributes_for(:employee_discipline_step) }

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employee_discipline_step',
                                                                                     rights: 'employee_discipline_setting' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee discipline_step with valid params' do
        expect do
          post :create, params: { employee_discipline_step: params, format: :json }
        end.to change(EmployeeDisciplineStep, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee discipline_step with blank discipline_setting' do
        validation_columns = ['employee_discipline_setting_id']
        modify_account_schema_validations(model: 'employee_discipline_steps', fields: validation_columns)
        post :create, params: { employee_discipline_step: params.merge(employee_discipline_setting_id: ''), format: :json }
        expect(error_response[0]).to include({ employee_discipline_setting: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee discipline_step with blank discipline_status' do
        validation_columns = ['discipline_status_id']
        modify_account_schema_validations(model: 'employee_discipline_steps', fields: validation_columns)
        post :create, params: { employee_discipline_step: params.merge(discipline_status_id: ''), format: :json }
        expect(error_response[0]).to include({ discipline_status_id: ['can\'t be blank'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee discipline_step with blank step' do
        validation_columns = ['step']
        modify_account_schema_validations(model: 'employee_discipline_steps', fields: validation_columns)
        post :create, params: { employee_discipline_step: params.merge(step: ''), format: :json }
        expect(error_response[0]).to include({ step: ['can\'t be blank'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create two discipline steps for the same discipline and employee' do
        employee_discipline_setting = Fabricate(:employee_discipline_setting)
        # Create another step for same employee and discipline
        Fabricate(:employee_discipline_step, employee_discipline_setting: employee_discipline_setting)

        post :create, params: {
          employee_discipline_step: params.merge(employee_discipline_setting_id: employee_discipline_setting.id),
          format: :json }

        expect(error_response[0]).to include({ step: ["Same step already exists"] }.as_json)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee discipline_step' do
        post :create, params: { employee_discipline_step: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employee_discipline_step',
                                                                                      rights: 'employee_discipline_setting' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee discipline_step with valid params' do
        patch :update, params: { id: employee_discipline_step.id, employee_discipline_step: params, format: :json }
        expect(DateTime.parse(parsed_response['attributes']['date']).strftime("%Y-%m-%d %H:%M:%S")).to eq(params[:date])
        expect(parsed_response['attributes']['recommended_notes']).to eq(params[:recommended_notes])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee discipline_step with blank discipline_setting' do
        validation_columns = ['employee_discipline_setting_id']
        modify_account_schema_validations(model: 'employee_discipline_steps', fields: validation_columns)
        patch :update, params: { id: employee_discipline_step.id, employee_discipline_step: params.merge(employee_discipline_setting_id: ''), format: :json }
        expect(error_response[0]).to include({ employee_discipline_setting: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee discipline_step with blank discipline_status' do
        validation_columns = ['discipline_status_id']
        modify_account_schema_validations(model: 'employee_discipline_steps', fields: validation_columns)
        post :create, params: { employee_discipline_step: params.merge(discipline_status_id: ''), format: :json }
        expect(error_response[0]).to include({ discipline_status_id: ['can\'t be blank'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee discipline_step with blank step' do
        validation_columns = ['step']
        modify_account_schema_validations(model: 'employee_discipline_steps', fields: validation_columns)
        post :create, params: { employee_discipline_step: params.merge(step: ''), format: :json }
        expect(error_response[0]).to include({ step: ['can\'t be blank'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create two discipline steps for the same discipline and employee' do
        employee_discipline_setting = Fabricate(:employee_discipline_setting)
        # Create another step for same employee and discipline
        Fabricate(:employee_discipline_step, employee_discipline_setting: employee_discipline_setting)

        post :create, params: {
          employee_discipline_step: params.merge(employee_discipline_setting_id: employee_discipline_setting.id),
          format: :json }

        expect(error_response[0]).to include({ step: ["Same step already exists"] }.as_json)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee discipline_step' do
        patch :update, params: { id: employee_discipline_step.id, employee_discipline_step: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
