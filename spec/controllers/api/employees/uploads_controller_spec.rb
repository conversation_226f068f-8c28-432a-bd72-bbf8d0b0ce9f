# frozen_string_literal: true

RSpec.describe Api::Employees::UploadsController do
  let(:employee) { Fabricate(:employee) }
  let(:upload) { Fabricate(:upload) }
  let(:params) { Fabricate.attributes_for(:upload) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'upload',
                                                                                   rights: 'employee_upload',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :upload, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of upload' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of upload page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Display list of upload' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of upload page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'upload', rights: 'employee_upload' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create upload with valid params' do
        expect { post :create, params: { upload: params, format: :json } }.to change(Upload, :count).by(1)
        expect(parsed_response['attributes']['file']['name']).to eq('test.pdf')
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not create upload with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'uploads', fields: validation_columns)
        post :create, params: { upload: params.merge(employee_id: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create upload with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'uploads', fields: validation_columns)
        post :create, params: { upload: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check upload validation if required fields not present' do
        modify_account_schema_validations(model: 'uploads', action: 'clear')
        expect do
          post :create, params: { upload: params.merge(notes: ''), format: :json }
        end.to change(Upload, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create upload' do
        post :create, params: { upload: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update',
                                           args: { object_name: 'upload', rights: 'employee_upload' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update upload with valid params' do
        patch :update, params: { id: upload.id, upload: { file: Rack::Test::UploadedFile.new(
          './spec/assets/sample.pdf',
          'application/pdf'
        ) }, format: :json }
        expect(parsed_response['attributes']['file']['name']).to eq('sample.pdf')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update upload with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'uploads', fields: validation_columns)
        patch :update, params: { id: upload.id, upload: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update upload with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'uploads', fields: validation_columns)
        patch :update, params: { id: upload.id, upload: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check upload validation if required fields not present' do
        modify_account_schema_validations(model: 'uploads', action: 'clear')
        patch :update, params: { id: upload.id, upload: { notes: '' }, format: :json }
        expect(parsed_response['attributes']['notes']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create upload' do
        patch :update, params: { id: upload.id, upload: { file: Rack::Test::UploadedFile.new(
            './spec/assets/sample.pdf',
            'application/pdf'
        ) }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy',
                                           args: { object_name: 'upload', rights: 'employee_upload' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete upload with valid params' do
        delete :destroy, params: { id: upload.id, format: :json }
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete upload with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete upload' do
        delete :destroy, params: { id: upload.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
