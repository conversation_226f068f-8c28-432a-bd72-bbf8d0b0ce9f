# frozen_string_literal: true

RSpec.describe Api::Employees::LodisController do
  let(:employee) { Fabricate(:employee) }
  let(:lodi) { Fabricate(:lodi) }
  let(:params) { Fabricate.attributes_for(:lodi) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'lodi',
                                                                                   rights: 'employee_analytics',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :lodi, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of lodi' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of lodi page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of lodis with specific lodi_type' do
        get :index, params: { employee_id: employee.id, lodi_type: 'lodi' }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Display list of lodi' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of lodi page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of lodis with specific lodi_type' do
        get :index, params: { employee_id: employee.id, lodi_type: 'lodi' }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'lodi', rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create lodi with valid params' do
        expect { post :create, params: { lodi: params, format: :json } }.to change(Lodi, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create lodi with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        post :create, params: { lodi: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create lodi with blank incident date' do
        validation_columns = ['incident_date']
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        post :create, params: { lodi: params.merge(incident_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create lodi with blank office' do
        validation_columns = ['office_id']
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        post :create, params: { lodi: params.merge(office_id: ''), format: :json }
        expect(error_response[0]).to include({office_id: ["can't be blank"]}.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should check lodi validation if required fields present' do
        validation_columns = %w[incident_date office_id employee_id]
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        post :create, params: { lodi: params.merge(office_id: '', incident_date: '', employee_id: ''), format: :json }
        expect(error_response[0]).to include({employee_id: ["can't be blank"], incident_date: ["can't be blank"], office_id: ["can't be blank"], employee: ["must exist"]}.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check lodi validation if required fields not present' do
        modify_account_schema_validations(model: 'lodis', action: 'clear')
        expect do
          post :create, params: { lodi: params.merge(incident_date: Date.today), format: :json }
        end.to change(Lodi, :count).by(1)
        expect(parsed_response['attributes']['incident_date']).to eq(Time.now.strftime('%Y-%m-%d'))
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create lodi with invalid date range' do
        post :create, params: { lodi: params.merge(return_date: Date.today, incident_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create lodi' do
        post :create, params: { lodi: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'lodi', rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update lodi with valid params' do
        lodi1 = Fabricate(:lodi)
        patch :update, params: { id: lodi1.id, lodi: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        expect(parsed_response['attributes']['office_id']).to eq(params['office_id'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update lodi with blank employee_id ' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        patch :update, params: { id: lodi.id, lodi: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update lodi with blank incident date' do
        validation_columns = ['incident_date']
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        patch :update, params: { id: lodi.id, lodi: { incident_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update lodi with blank office' do
        validation_columns = ['office_id']
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        patch :update, params: { id: lodi.id, lodi: { office_id: '' }, format: :json }
        expect(error_response[0]).to include({office_id: ["can't be blank"]}.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should check lodi validation if required fields present' do
        validation_columns = %w[incident_date office_id employee_id]
        modify_account_schema_validations(model: 'lodis', fields: validation_columns)
        patch :update, params: { id: lodi.id, lodi: params.merge(office_id: '', incident_date: '', employee_id: ''), format: :json }
        expect(error_response[0]).to include({employee_id: ["can't be blank"], incident_date: ["can't be blank"], office_id: ["can't be blank"], employee: ["must exist"]}.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check lodi validation if required fields not present' do
        modify_account_schema_validations(model: 'lodis', action: 'clear')
        patch :update, params: { id: lodi.id, lodi: params.merge(incident_date: Date.today), format: :json }
        expect(parsed_response['attributes']['incident_date']).to eq(Time.now.strftime('%Y-%m-%d'))
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update lodi with invalid date range' do
        patch :update, params: { id: lodi.id, lodi: params.merge(return_date: Date.today, incident_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update lodi' do
        lodi1 = Fabricate(:lodi)
        patch :update, params: { id: lodi1.id, lodi: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'lodi', rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete lodi with valid params' do
        delete :destroy, params: { id: lodi.id, format: :json }
        expect(assigns(:lodi).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete lodi with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete lodi' do
        delete :destroy, params: { id: lodi.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
