require 'rails_helper'

RSpec.describe Api::Employees::PeshesController do
  let(:employee) { Fabricate(:employee) }
  let(:pesh) { Fabricate(:pesh) }
  let(:params) { Fabricate.attributes_for(:pesh) }

  describe "GET #index" do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index',
                    args: { object_name: 'pesh' }

    before(:each) do
      Fabricate.times(30, :pesh, employee: employee, discarded_at: nil)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond peshes list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of peshes page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond peshes list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of peshes page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

  end

  describe "POST #create" do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create',
                    args: { object_name: 'pesh' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create pesh with valid params' do
        expect { post :create, params: { pesh: params, format: :json } }.to change(Pesh, :count).by(1)
        expect(parsed_response['attributes']['remarks']).to eq(params['remarks'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do

      it 'Should not create pesh with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'peshes', fields: validation_columns)
        post :create, params: { pesh: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee_id: ["can't be blank"] }.as_json)
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create pesh' do
        post :create, params: { pesh: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe "PUT #update" do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update',
                    args: { object_name: 'pesh' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update pesh with valid params' do
        pesh1 = Fabricate(:pesh)
        patch :update, params: { id: pesh1.id, pesh: params, format: :json }
        expect(parsed_response['attributes']['remarks']).to eq(params[:remarks])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do

      it 'Should not update pesh with blank employee' do
        validation_columns = %w[employee_id]
        modify_account_schema_validations(model: 'peshes', fields: validation_columns)
        patch :update, params: { id: pesh.id, pesh: { employee_id: '' }, format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update pesh with blank id' do
        patch :update, params: { id: '', pesh: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update pesh' do
        pesh1 = Fabricate(:pesh)
        patch :update, params: { id: pesh1.id, pesh: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe "DELETE #destroy" do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy',
                    args: { object_name: 'pesh' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete pesh with valid params' do
        delete :destroy, params: { id: pesh.id, format: :json }
        expect(assigns(:pesh).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete pesh with blank id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete pesh' do
        delete :destroy, params: { id: pesh.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
