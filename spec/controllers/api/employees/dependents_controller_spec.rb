# frozen_string_literal: true

RSpec.describe Api::Employees::DependentsController do
  let(:employee) { Fabricate(:employee) }
  let(:life_insurance) { Fabricate(:life_insurance) }
  let(:dependent) { Fabricate(:dependent) }
  let(:params) { Fabricate.attributes_for(:dependent) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'dependent',
                                                                                   rights: 'life_insurance',
                                                                                   params: { employee_id: 1,
                                                                                             life_insurance_id: 1 } }

    before(:each) do
      Fabricate.times(30, :dependent, employee: employee, life_insurance: life_insurance)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end

      it 'Respond dependent list with default pagination count' do
        get :index, params: { employee_id: employee.id, life_insurance_id: life_insurance.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of dependent page 2' do
        get :index, params: { employee_id: employee.id, life_insurance_id: life_insurance.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end

      it 'Respond dependent list with default pagination count' do
        get :index, params: { employee_id: employee.id, life_insurance_id: life_insurance.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of dependent page 2' do
        get :index, params: { employee_id: employee.id, life_insurance_id: life_insurance.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'dependent', rights: 'life_insurance' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create dependent with valid params' do
        post :create, params: { dependent: params, format: :json }
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create dependent with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank date' do
        validation_columns = ['age']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(age: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank life insurance' do
        validation_columns = ['life_insurance_id']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(life_insurance_id: ''), format: :json }
        expect(error_response[0]).to include({ life_insurance: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank relationship' do
        validation_columns = ['relationship']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(relationship: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create dependent with blank SSN' do
        validation_columns = ['amount']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(amount: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check dependent validation if required fields present' do
        validation_columns = %w[amount date address relationship name]
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        post :create, params: { dependent: params.merge(amount: '', date: '', age: '', address: '',
                                                               relationship: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create dependent' do
        post :create, params: { dependent: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'dependent', rights: 'life_insurance' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update dependent with valid params' do
        dependent1 = Fabricate(:dependent)
        patch :update, params: { id: dependent1.id, dependent: params, format: :json }
        expect(parsed_response['attributes']['name']).to eq(params[:name])
        expect(parsed_response['attributes']['relationship']).to eq(params[:relationship])
        expect(parsed_response['attributes']['address']).to eq(params[:address])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update dependent with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank life insurance' do
        validation_columns = ['life_insurance_id']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { life_insurance_id: '' }, format: :json }
        expect(error_response[0]).to include({ life_insurance: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { address: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank age' do
        validation_columns = ['age']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { age: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank SSN' do
        validation_columns = ['amount']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { amount: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update dependent with blank relationship' do
        validation_columns = ['relationship']
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: { relationship: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check dependent validation if required fields present' do
        validation_columns = %w[amount date address relationship name]
        modify_account_schema_validations(model: 'dependents', fields: validation_columns)
        patch :update, params: { id: dependent.id, dependent: params.merge(amount: '', date: '', age: '', address: '',
                                                                                         relationship: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update dependent' do
        dependent1 = Fabricate(:dependent)
        patch :update, params: { id: dependent1.id, dependent: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'dependent', rights: 'life_insurance' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete dependent with valid params' do
        delete :destroy, params: { id: dependent.id, format: :json }
        expect(assigns(:dependent).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete dependent with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete dependent' do
        delete :destroy, params: { id: dependent.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
