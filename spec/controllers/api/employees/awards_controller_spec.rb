# frozen_string_literal: true

RSpec.describe Api::Employees::AwardsController do
  let(:award) { Fabricate(:award) }
  let(:employee) { Fabricate(:employee) }
  let(:params) { Fabricate.attributes_for(:award) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index',
                                           args: { object_name: 'award', rights: 'employee_award',
                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :award, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond awards list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Respond awards list with page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond awards list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Respond awards list with page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create',
                                           args: { object_name: 'award', rights: 'employee_award' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create award with valid params' do
        expect { post :create, params: { award: params, format: :json } }.to change(Award, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create award with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        post :create, params: { award: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create award with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        post :create, params: { award: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create award with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        post :create, params: { award: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should check award validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        post :create, params: { award: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check award validation if required fields not present' do
        modify_account_schema_validations(model: 'awards', action: 'clear')
        expect { post :create, params: { award: params.merge(name: '', description: ''), format: :json } }.to change(Award, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create award' do
       post :create, params: { award: params, format: :json }
       expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
       forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update',
                                           args: { object_name: 'award', rights: 'employee_award' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update award with valid params' do
        award1 = Fabricate(:award)
        patch :update, params: { id: award1.id, award: params, format: :json }
        expect(parsed_response['attributes']['name']).to eq(params[:name])
        expect(parsed_response['attributes']['description']).to eq(params[:description])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update award with blank name' do
        validation_columns = %w[name]
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        patch :update, params: { id: award.id, award: { name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update award with blank employee' do
        validation_columns = %w[employee_id]
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        patch :update, params: { id: award.id, award: { employee_id: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update award with blank description' do
        validation_columns = %w[description]
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        patch :update, params: { id: award.id, award: { description: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check award validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'awards', fields: validation_columns)
        patch :update, params: { id: award.id, award: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check award validation if required fields not present' do
        modify_account_schema_validations(model: 'awards', action: 'clear')
        patch :update, params: { id: award.id, award: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update award with blank id' do
        patch :update, params: { id: '', award: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update award' do
        award1 = Fabricate(:award)
        patch :update, params: { id: award1.id, award: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy',
                                           args: { object_name: 'award', rights: 'employee_award' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete award with valid params' do
        delete :destroy, params: { id: award.id, format: :json }
        expect(assigns(:award).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete award with blank id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete award' do
        delete :destroy, params: { id: award.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
