# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::UsersController do
  let(:user) { Fabricate(:user) }
  let(:user_contact) { Fabricate(:user_contact, user: user) }
  let(:params) { Fabricate.attributes_for(:user) }

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'user' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :user)
      end

      it 'Respond users list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of user page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(6)
        success_response
      end
    end

    context 'Search filter', authenticated: true do
      before(:each) do
        Fabricate.times(5, :user, first_name: 'xyz')
      end

      it 'Display user by search email' do
        get :index, params: { search_text: user.email }
        expect(parsed_response.count).to eq(1)
        success_response
      end

      it 'Display user by search first_name' do
        get :index, params: { search_text: user.first_name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display user by search last_name' do
        get :index, params: { search_text: user.last_name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display user by invalid search' do
        get :index, params: { search_text: 'abcd' }
        expect(parsed_response.count).to eq(0)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'user' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create user with valid params' do
        expect { post :create, params: { user: params }, format: :json }.to change(User, :count).by(1)
        expect(parsed_response['attributes']['username']).to eq(params['username'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create user with invalid username' do
        validation_columns = ['username']
        modify_account_schema_validations(model: 'users', fields: validation_columns)
        post :create, params: { user: params.merge(username: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create user with invalid password_confirmation' do
        validation_columns = ['password_confirmation']
        modify_account_schema_validations(model: 'users', fields: validation_columns)
        post :create, params: { user: params.merge(password_confirmation: ''), format: :json }
        expect(error_response).to eq([{ 'password_confirmation' => ["doesn't match Password", "can't be blank"] }])
      end
    end
  end

  context 'Invalid cases - static validations(validations defined in model itself)', authenticated: true do
    it 'Should not create user with same username' do
      user
      post :create, params: { user: params.merge(username: user.username), format: :json }
      expect(error_response).to eq([{ 'username' => ['has already been taken'] }])
    end

    it 'Should not create user with same email' do
      user
      post :create, params: { user: params.merge(email: user.email), format: :json }
      expect(error_response).to eq([{ 'email' => ['has already been taken'] }])
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'user' }

    before(:each) do
      user
    end

    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update user with valid params' do
        patch :update, params: { id: user.id, user: { username: 'name' } }, format: :json
        expect(parsed_response['attributes']['username']).to eq('name')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update user with invalid username' do
        validation_columns = ['username']
        modify_account_schema_validations(model: 'users', fields: validation_columns)
        patch :update, params: { id: user.id, user: { username: '' }, format: :json }
        blank_error_responses(validation_columns)
      end
    end
  end

  context 'Invalid cases', authenticated: true do
    it 'Should not update user with same username' do
      user1 = Fabricate(:user)
      patch :update, params: { id: user.id, user: params.merge(username: user1.username), format: :json }
      expect(error_response).to eq([{ 'username' => ['has already been taken'] }])
    end

    it 'Should not update user with same email' do
      user1 = Fabricate(:user)
      patch :update, params: { id: user.id, user: params.merge(email: user1.email), format: :json }
      expect(error_response).to eq([{ 'email' => ['has already been taken'] }])
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'user' }

    context 'Valid params', authenticated: true do
      it 'should delete user with valid params' do
        delete :destroy, params: { id: user.id }, format: :json
        expect(assigns(:user).discarded_at).not_to eq(nil)
        success_response
      end

      it 'should delete user and create with same username and email' do
        user = Fabricate(:user, username: 'mallow', email: '<EMAIL>')
        delete :destroy, params: { id: user.id }, format: :json
        expect(assigns(:user).discarded_at).not_to eq(nil)
        expect { post :create, params: { user: params.merge(username: 'mallow', email: '<EMAIL>') }, format: :json }.to change(User, :count).by(1)
        expect(parsed_response['attributes']['username']).to eq('mallow')
        expect(parsed_response['attributes']['email']).to eq('<EMAIL>')
        success_response
      end
    end

    context 'Invalid params', authenticated: true do
      it 'Should not delete with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end
  end

  describe 'GET #profile' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'profile', params: { id: 1 }

    context 'Valid params', authenticated: true do
      it 'should display profile for valid params' do
        get :profile, format: :json
        expect(parsed_response['attributes']['username']).to eq(@user.username)
        success_response
      end
    end
  end

  describe 'PATCH #profile_update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'profile_update', params: { id: 1 }

    context 'Valid params', authenticated: true do
      it 'should update with valid params' do
        user1 = Fabricate.to_params(:user_profile)
        patch :profile_update, params: { user: user1 }, format: :json
        success_response
      end
    end

    context 'Invalid params', authenticated: true do
      it 'should update with valid params' do
        user1 = Fabricate.to_params(:user_profile1)
        patch :profile_update, params: { user: user1 }, format: :json
        expect(error_response).to eq([{ 'user_contacts.contact_for' => ['is not included in the list'] }])
      end
    end
  end
end
