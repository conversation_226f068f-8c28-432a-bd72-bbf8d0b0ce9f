# frozen_string_literal: true

module Helpers
  def response_data
    @response_data = JSON.parse(response.body)
  end

  def parsed_response
    response_data['data']
  end

  def error_response
    response_data['errors']
  end

  def success_response
    expect(response).to have_http_status(:success)
  end

  def not_found
    expect(response).to have_http_status(404)
  end

  def unprocessable_entity
    expect(response).to have_http_status(422)
  end

  def forbidden
    expect(response).to have_http_status(403)
  end

  def modify_account_schema_validations(model:, fields: [], action: 'add')
    @current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
    if action == 'add'
      @current_account.saas_json['schema'][model]['required_fields'] += fields
      @current_account.saas_json['schema'][model]['required_fields'].flatten
    elsif action == 'clear'
      @current_account.saas_json['schema'][model]['required_fields'] = []
    end
    @current_account.save
  end

  def blank_error_responses(validation_columns)
    validation_columns.each do |column|
      error_json = {}
      error_json[column] = ["can't be blank"]
      expect(error_response[0]).to include(error_json.as_json)
    end
    unprocessable_entity
  end

  def employee_search_schema(key: [], action: 'add')
    @current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
    if action == 'add'
      @current_account.saas_json['schema']['employees']['search_columns']['same_model'] += key
      @current_account.saas_json['schema']['employees']['search_columns']['same_model'].flatten
    elsif action == 'clear'
      @current_account.saas_json['schema']['employees']['search_columns']['same_model'] = []
    end
    @current_account.save
  end
end
