# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AnalyticsConfiguration, type: :model do
  describe 'validation' do
    subject { Fabricate(:analytics_configuration) }

    describe 'valid analytics_configuration' do
      it { expect(subject).to validate_presence_of(:days_earned) }
      it { expect(subject).to validate_presence_of(:analytics_type) }
      it { expect(subject).to validate_numericality_of(:days_earned) }
      it { expect(subject).to belong_to(:employee) }
    end
  end
end
