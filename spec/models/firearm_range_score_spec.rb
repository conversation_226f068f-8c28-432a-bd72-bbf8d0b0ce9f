# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FirearmRangeScore, type: :model do
  describe 'validation' do
    subject { Fabricate(:firearm_range_score) }

    describe 'valid firearm_range_score' do
      it { expect(subject).to validate_presence_of(:score) }
      it { expect(subject).to validate_presence_of(:test_type) }
      it { expect(subject).to validate_presence_of(:test_date) }
      it { expect(subject).to belong_to(:employee) }
    end
  end
end
