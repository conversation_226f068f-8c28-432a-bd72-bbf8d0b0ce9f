# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmployeeMeetingType, type: :model do
  describe 'validation' do
    subject { Fabricate(:employee_meeting_type) }

    describe 'valid employee_meeting_type' do
      it { expect(subject).to validate_presence_of(:meeting_date) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:meeting_type) }
    end
  end
end
