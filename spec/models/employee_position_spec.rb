# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmployeePosition, type: :model do
  describe 'validation' do
    subject { Fabricate(:employee_position) }

    describe 'valid employee_position' do
      it { expect(subject).to validate_presence_of(:start_date) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:position) }
    end
  end
end
