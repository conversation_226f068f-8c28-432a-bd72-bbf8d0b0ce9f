# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmployeeDisciplineSetting, type: :model do
  describe 'validation' do
    subject { Fabricate(:employee_discipline_setting) }

    describe 'valid employee employee discipline setting' do
      it { should belong_to(:employee) }
      it { should belong_to(:discipline_setting) }
      it { should belong_to(:discipline_charge).optional }
      it { should have_many(:employee_discipline_steps) }
      it { should have_many(:discipline_statuses) }
    end
  end
end
