# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmployeeEmploymentStatus, type: :model do
  describe 'validation' do
    subject { Fabricate(:employee_employment_status) }

    describe 'valid employee_employment_status' do
      it { expect(subject).to validate_presence_of(:start_date) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:employment_status) }
    end
  end
end
