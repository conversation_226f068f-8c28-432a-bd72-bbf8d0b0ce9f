# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Office, type: :model do
  describe 'validation' do
    subject { Fabricate(:office) }

    describe 'valid office' do
      it { expect(subject).to validate_presence_of(:name) }
      it { expect(subject).to validate_presence_of(:address) }
      it { expect(subject).to have_many(:delegate_assignments) }
      it { expect(subject).to have_many(:employee_offices) }
      it { expect(subject).to have_many(:lodis) }
      it { expect(subject).to have_many(:peshes) }
    end
  end
end
