# frozen_string_literal: true

Fabricator(:employee) do
  a_number { Faker::IDNumber.invalid }
  apartment { Faker::Address.building_number }
  birthday { Faker::Date.birthday(min_age: 30, max_age: 65) }
  city { Faker::Address.city }
  first_name { Faker::Name.first_name }
  last_name { Faker::Name.last_name }
  middle_name { Faker::Name.middle_name }
  notes { Faker::Lorem.paragraph }
  placard_number { Faker::IDNumber.invalid }
  shield_number { Faker::Number.number }
  social_security_number { Faker::IDNumber.invalid }
  state { Faker::Address.state }
  street { Faker::Address.street_name }
  veteran_status { [true, false].sample }
  staff_member { [true, false].sample }
  janus_card { [true, false].sample }
  do_not_mail { [true, false].sample }
  email_opt_out { [true, false].sample }
  sms_opt_out { [true, false].sample }
  zipcode '89976'
  member_since Date.today
  start_date Date.today
  title_code Faker::Lorem.word
  prom_prov Date.today
  prom_perm Date.today
  janus_card_opt_out_date Date.today
  rdo { Faker::Lorem.sentence }
  payroll_id { Faker::Lorem.sentence }
  longevity_date Date.today
  leave_progression_date Date.today
  ncc_date Date.today
  primary_work_location { Faker::Address.state }
  responder_911 { [true, false].sample }
  username { sequence { |i| "username#{i}" } }
  password { 'password' }
  password_confirmation { 'password' }

  marital_status
  unit
  gender
  affiliation
  tour_of_duty
  platoon
end

Fabricator(:employee1, from: :employee) do
  contacts_attributes do
    { '0' => Fabricate.to_params(:contact2) }
  end
end

Fabricator(:employee2, from: :employee) do
  contacts_attributes do
    { '0' => Fabricate.to_params(:contact1) }
  end
end

Fabricator(:employee_search, from: 'employee') do
  a_number { Faker::IDNumber.invalid }
  apartment { Faker::Address.building_number }
  birthday { Date.today }
  city { sequence { |i| "chennai#{i}" } }
  first_name { sequence { |i| "xyz#{i}" } }
  last_name { sequence { |i| "xyz#{i}" } }
  middle_name { sequence { |i| "xyz#{i}" } }
  notes { Faker::Lorem.paragraph }
  placard_number { sequence { |i| "89#{i}" } }
  shield_number { sequence { |i| "89#{i}" } }
  social_security_number { sequence { |i| "89#{i}" } }
  state { sequence { |i| "xyz#{i}" } }
  street { sequence { |i| "xyz#{i}" } }
  veteran_status { [true, false].sample }
  staff_member { [true, false].sample }
  janus_card { [true, false].sample }
  do_not_mail { [true, false].sample }
  email_opt_out { [true, false].sample }
  sms_opt_out { [true, false].sample }
  zipcode '89076'
  member_since Date.today
  start_date Date.today
  title_code Faker::Lorem.word
  prom_prov Date.today
  prom_perm Date.today
  janus_card_opt_out_date Date.today
  rdo { Faker::Lorem.sentence }
  payroll_id { Faker::Lorem.sentence }
  longevity_date Date.today
  leave_progression_date Date.today
  ncc_date Date.today
  primary_work_location { sequence { |i| "xyz#{i}" } }
  responder_911 { [true, false].sample }

  marital_status
  unit
  gender
  affiliation
  tour_of_duty
  platoon
end
