# frozen_string_literal: true

Fabricator(:user) do
  first_name { Faker::Lorem.word }
  last_name { Faker::Lorem.word }
  username { sequence { |i| "#{Faker::Lorem.word}_#{i}" } }
  access_role { 'Administrator' }
  email { sequence { |i| "#{Faker::Internet.email}_#{i}" } }
  password { 'password' }
  password_confirmation { 'password' }
  role_id { Role.where(name: 'Admin').first&.id }
end

Fabricator(:user_profile, from: :user) do
  user_contacts_attributes do
    { '0' => Fabricate.to_params(:user_contact) }
  end
end

Fabricator(:user_profile1, from: :user) do
  user_contacts_attributes do
    { '0' => Fabricate.to_params(:user_contact1) }
  end
end
