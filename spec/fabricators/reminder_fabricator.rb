Fabricator(:reminder) do
    title { Faker::Lorem.sentence(word_count: 3) }
    description { Faker::Lorem.paragraph }
    reminder_start_date { Faker::Date.forward(days: 7) }
    time { Time.parse(Faker::Time.forward(days: 7, period: :morning).strftime('%H:%M:%S')) }
    creator { Fabricate(:user) }
    status { 'pending' }
    user_ids { [Fabricate(:user).id.to_s] }
    repeat { { type: 'daily', daily: { every: 1 } } }
end