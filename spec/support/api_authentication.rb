# frozen_string_literal: true

include Api::JsonWebToken

RSpec.configure do |config|
  # Create user and set authorization cookies in the request
  # This will run only before the block with 'authenticated: true'
  config.before :each, authenticated: true do
    generate_and_set_token
  end

  config.before :each, expired_token: true do
    generate_and_set_token(1.days)
  end

  config.before :each, authenticated_employee: true do
    generate_and_set_employee_token
  end

  def generate_and_set_token(exp_token = 0.minutes)
    @user ||= Fabricate(:user)
    pay_load = {
      user_id: @user.id,
      exp: (token_expire_at - exp_token).to_i,
      iat: token_issued_at.to_i
    }
    request.cookies['fusesystems_session'] = JWT.encode(pay_load, Rails.application.credentials.secret_key_base)
  end

  def generate_and_set_employee_token
    @employee ||= Fabricate(:employee)
    token = Doorkeeper::AccessToken.create!(resource_owner_id: @employee.id, use_refresh_token: true, expires_in: nil)
    request.headers['Authorization'] = "Bearer #{token.token}"
  end
end
