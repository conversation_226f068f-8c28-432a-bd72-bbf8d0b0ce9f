commands:
  redis_install_command block:
    command: |
      if [ ! -f /etc/redis/redis.conf ]; then
        echo "Installing Redis"
        wget http://download.redis.io/redis-stable.tar.gz
        tar xvzf redis-stable.tar.gz
        cd redis-stable
        make
        mkdir /etc/redis /var/lib/redis
        cp src/redis-server src/redis-cli /usr/local/bin
        cp redis.conf /etc/redis/redis.conf
        sed -e "s/^daemonize no$/daemonize yes/" -e "s/^stop-writes-on-bgsave-error yes$/stop-writes-on-bgsave-error no/" -e "s/^# bind 127.0.0.1$/bind 127.0.0.1/" -e "s/^dir \.\//dir \/var\/lib\/redis\//" -e "s/^loglevel verbose$/loglevel notice/" -e "s/^logfile stdout$/logfile \/var\/log\/redis.log/" redis.conf > /etc/redis/redis.conf
        wget https://gist.githubusercontent.com/dstroot/2777433/raw/061ceb203dc7b002b073f28880f63cfa3ef9b5d2/redis-server
        mv redis-server /etc/init.d
        chmod 755 /etc/init.d/redis-server
        chkconfig --add redis-server
        chkconfig --level 345 redis-server on
      else
        echo "Redis is already installed"
      fi

      /usr/local/bin/redis-server /etc/redis/redis.conf
