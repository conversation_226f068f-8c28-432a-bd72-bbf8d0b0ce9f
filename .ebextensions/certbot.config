files:
    "/etc/cron.d/renew_certbot":
        mode: "000644"
        owner: root
        group: root
        content: |
            0 0 1 * * root /var/app/current/.platform/script/certbot_renew.sh

container_commands:
  00_permission_hook:
    command: "chmod +x .platform/confighooks/postdeploy/*"
  01_permission_hook:
    command: "chmod +x .platform/confighooks/prebuild/*"
  02_permission_hook:
    command: "chmod +x .platform/confighooks/predeploy/*"
  03_permission_hook:
    command: "chmod +x .platform/hooks/postdeploy/*"
  04_permission_hook:
    command: "chmod +x .platform/hooks/prebuild/*"
  05_permission_hook:
    command: "chmod +x .platform/hooks/predeploy/*"
  06_permission_hook:
    command: "chmod +x .platform/script/*"