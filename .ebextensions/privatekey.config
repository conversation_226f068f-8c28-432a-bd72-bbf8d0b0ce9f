Resources:
  AWSEBAutoScalingGroup:
    Metadata:
      AWS::CloudFormation::Authentication:
        S3Auth:
          type: "s3"
          buckets: ["fuse-development-ssl-certs"]
          roleName: 
            "Fn::GetOptionSetting": 
              Namespace: "aws:autoscaling:launchconfiguration"
              OptionName: "IamInstanceProfile"
              DefaultValue: "aws-elasticbeanstalk-ec2-role"
files:
  # Private key
  "/etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/fullchain1.pem":
    mode: "000400"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://fuse-development-ssl-certs.s3.us-east-2.amazonaws.com/saasdevelopment.myfusesystems.com/fullchain1.pem
  "/etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/privkey1.pem":
    mode: "000400"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://fuse-development-ssl-certs.s3.us-east-2.amazonaws.com/saasdevelopment.myfusesystems.com/privkey1.pem
  "/etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/chain1.pem":
    mode: "000400"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://fuse-development-ssl-certs.s3.us-east-2.amazonaws.com/saasdevelopment.myfusesystems.com/chain1.pem
  "/etc/letsencrypt/archive/saasdevelopment.myfusesystems.com/cert1.pem":
    mode: "000400"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://fuse-development-ssl-certs.s3.us-east-2.amazonaws.com/saasdevelopment.myfusesystems.com/cert1.pem