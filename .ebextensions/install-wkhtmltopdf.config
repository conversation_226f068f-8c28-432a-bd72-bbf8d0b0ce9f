container_commands:
  1_install_wkhtmltopdf:
    command: yum -y install fontconfig libXrender libXext xorg-x11-fonts-Type1 xorg-x11-fonts-75dpi freetype libpng zlib libjpeg-turbo openssl icu
    ignoreErrors: true

  2_install_wkhtmltopdf:
    # see: https://wkhtmltopdf.org/downloads.html for updates
    command: wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz --dns-timeout=5 --connect-timeout=5 --no-check-certificate
    test: test ! -f .wkhtmltopdf

  3_install_wkhtmltopdf:
    command: tar -xJf wkhtmltox-0.12.4_linux-generic-amd64.tar.xz
    test: test ! -f .wkhtmltopdf

  4_install_wkhtmltopdf:
    command: cp wkhtmltox/bin/wkhtmltopdf /usr/local/bin/wkhtmltopdf
    test: test ! -f .wkhtmltopdf

  5_install_wkhtmltopdf:
    command: cp wkhtmltox/bin/wkhtmltoimage /usr/local/bin/wkhtmltoimage
    test: test ! -f .wkhtmltopdf

  6_install_wkhtmltopdf:
    command: touch .wkhtmltopdf
