{"version": "0.9.1", "created_at": "2023-10-05 17:11:27 +0530", "results": [{"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2023-22795.yml", "id": "CVE-2023-22795", "url": "https://github.com/rails/rails/releases/tag/v*******", "title": "ReDoS based DoS vulnerability in Action Dispatch", "date": "2023-01-18", "description": "There is a possible regular expression based DoS vulnerability in Action\nDispatch related to the If-None-Match header. This vulnerability has been\nassigned the CVE identifier CVE-2023-22795.\n\nVersions Affected: All\nNot affected: None\nFixed Versions: ******** (Rails LTS), *******, *******\n\n# Impact\n\nA specially crafted HTTP If-None-Match header can cause the regular\nexpression engine to enter a state of catastrophic backtracking, when on a\nversion of Ruby below 3.2.0. This can cause the process to use large amounts\nof CPU and memory, leading to a possible DoS vulnerability All users running\nan affected release should either upgrade or use one of the workarounds\nimmediately.\n\n# Workarounds\n\nWe recommend that all users upgrade to one of the FIXED versions. In the\nmeantime, users can mitigate this vulnerability by using a load balancer or\nother device to filter out malicious If-None-Match headers before they reach\nthe application.\n\nUsers on Ruby 3.2.0 or greater are not affected by this vulnerability.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-22795", "osvdb": null, "ghsa": "8xww-x3g3-6jcv", "unaffected_versions": [], "patched_versions": ["~> 5.2.8, >= ********", "~> 6.1.7, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2021-22885.yml", "id": "CVE-2021-22885", "url": "https://groups.google.com/g/rubyonrails-security/c/NiQl-48cXYI", "title": "Possible Information Disclosure / Unintended Method Execution in Action Pack", "date": "2021-05-05", "description": "There is a possible information disclosure / unintended method execution\nvulnerability in Action Pack which has been assigned the CVE identifier\nCVE-2021-22885.\n\nVersions Affected:  >= 2.0.0.\nNot affected:       < 2.0.0.\nFixed Versions:     *******, *******, *******, 5.2.6\n\nImpact\n------\nThere is a possible information disclosure / unintended method execution\nvulnerability in Action Pack when using the `redirect_to` or `polymorphic_url`\nhelper with untrusted user input.\n\nVulnerable code will look like this:\n\n```\nredirect_to(params[:some_param])\n```\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\nWorkarounds\n-----------\nTo work around this problem, it is recommended to use an allow list for valid\nparameters passed from the user.  For example:\n\n```\nprivate def check(param)\n  case param\n  when \"valid\"\n    param\n  else\n    \"/\"\n  end\nend\n\ndef index\n  redirect_to(check(params[:some_param]))\nend\n```\n\nOr force the user input to be cast to a string like this:\n\n```\ndef index\n  redirect_to(params[:some_param].to_s)\nend\n```\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2021-22885", "osvdb": null, "ghsa": "hjg4-8q5f-x6fm", "unaffected_versions": ["< 2.0.0"], "patched_versions": ["~> *******", "~> 5.2.6", "~> 6.0.3, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2023-22792.yml", "id": "CVE-2023-22792", "url": "https://github.com/rails/rails/releases/tag/v*******", "title": "ReDoS based DoS vulnerability in Action Dispatch", "date": "2023-01-18", "description": "There is a possible regular expression based DoS vulnerability in Action\nDispatch. This vulnerability has been assigned the CVE identifier\nCVE-2023-22792.\n\nVersions Affected: >= 3.0.0\nNot affected: < 3.0.0\nFixed Versions: ******** (Rails LTS), *******, *******\n\n# Impact\n\nSpecially crafted cookies, in combination with a specially crafted\nX_FORWARDED_HOST header can cause the regular expression engine to enter a\nstate of catastrophic backtracking. This can cause the process to use large\namounts of CPU and memory, leading to a possible DoS vulnerability All users\nrunning an affected release should either upgrade or use one of the\nworkarounds immediately.\n\n# Workarounds\n\nWe recommend that all users upgrade to one of the FIXED versions. In the\nmeantime, users can mitigate this vulnerability by using a load balancer or\nother device to filter out malicious X_FORWARDED_HOST headers before they\nreach the application.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-22792", "osvdb": null, "ghsa": "p84v-45xj-wwqj", "unaffected_versions": ["< 3.0.0"], "patched_versions": ["~> 5.2.8, >= ********", "~> 6.1.7, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2021-22881.yml", "id": "CVE-2021-22881", "url": "https://groups.google.com/g/rubyonrails-security/c/zN_3qA26l6E", "title": "Possible Open Redirect in Host Authorization Middleware", "date": "2021-02-10", "description": "There is a possible open redirect vulnerability in the Host Authorization\nmiddleware in Action Pack. This vulnerability has been assigned the CVE\nidentifier CVE-2021-22881.\n\nVersions Affected:  >= 6.0.0\nNot affected:       < 6.0.0\nFixed Versions:     *******, *******\n\nImpact\n------\nSpecially crafted \"Host\" headers in combination with certain \"allowed host\"\nformats can cause the Host Authorization middleware in Action Pack to redirect\nusers to a malicious website.\n\nImpacted applications will have allowed hosts with a leading dot.  For\nexample, configuration files that look like this:\n\n```\nconfig.hosts <<  '.tkte.ch'\n```\n\nWhen an allowed host contains a leading dot, a specially crafted Host header\ncan be used to redirect to a malicious website.\n\nWorkarounds\n-----------\nIn the case a patch can't be applied, the following monkey patch can be used\nin an initializer:\n\n```ruby\nmodule ActionDispatch\n  class HostAuthorization\n    private\n      def authorized?(request)\n        valid_host = /\n          \\A\n          (?<host>[a-z0-9.-]+|\\[[a-f0-9]*:[a-f0-9\\.:]+\\])\n          (:\\d+)?\n          \\z\n        /x\n\n        origin_host = valid_host.match(\n          request.get_header(\"HTTP_HOST\").to_s.downcase)\n        forwarded_host = valid_host.match(\n          request.x_forwarded_host.to_s.split(/,\\s?/).last)\n\n        origin_host && @permissions.allows?(origin_host[:host]) && (\n          forwarded_host.nil? || @permissions.allows?(forwarded_host[:host]))\n      end\n  end\nend\n```\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2021-22881", "osvdb": null, "ghsa": "8877-prq4-9xfw", "unaffected_versions": ["< 6.0.0"], "patched_versions": ["~> 6.0.3, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2021-22942.yml", "id": "CVE-2021-22942", "url": "https://groups.google.com/g/rubyonrails-security/c/wB5tRn7h36c", "title": "Possible Open Redirect in Host Authorization Middleware", "date": "2021-08-19", "description": "There is a possible open redirect vulnerability in the Host Authorization\nmiddleware in Action Pack. This vulnerability has been assigned the CVE\nidentifier CVE-2021-22942.\n\nVersions Affected: >= 6.0.0.\nNot affected: < 6.0.0\nFixed Versions: *******, *******\n\nImpact\n------\n\nSpecially crafted “X-Forwarded-Host” headers in combination with certain\n“allowed host” formats can cause the Host Authorization middleware in\nAction Pack to redirect users to a malicious website.\n\nImpacted applications will have allowed hosts with a leading dot.\nFor example, configuration files that look like this:\n\n```ruby\nconfig.hosts <<  '.EXAMPLE.com'\n```\n\nWhen an allowed host contains a leading dot, a specially crafted\nHost header can be used to redirect to a malicious website.\n\nThis vulnerability is similar to CVE-2021-22881, but CVE-2021-22881 did not\ntake in to account domain name case sensitivity.\n\nReleases\n--------\n\nThe fixed releases are available at the normal locations.\n\nWorkarounds\n-----------\n\nIn the case a patch can’t be applied, the following monkey patch can be\nused in an initializer:\n\n```ruby\nmodule ActionDispatch\n  class HostAuthorization\n    HOSTNAME = /[a-z0-9.-]+|\\[[a-f0-9]*:[a-f0-9.:]+\\]/i\n    VALID_ORIGIN_HOST = /\\A(#{HOSTNAME})(?::\\d+)?\\z/\n    VALID_FORWARDED_HOST = /(?:\\A|,[ ]?)(#{HOSTNAME})(?::\\d+)?\\z/\n\n    private\n      def authorized?(request)\n        origin_host =\n          request.get_header(\"HTTP_HOST\")&.slice(VALID_ORIGIN_HOST, 1) || \"\"\n        forwarded_host =\n          request.x_forwarded_host&.slice(VALID_FORWARDED_HOST, 1) || \"\"\n        @permissions.allows?(origin_host) &&\n          (forwarded_host.blank? || @permissions.allows?(forwarded_host))\n      end\n  end\nend\n```\n", "cvss_v2": null, "cvss_v3": 7.6, "cve": "2021-22942", "osvdb": null, "ghsa": "2rqw-v265-jf8c", "unaffected_versions": ["< 6.0.0"], "patched_versions": ["~> 6.0.4, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2020-8185.yml", "id": "CVE-2020-8185", "url": "https://groups.google.com/g/rubyonrails-security/c/pAe9EV8gbM0", "title": "Untrusted users able to run pending migrations in production", "date": "2020-06-17", "description": "There is a vulnerability in versions of Rails prior to ******* that allowed\nan untrusted user to run any pending migrations on a Rails app running in\nproduction.\n\nThis vulnerability has been assigned the CVE identifier CVE-2020-8185.\n\nVersions Affected:  6.0.0 < rails < *******\nNot affected:       Applications with `config.action_dispatch.show_exceptions = false` (this is not a default setting in production)\nFixed Versions:     rails >= *******\n\nImpact\n------\n\nUsing this issue, an attacker would be able to execute any migrations that\nare pending for a Rails app running in production mode. It is important to\nnote that an attacker is limited to running migrations the application\ndeveloper has already defined in their application and ones that have not\nalready ran.\n\nWorkarounds\n-----------\n\nUntil such time as the patch can be applied, application developers should\ndisable the ActionDispatch middleware in their production environment via\na line such as this one in their config/environment/production.rb:\n\n`config.middleware.delete ActionDispatch::ActionableExceptions`\n", "cvss_v2": null, "cvss_v3": 6.5, "cve": "2020-8185", "osvdb": null, "ghsa": "c6qr-h5vq-59jc", "unaffected_versions": ["< 6.0.0"], "patched_versions": [">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2022-22577.yml", "id": "CVE-2022-22577", "url": "https://groups.google.com/g/ruby-security-ann/c/NuFRKaN5swI", "title": "Possible XSS Vulnerability in Action Pack", "date": "2022-04-27", "description": "There is a possible XSS vulnerability in Rails / Action Pack. This vulnerability has been\nassigned the CVE identifier CVE-2022-22577.\n\nVersions Affected:  >= 5.2.0\nNot affected:       < 5.2.0\nFixed Versions:     *******, *******, *******, *******\n\n## Impact\n\nCSP headers were only sent along with responses that Rails considered as\n\"HTML\" responses.  This left API requests without CSP headers, which could\npossibly expose users to XSS attacks.\n\n## Releases\n\nThe FIXED releases are available at the normal locations.\n\n## Workarounds\n\nSet a CSP for your API responses manually.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-22577", "osvdb": null, "ghsa": "mm33-5vfq-3mm3", "unaffected_versions": ["< 5.2.0"], "patched_versions": ["~> 5.2.7, >= *******", "~> 6.0.4, >= *******", "~> 6.1.5, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2023-28362.yml", "id": "CVE-2023-28362", "url": "https://discuss.rubyonrails.org/t/cve-2023-28362-possible-xss-via-user-supplied-values-to-redirect-to/83132", "title": "Possible XSS via User Supplied Values to redirect_to", "date": "2023-06-26", "description": "The redirect_to method in Rails allows provided values to contain characters\nwhich are not legal in an HTTP header value. This results in the potential for\ndownstream services which enforce RFC compliance on HTTP response headers to\nremove the assigned Location header. This vulnerability has been assigned the\nCVE identifier CVE-2023-28362.\n\nVersions Affected: All. Not affected: None Fixed Versions: *******, *******\n\n# Impact\n\nThis introduces the potential for a Cross-site-scripting (XSS) payload to be\ndelivered on the now static redirection page. Note that this both requires\nuser interaction and for a Rails app to be configured to allow redirects to\nexternal hosts (defaults to false in Rails >= 7.0.x).\n\n# Releases\n\nThe FIXED releases are available at the normal locations.\n\n# Workarounds\n\nAvoid providing user supplied URLs with arbitrary schemes to the redirect_to\nmethod.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-28362", "osvdb": null, "ghsa": "4g8v-vg43-wpgf", "unaffected_versions": [], "patched_versions": ["~> *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2020-8166.yml", "id": "CVE-2020-8166", "url": "https://groups.google.com/forum/#!topic/rubyonrails-security/NOjKiGeXUgw", "title": "Ability to forge per-form CSRF tokens given a global CSRF token", "date": "2020-05-18", "description": "It is possible to possible to, given a global CSRF token such as the one\npresent in the authenticity_token meta tag, forge a per-form CSRF token for\nany action for that session.\n\nVersions Affected:  rails < 5.2.5, rails < 6.0.4\nNot affected:       Applications without existing HTML injection vulnerabilities.\nFixed Versions:     rails >= *******, rails >= *******\n\nImpact\n------\n\nGiven the ability to extract the global CSRF token, an attacker would be able to\nconstruct a per-form CSRF token for that session.\n\nWorkarounds\n-----------\n\nThis is a low-severity security issue. As such, no workaround is necessarily\nuntil such time as the application can be upgraded.\n", "cvss_v2": null, "cvss_v3": 4.3, "cve": "2020-8166", "osvdb": null, "ghsa": "jp5v-5gx4-jmj9", "unaffected_versions": [], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2020-8164.yml", "id": "CVE-2020-8164", "url": "https://groups.google.com/forum/#!topic/rubyonrails-security/f6ioe4sdpbY", "title": "Possible Strong Parameters Bypass in ActionPack", "date": "2020-05-18", "description": "There is a strong parameters bypass vector in ActionPack.\n\nVersions Affected:  rails <= 6.0.3\nNot affected:       rails < 4.0.0\nFixed Versions:     rails >= *******, rails >= *******\n\nImpact\n------\nIn some cases user supplied information can be inadvertently leaked from\nStrong Parameters.  Specifically the return value of `each`, or `each_value`,\nor `each_pair` will return the underlying \"untrusted\" hash of data that was\nread from the parameters.  Applications that use this return value may be\ninadvertently use untrusted user input.\n\nImpacted code will look something like this:\n\n```\ndef update\n  # Attacker has included the parameter: `{ is_admin: true }`\n  User.update(clean_up_params)\nend\n\ndef clean_up_params\n   params.each { |k, v|  SomeModel.check(v) if k == :name }\nend\n```\n\nNote the mistaken use of `each` in the `clean_up_params` method in the above\nexample.\n\nWorkarounds\n-----------\nDo not use the return values of `each`, `each_value`, or `each_pair` in your\napplication.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2020-8164", "osvdb": null, "ghsa": "8727-m6gj-mc37", "unaffected_versions": ["< 4.0.0"], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2021-22904.yml", "id": "CVE-2021-22904", "url": "https://groups.google.com/g/rubyonrails-security/c/Pf1TjkOBdyQ", "title": "Possible DoS Vulnerability in Action Controller Token Authentication", "date": "2021-05-05", "description": "There is a possible DoS vulnerability in the Token Authentication logic in\nAction Controller.  This vulnerability has been assigned the CVE identifier\nCVE-2021-22904.\n\nVersions Affected:  >= 4.0.0\nNot affected:       < 4.0.0\nFixed Versions:     *******, *******, *******, 5.2.6\n\nImpact\n------\nImpacted code uses `authenticate_or_request_with_http_token` or\n`authenticate_with_http_token` for request authentication.  Impacted code will\nlook something like this:\n\n```\nclass PostsController < ApplicationController\n  before_action :authenticate\n\n  private\n\n  def authenticate\n    authenticate_or_request_with_http_token do |token, options|\n      # ...\n    end\n  end\nend\n```\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\nReleases\n--------\nThe fixed releases are available at the normal locations.\n\nWorkarounds\n-----------\nThe following monkey patch placed in an initializer can be used to work around\nthe issue:\n\n```ruby\nmodule ActionController::HttpAuthentication::Token\n  AUTHN_PAIR_DELIMITERS = /(?:,|;|\\t)/\nend\n```\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2021-22904", "osvdb": null, "ghsa": "7wjx-3g7j-8584", "unaffected_versions": ["< 4.0.0"], "patched_versions": ["~> *******", "~> 5.2.6", "~> 6.0.3, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2022-23633.yml", "id": "CVE-2022-23633", "url": "https://groups.google.com/g/ruby-security-ann/c/FkTM-_7zSNA/m/K2RiMJBlBAAJ", "title": "Possible exposure of information vulnerability in Action Pack", "date": "2022-02-11", "description": "## Impact\n\nUnder certain circumstances response bodies will not be closed, for example a\nbug in a webserver (https://github.com/puma/puma/pull/2812) or a bug in a Rack\nmiddleware. In the event a response is not notified of a `close`,\n`ActionDispatch::Executor` will not know to reset thread local state for the\nnext request. This can lead to data being leaked to subsequent requests,\nespecially when interacting with `ActiveSupport::CurrentAttributes`.\n\nUpgrading to the FIXED versions of Rails will ensure mitigation if this issue\neven in the context of a buggy webserver or middleware implementation.\n\n## Patches\n\nThis has been fixed in Rails *******, *******, *******, and *******.\n\n## Workarounds\n\nUpgrading is highly recommended, but to work around this problem the following\nmiddleware can be used:\n\n```\nclass GuardedExecutor < ActionDispatch::Executor\n  def call(env)\n    ensure_completed!\n    super\n  end\n\n  private\n\n    def ensure_completed!\n      @executor.new.complete! if @executor.active?\n    end\nend\n\n# Ensure the guard is inserted before ActionDispatch::Executor\nRails.application.configure do\n  config.middleware.swap ActionDispatch::Executor, GuardedExecutor, executor\nend\n```\n", "cvss_v2": null, "cvss_v3": 7.4, "cve": "2022-23633", "osvdb": null, "ghsa": "wh98-p28r-vrc9", "unaffected_versions": ["< 5.0.0"], "patched_versions": ["~> 5.2.6, >= *******", "~> 6.0.4, >= *******", "~> 6.1.4, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2021-44528.yml", "id": "CVE-2021-44528", "url": "https://groups.google.com/g/ruby-security-ann/c/vG9gz3nk1pM/m/7-NU4MNrDAAJ", "title": "Possible Open Redirect in Host Authorization Middleware", "date": "2021-12-14", "description": "There is a possible open redirect vulnerability in the Host Authorization\nmiddleware in Action Pack.\n\nSpecially crafted \"X-Forwarded-Host\" headers in combination with certain\n\"allowed host\" formats can cause the Host Authorization middleware in Action\nPack to redirect users to a malicious website.\n\nImpacted applications will have allowed hosts with a leading dot. For example,\nconfiguration files that look like this:\n\n```\nconfig.hosts <<  '.EXAMPLE.com'\n```\n\nWhen an allowed host contains a leading dot, a specially crafted Host header\ncan be used to redirect to a malicious website.\n\nThis vulnerability is similar to CVE-2021-22881 and CVE-2021-22942.\n\nReleases\n--------\nThe fixed releases are available at the normal locations.\n\nPatches\n-------\nTo aid users who aren't able to upgrade immediately we have provided patches for\nthe two supported release series. They are in git-am format and consist of a\nsingle changeset.\n\n* 6-0-host-authorzation-open-redirect.patch - Patch for 6.0 series\n* 6-1-host-authorzation-open-redirect.patch - Patch for 6.1 series\n* 7-0-host-authorzation-open-redirect.patch - Patch for 7.0 series\n\nPlease note that only the 6.1.Z, 6.0.Z, and 5.2.Z series are supported at\npresent. Users of earlier unsupported releases are advised to upgrade as soon\nas possible as we cannot guarantee the continued availability of security\nfixes for unsupported releases.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2021-44528", "osvdb": null, "ghsa": "qphc-hf5q-v8fc", "unaffected_versions": ["< 6.0.0"], "patched_versions": ["~> 6.0.4, >= *******", "~> 6.1.4, >= *******", ">= 7.0.0.rc2"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2020-8264.yml", "id": "CVE-2020-8264", "url": "https://groups.google.com/g/rubyonrails-security/c/yQzUVfv42jk", "title": "Possible XSS Vulnerability in Action Pack in Development Mode", "date": "2020-10-07", "description": "There is a possible XSS vulnerability in Action Pack while the application\nserver is in development mode.  This vulnerability is in the Actionable\nExceptions middleware.  This vulnerability has been assigned the CVE\nidentifier CVE-2020-8264.\n\nVersions Affected:  >= 6.0.0\nNot affected:       < 6.0.0\nFixed Versions:     *******\n\nImpact\n------\nWhen an application is running in development mode, and attacker can send or\nembed (in another page) a specially crafted URL which can allow the attacker\nto execute JavaScript in the context of the local application.\n\nWorkarounds\n-----------\nUntil such time as the patch can be applied, application developers should\ndisable the Actionable Exceptions middleware in their development environment via\na line such as this one in their config/environment/development.rb:\n\n`config.middleware.delete ActionDispatch::ActionableExceptions`\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2020-8264", "osvdb": null, "ghsa": "35mm-cc6r-8fjp", "unaffected_versions": ["< 6.0.0"], "patched_versions": [">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionpack", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionpack/CVE-2021-22902.yml", "id": "CVE-2021-22902", "url": "https://groups.google.com/g/rubyonrails-security/c/_5ID_ld9u1c", "title": "Possible Denial of Service vulnerability in Action Dispatch", "date": "2021-05-05", "description": "There is a possible Denial of Service vulnerability in the Mime type parser of\nAction Dispatch. This vulnerability has been assigned the CVE identifier\nCVE-2021-22902.\n\nVersions Affected:  >= 6.0.0\nNot affected:       < 6.0.0\nFixed Versions:     *******, *******\n\nImpact\n------\nThere is a possible Denial of Service vulnerability in Action Dispatch.\nCarefully crafted Accept headers can cause the mime type parser in Action\nDispatch to do catastrophic backtracking in the regular expression engine.\n\nWorkarounds\n-----------\nThe following monkey patch placed in an initializer can be used to work around\nthe issue:\n\n```ruby\nmodule Mime\n  class Type\n    MIME_REGEXP = /\\A(?:\\*\\/\\*|#{MIME_NAME}\\/(?:\\*|#{MIME_NAME})(?>\\s*#{MIME_PARAMETER}\\s*)*)\\z/\n  end\nend\n```\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2021-22902", "osvdb": null, "ghsa": "g8ww-46x2-2p65", "unaffected_versions": ["< 6.0.0"], "patched_versions": ["~> 6.0.3, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "actionview", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionview/CVE-2020-15169.yml", "id": "CVE-2020-15169", "url": "https://groups.google.com/g/rubyonrails-security/c/b-C9kSGXYrc", "title": "Potential XSS vulnerability in Action View", "date": "2020-09-09", "description": "There is a potential Cross-Site Scripting (XSS) vulnerability in Action\nView's translation helpers. Views that allow the user to control the\ndefault (not found) value of the `t` and `translate` helpers could be\nsusceptible to XSS attacks.\n\nImpact\n------\n\nWhen an HTML-unsafe string is passed as the default for a missing\ntranslation key [named `html` or ending in `_html`](https://guides.rubyonrails.org/i18n.html#using-safe-html-translations),\nthe default string is incorrectly marked as HTML-safe and not escaped.\nVulnerable code may look like the following examples:\n\n```erb\n<%# The welcome_html translation is not defined for the current locale: %>\n<%= t(\"welcome_html\", default: untrusted_user_controlled_string) %>\n\n<%# Neither the title.html translation nor the missing.html translation is defined for the current locale: %>\n<%= t(\"title.html\", default: [:\"missing.html\", untrusted_user_controlled_string]) %>\n```\n\nWorkarounds\n-----------\nImpacted users who can’t upgrade to a patched Rails version can avoid\nthis issue by manually escaping default translations with the\n`html_escape` helper (aliased as `h`):\n\n```erb\n<%= t(\"welcome_html\", default: h(untrusted_user_controlled_string)) %>\n```\n", "cvss_v2": null, "cvss_v3": 5.4, "cve": "2020-15169", "osvdb": null, "ghsa": "cfjv-5498-mph5", "unaffected_versions": [], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionview", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionview/CVE-2020-5267.yml", "id": "CVE-2020-5267", "url": "https://groups.google.com/forum/#!topic/rubyonrails-security/55reWMM_Pg8", "title": "Possible XSS vulnerability in ActionView", "date": "2020-03-19", "description": "There is a possible XSS vulnerability in ActionView's JavaScript literal\nescape helpers.  Views that use the `j` or `escape_javascript` methods\nmay be susceptible to XSS attacks.\n\nVersions Affected:  All.\nNot affected:       None.\nFixed Versions:     *******, *******\n\nImpact\n------\nThere is a possible XSS vulnerability in the `j` and `escape_javascript`\nmethods in ActionView.  These methods are used for escaping JavaScript string\nliterals.  Impacted code will look something like this:\n\n```erb\n<script>let a = `<%= j unknown_input %>`</script>\n```\n\nor\n\n```erb\n<script>let a = `<%= escape_javascript unknown_input %>`</script>\n```\n\nReleases\n--------\nThe ******* and ******* releases are available at the normal locations.\n\nWorkarounds\n-----------\nFor those that can't upgrade, the following monkey patch may be used:\n\n```ruby\nActionView::Helpers::JavaScriptHelper::JS_ESCAPE_MAP.merge!(\n  {\n    \"`\" => \"\\\\`\",\n    \"$\" => \"\\\\$\"\n  }\n)\n\nmodule ActionView::Helpers::JavaScriptHelper\n  alias :old_ej :escape_javascript\n  alias :old_j :j\n\n  def escape_javascript(javascript)\n    javascript = javascript.to_s\n    if javascript.empty?\n      result = \"\"\n    else\n      result = javascript.gsub(/(\\\\|<\\/|\\r\\n|\\342\\200\\250|\\342\\200\\251|[\\n\\r\"']|[`]|[$])/u, JS_ESCAPE_MAP)\n    end\n    javascript.html_safe? ? result.html_safe : result\n  end\n\n  alias :j :escape_javascript\nend\n```\n", "cvss_v2": null, "cvss_v3": 4.0, "cve": "2020-5267", "osvdb": null, "ghsa": "65cv-r6x7-79hv", "unaffected_versions": [], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionview", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionview/CVE-2022-27777.yml", "id": "CVE-2022-27777", "url": "https://groups.google.com/g/ruby-security-ann/c/9wJPEDv-iRw", "title": "Possible XSS Vulnerability in Action View tag helpers", "date": "2022-04-26", "description": "There is a possible XSS vulnerability in Action View tag helpers.  Passing\nuntrusted input as hash keys can lead to a possible XSS vulnerability. This\nvulnerability has been assigned the CVE identifier CVE-2022-27777.\n\nVersions Affected:  ALL\nNot affected:       NONE\nFixed Versions:     *******, *******, *******, *******\n\n## Impact\n\nIf untrusted data is passed as the hash key for tag attributes, there is a\npossibility that the untrusted data may not be properly escaped which can\nlead to an XSS vulnerability.\n\nImpacted code will look something like this:\n\n```\ncheck_box_tag('thename', 'thevalue', false, aria: { malicious_input => 'thevalueofaria' })\n```\n\nWhere the \"malicious_input\" variable contains untrusted data.\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\n## Releases\n\nThe FIXED releases are available at the normal locations.\n\n## Workarounds\n\nEscape the untrusted data before using it as a key for tag helper methods.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-27777", "osvdb": null, "ghsa": "ch3h-j2vf-95pv", "unaffected_versions": [], "patched_versions": ["~> 5.2.7, >= *******", "~> 6.0.4, >= *******", "~> 6.1.5, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionview", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionview/CVE-2020-8167.yml", "id": "CVE-2020-8167", "url": "https://groups.google.com/forum/#!topic/rubyonrails-security/x9DixQDG9a0", "title": "CSRF Vulnerability in rails-ujs", "date": "2020-05-18", "description": "There is an vulnerability in rails-ujs that allows attackers to send\nCSRF tokens to wrong domains.\n\nVersions Affected:  rails <= 6.0.3\nNot affected:       Applications which don't use rails-ujs.\nFixed Versions:     rails >= *******, rails >= *******\n\nImpact\n------\n\nThis is a regression of CVE-2015-1840.\n\nIn the scenario where an attacker might be able to control the href attribute of an anchor tag or\nthe action attribute of a form tag that will trigger a POST action, the attacker can set the\nhref or action to a cross-origin URL, and the CSRF token will be sent.\n\nWorkarounds\n-----------\n\nTo work around this problem, change code that allows users to control the href attribute of an anchor\ntag or the action attribute of a form tag to filter the user parameters.\n\nFor example, code like this:\n\n    link_to params\n\nto code like this:\n\n    link_to filtered_params\n\n    def filtered_params\n      # Filter just the parameters that you trust\n    end\n", "cvss_v2": null, "cvss_v3": 6.5, "cve": "2020-8167", "osvdb": null, "ghsa": "xq5j-gw7f-jgj8", "unaffected_versions": [], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "actionview", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/actionview/CVE-2023-23913.yml", "id": "CVE-2023-23913", "url": "https://discuss.rubyonrails.org/t/cve-2023-23913-dom-based-cross-site-scripting-in-rails-ujs-for-contenteditable-html-elements/82468", "title": "DOM Based Cross-site Scripting in rails-ujs for contenteditable HTML Elements", "date": "2023-03-13", "description": "NOTE: rails-ujs is part of Rails/actionview since 5.1.0.\n\nThere is a potential DOM based cross-site scripting issue in rails-ujs\nwhich leverages the Clipboard API to target HTML elements that are\nassigned the contenteditable attribute. This has the potential to\noccur when pasting malicious HTML content from the clipboard that\nincludes a data-method, data-remote or data-disable-with attribute.\n\nThis vulnerability has been assigned the CVE identifier CVE-2023-23913.\n\nNot affected: < 5.1.0\nVersions Affected: >= 5.1.0\nFixed Versions: *******, *******\n\nImpact\n  If the specified malicious HTML clipboard content is provided to a\n  contenteditable element, this could result in the arbitrary execution\n  of javascript on the origin in question.\n\nReleases\n  The FIXED releases are available at the normal locations.\n\nWorkarounds\n  We recommend that all users upgrade to one of the FIXED versions.\n  In the meantime, users can attempt to mitigate this vulnerability\n  by removing the contenteditable attribute from elements in pages\n  that rails-ujs will interact with.\n\nPatches\n  To aid users who aren’t able to upgrade immediately we have provided\n  patches for the two supported release series. They are in git-am\n  format and consist of a single changeset.\n\n* rails-ujs-data-method-contenteditable-6-1.patch - Patch for 6.1 series\n* rails-ujs-data-method-contenteditable-7-0.patch - Patch for 7.0 series\n\nPlease note that only the 7.0.Z and 6.1.Z series are\nsupported at present, and 6.0.Z for severe vulnerabilities.\n\nUsers of earlier unsupported releases are advised to upgrade as\nsoon as possible as we cannot guarantee the continued availability\nof security fixes for unsupported releases.\n\nCredits\n  We would like to thank ryotak 15 for reporting this!\n\n* rails-ujs-data-method-contenteditable-6-1.patch (8.5 KB)\n* rails-ujs-data-method-contenteditable-7-0.patch (8.5 KB)\n* rails-ujs-data-method-contenteditable-main.patch (8.9 KB)\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2023-23913", "osvdb": null, "ghsa": "xp5h-f8jf-rc8q", "unaffected_versions": ["< 5.1.0"], "patched_versions": ["~> *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "activerecord", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activerecord/CVE-2023-22794.yml", "id": "CVE-2023-22794", "url": "https://github.com/rails/rails/releases/tag/v*******", "title": "SQL Injection Vulnerability via ActiveRecord comments", "date": "2023-01-18", "description": "There is a possible vulnerability in ActiveRecord related to the\nsanitization of comments. This vulnerability has been assigned the CVE\nidentifier CVE-2023-22794.\n\nVersions Affected: >= 6.0.0\nNot affected: < 6.0.0\nFixed Versions: *******, *******, *******\n\n# Impact\n\nPreviously the implementation of escaping for comments was insufficient for\n\nIf malicious user input is passed to either the annotate query method, the\noptimizer_hints query method, or through the QueryLogs interface which\nautomatically adds annotations, it may be sent to the database with\ninsufficient sanitization and be able to inject SQL outside of the comment.\n\nIn most cases these interfaces won’t be used with user input and users\nshould avoid doing so.\n\nExample vulnerable code:\n```\nPost.where(id: 1).annotate(\"#{params[:user_input]}\")\n\nPost.where(id: 1).optimizer_hints(\"#{params[:user_input]}\")\n```\n\nExample vulnerable QueryLogs configuration (the default configuration is not\nvulnerable):\n```\nconfig.active_record.query_log_tags = [\n  {\n    something: -> { <some value including user input> }\n  }\n]\n```\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\n# Workarounds\n\nAvoid passing user input to annotate and avoid using QueryLogs configuration\nwhich can include user input.\n", "cvss_v2": null, "cvss_v3": 8.8, "cve": "2023-22794", "osvdb": null, "ghsa": "hq7p-j377-6v63", "unaffected_versions": ["< 6.0.0"], "patched_versions": ["~> 6.0.6, >= *******", "~> 6.1.7, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "activerecord", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activerecord/CVE-2021-22880.yml", "id": "CVE-2021-22880", "url": "https://groups.google.com/g/rubyonrails-security/c/ZzUqCh9vyhI", "title": "Possible DoS Vulnerability in Active Record PostgreSQL adapter", "date": "2021-02-10", "description": "There is a possible DoS vulnerability in the PostgreSQL adapter in Active\nRecord. This vulnerability has been assigned the CVE identifier CVE-2021-22880.\n\nVersions Affected:  >= 4.2.0\nNot affected:       < 4.2.0\nFixed Versions:     *******, *******, *******\n\nImpact\n------\nCarefully crafted input can cause the input validation in the \"money\" type of\nthe PostgreSQL adapter in Active Record to spend too much time in a regular\nexpression, resulting in the potential for a DoS attack.\n\nThis only impacts Rails applications that are using PostgreSQL along with\nmoney type columns that take user input.\n\nWorkarounds\n-----------\nIn the case a patch can't be applied, the following monkey patch can be used\nin an initializer:\n\n```\nmodule ActiveRecord\n  module ConnectionAdapters\n    module PostgreSQL\n      module OID # :nodoc:\n        class Money < Type::Decimal # :nodoc:\n          def cast_value(value)\n            return value unless ::String === value\n\n            value = value.sub(/^\\((.+)\\)$/, '-\\1') # (4)\n            case value\n            when /^-?\\D*+[\\d,]+\\.\\d{2}$/  # (1)\n              value.gsub!(/[^-\\d.]/, \"\")\n            when /^-?\\D*+[\\d.]+,\\d{2}$/  # (2)\n              value.gsub!(/[^-\\d,]/, \"\").sub!(/,/, \".\")\n            end\n\n            super(value)\n          end\n        end\n      end\n    end\n  end\nend\n```\n", "cvss_v2": null, "cvss_v3": 5.3, "cve": "2021-22880", "osvdb": null, "ghsa": "8hc4-xxm3-5ppp", "unaffected_versions": ["< 4.2.0"], "patched_versions": ["~> 5.2.4, >= *******", "~> 6.0.3, >= *******", ">= *******"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "activerecord", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activerecord/CVE-2022-44566.yml", "id": "CVE-2022-44566", "url": "https://github.com/rails/rails/releases/tag/v*******", "title": "Denial of Service Vulnerability in ActiveRecord’s PostgreSQL adapter", "date": "2023-01-18", "description": "There is a potential denial of service vulnerability present in\nActiveRecord’s PostgreSQL adapter.\n\nThis has been assigned the CVE identifier CVE-2022-44566.\n\nVersions Affected: All.\nNot affected: None.\nFixed Versions: ******** (Rails LTS), *******, *******\n\n# Impact\n\nIn ActiveRecord <******* and <*******, when a value outside the range for a\n64bit signed integer is provided to the PostgreSQL connection adapter, it\nwill treat the target column type as numeric. Comparing integer values\nagainst numeric values can result in a slow sequential scan resulting in\npotential Denial of Service.\n\n# Workarounds\n\nEnsure that user supplied input which is provided to ActiveRecord clauses do\nnot contain integers wider than a signed 64bit representation or floats.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-44566", "osvdb": null, "ghsa": "579w-22j4-4749", "unaffected_versions": [], "patched_versions": ["~> 5.2.8, >= ********", "~> 6.1.7, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "activerecord", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activerecord/CVE-2022-32224.yml", "id": "CVE-2022-32224", "url": "https://groups.google.com/g/rubyonrails-security/c/MmFO3LYQE8U", "title": "Possible RCE escalation bug with Serialized Columns in Active Record", "date": "2022-07-12", "description": "There is a possible escalation to RCE when using YAML serialized columns in\nActive Record. This vulnerability has been assigned the CVE identifier\nCVE-2022-32224.\n\nVersions Affected:  All.\nNot affected:       None\nFixed Versions:     *******, *******, *******, *******\n\nImpact\n------\nWhen serialized columns that use YAML (the default) are deserialized, Rails\nuses `YAML.unsafe_load` to convert the YAML data in to Ruby objects.  If an\nattacker can manipulate data in the database (via means like SQL injection),\nthen it may be possible for the attacker to escalate to an RCE.\n\nImpacted Active Record models will look something like this:\n\n```ruby\nclass User < ApplicationRecord\n  serialize :options       # Vulnerable: Uses YAML for serialization\n  serialize :values, Array # Vulnerable: Uses YAML for serialization\n  serialize :values, JSON  # Not vulnerable\nend\n```\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\nReleases\n--------\nThe FIXED releases are available at the normal locations.\n\nThe released versions change the default YAML deserializer to use\n`YAML.safe_load`, which prevents deserialization of possibly dangerous\nobjects.  This may introduce backwards compatibility issues with existing\ndata.\n\nIn order to cope with that situation, the released version also contains two\nnew Active Record configuration options.  The configuration options are as\nfollows:\n\n* `config.active_record.use_yaml_unsafe_load`\n\nWhen set to true, this configuration option tells Rails to use the old\n\"unsafe\" YAML loading strategy, maintaining the existing behavior but leaving\nthe possible escalation vulnerability in place.  Setting this option to true\nis *not* recommended, but can aid in upgrading.\n\n* `config.active_record.yaml_column_permitted_classes`\n\nThe \"safe YAML\" loading method does not allow all classes to be deserialized\nby default.  This option allows you to specify classes deemed \"safe\" in your\napplication.  For example, if your application uses Symbol and Time in\nserialized data, you can add Symbol and Time to the allowed list as follows:\n\n```\nconfig.active_record.yaml_column_permitted_classes = [Symbol, Date, Time]\n```\n\nWorkarounds\n-----------\nThere are no feasible workarounds for this issue, but other coders (such as\nJSON) are not impacted.\n", "cvss_v2": null, "cvss_v3": 9.8, "cve": "2022-32224", "osvdb": null, "ghsa": "3hhc-qp5v-9p2j", "unaffected_versions": [], "patched_versions": ["~> 5.2.8, >= *******", "~> 6.0.5, >= *******", "~> 6.1.6, >= *******", ">= *******"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "activestorage", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activestorage/CVE-2022-21831.yml", "id": "CVE-2022-21831", "url": "https://groups.google.com/g/rubyonrails-security/c/n-p-W1yxatI", "title": "Possible code injection vulnerability in Rails / Active Storage", "date": "2022-03-08", "description": "There is a possible code injection vulnerability in the Active Storage module\nof Rails. This vulnerability has been assigned the CVE identifier\nCVE-2022-21831.\n\nVersions Affected:  >= 5.2.0\nNot affected:       < 5.2.0\nFixed Versions:     *******, *******, *******, *******\n\nImpact\n------\nThere is a possible code injection vulnerability in the Active Storage module\nof Rails.  This vulnerability impacts applications that use Active Storage\nwith the image_processing processing in addition to the mini_magick back end\nfor image_processing.\n\nVulnerable code will look something similar to this:\n\n```ruby\n<%= image_tag blob.variant(params[:t] => params[:v]) %>\n```\n\nWhere the transformation method or its arguments are untrusted arbitrary\ninput.\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\nWorkarounds\n-----------\nTo work around this issue, applications should implement a strict allow-list\non accepted transformation methods or arguments.  Additionally, a strict image\nmagick security policy will help mitigate this issue.\n\n  https://imagemagick.org/script/security-policy.php\n", "cvss_v2": null, "cvss_v3": 9.8, "cve": "2022-21831", "osvdb": null, "ghsa": "w749-p3v6-hccq", "unaffected_versions": ["< 5.2.0"], "patched_versions": ["~> 5.2.6, >= *******", "~> 6.0.4, >= *******", "~> 6.1.4, >= *******", ">= *******"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "activestorage", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activestorage/CVE-2020-8162.yml", "id": "CVE-2020-8162", "url": "https://groups.google.com/forum/#!topic/rubyonrails-security/PjU3946mreQ", "title": "Circumvention of file size limits in ActiveStorage", "date": "2020-05-18", "description": "There is a vulnerability in ActiveStorage's S3 adapter that allows the Content-Length of a\ndirect file upload to be modified by an end user.\n\nVersions Affected:  rails < *******, rails < *******\nNot affected:       Applications that do not use the direct upload functionality of the ActiveStorage S3 adapter.\nFixed Versions:     rails >= *******, rails >= *******\n\nImpact\n------\n\nUtilizing this vulnerability, an attacker can control the Content-Length of an S3 direct upload URL without receiving a\nnew signature from the server. This could be used to bypass controls in place on the server to limit upload size.\n\nWorkarounds\n-----------\n\nThis is a low-severity security issue. As such, no workaround is necessarily\nuntil such time as the application can be upgraded.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2020-8162", "osvdb": null, "ghsa": "m42x-37p3-fv5w", "unaffected_versions": [], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "activesupport", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activesupport/CVE-2023-22796.yml", "id": "CVE-2023-22796", "url": "https://github.com/rails/rails/releases/tag/v*******", "title": "ReDoS based DoS vulnerability in Active Support’s underscore", "date": "2023-01-18", "description": "There is a possible regular expression based DoS vulnerability in Active\nSupport. This vulnerability has been assigned the CVE identifier\nCVE-2023-22796.\n\nVersions Affected: All\nNot affected: None\nFixed Versions: ******** (Rails LTS), *******, *******\n\n# Impact\n\nA specially crafted string passed to the underscore method can cause the\nregular expression engine to enter a state of catastrophic backtracking.\nThis can cause the process to use large amounts of CPU and memory, leading\nto a possible DoS vulnerability.\n\nThis affects String#underscore, ActiveSupport::Inflector.underscore,\nString#titleize, and any other methods using these.\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\n# Workarounds\n\nThere are no feasible workarounds for this issue.\n\nUsers on Ruby 3.2.0 or greater may be able to reduce the impact by\nconfiguring Regexp.timeout.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-22796", "osvdb": null, "ghsa": "j6gc-792m-qgm2", "unaffected_versions": [], "patched_versions": ["~> 5.2.8, >= ********", "~> 6.1.7, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "activesupport", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activesupport/CVE-2023-28120.yml", "id": "CVE-2023-28120", "url": "https://discuss.rubyonrails.org/t/cve-2023-28120-possible-xss-security-vulnerability-in-safebuffer-bytesplice/82469", "title": "Possible XSS Security Vulnerability in SafeBuffer#bytesplice", "date": "2023-03-13", "description": "There is a vulnerability in ActiveSupport if the new bytesplice method is called on a SafeBuffer with untrusted user input.\nThis vulnerability has been assigned the CVE identifier CVE-2023-28120.\n\nVersions Affected: All. Not affected: None Fixed Versions: *******, *******\n\n# Impact\n\nActiveSupport uses the SafeBuffer string subclass to tag strings as html_safe after they have been sanitized.\nWhen these strings are mutated, the tag is should be removed to mark them as no longer being html_safe.\n\nRuby 3.2 introduced a new bytesplice method which ActiveSupport did not yet understand to be a mutation.\nUsers on older versions of Ruby are likely unaffected.\n\nAll users running an affected release and using bytesplice should either upgrade or use one of the workarounds immediately.\n\n# Workarounds\n\nAvoid calling bytesplice on a SafeBuffer (html_safe) string with untrusted user input.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-28120", "osvdb": null, "ghsa": "pj73-v5mw-pm9j", "unaffected_versions": [], "patched_versions": ["~> 6.1.7, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "activesupport", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activesupport/CVE-2023-38037.yml", "id": "CVE-2023-38037", "url": "https://github.com/rails/rails/releases/tag/v*******", "title": "Possible File Disclosure of Locally Encrypted Files", "date": "2023-08-23", "description": "There is a possible file disclosure of locally encrypted files in Active Support. This vulnerability has been assigned the CVE identifier CVE-2023-38037.\n\nVersions Affected: >= 5.2.0 Not affected: < 5.2.0 Fixed Versions: *******, *******\n\n# Impact\nActiveSupport::EncryptedFile writes contents that will be encrypted to a temporary file. The temporary file’s permissions are defaulted to the user’s current umask settings, meaning that it’s possible for other users on the same system to read the contents of the temporary file.\n\nAttackers that have access to the file system could possibly read the contents of this temporary file while a user is editing it.\n\nAll users running an affected release should either upgrade or use one of the workarounds immediately.\n\n# Releases\nThe fixed releases are available at the normal locations.\n\n# Workarounds\nTo work around this issue, you can set your umask to be more restrictive like this:\n\n```ruby\n$ umask 0077\n```\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-38037", "osvdb": null, "ghsa": "cr5q-6q9f-rq6q", "unaffected_versions": ["< 5.2.0"], "patched_versions": ["~> 6.1.7, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "activesupport", "version": "*******"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/activesupport/CVE-2020-8165.yml", "id": "CVE-2020-8165", "url": "https://groups.google.com/forum/#!topic/rubyonrails-security/bv6fW4S0Y1c", "title": "Potentially unintended unmarshalling of user-provided objects in MemCacheStore and RedisCacheStore", "date": "2020-05-18", "description": "There is potentially unexpected behaviour in the MemCacheStore and RedisCacheStore where, when\nuntrusted user input is written to the cache store using the `raw: true` parameter, re-reading the result\nfrom the cache can evaluate the user input as a Marshalled object instead of plain text. Vulnerable code looks like:\n\n```\ndata = cache.fetch(\"demo\", raw: true) { untrusted_string }\n```\n\nVersions Affected:  rails < 5.2.5, rails < 6.0.4\nNot affected:       Applications not using MemCacheStore or RedisCacheStore. Applications that do not use the `raw` option when storing untrusted user input.\nFixed Versions:     rails >= *******, rails >= *******\n\nImpact\n------\n\nUnmarshalling of untrusted user input can have impact up to and including RCE. At a minimum,\nthis vulnerability allows an attacker to inject untrusted Ruby objects into a web application.\n\nIn addition to upgrading to the latest versions of Rails, developers should ensure that whenever\nthey are calling `Rails.cache.fetch` they are using consistent values of the `raw` parameter for both\nreading and writing, especially in the case of the RedisCacheStore which does not, prior to these changes,\ndetect if data was serialized using the raw option upon deserialization.\n\nWorkarounds\n-----------\n\nIt is recommended that application developers apply the suggested patch or upgrade to the latest release as\nsoon as possible. If this is not possible, we recommend ensuring that all user-provided strings cached using\nthe `raw` argument should be double-checked to ensure that they conform to the expected format.\n", "cvss_v2": null, "cvss_v3": 9.8, "cve": "2020-8165", "osvdb": null, "ghsa": "2p68-f74v-9wc6", "unaffected_versions": [], "patched_versions": ["~> 5.2.4, >= *******", ">= *******"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "addressable", "version": "2.7.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/addressable/CVE-2021-32740.yml", "id": "CVE-2021-32740", "url": "https://github.com/advisories/GHSA-jxhc-q857-3j6g", "title": "Regular Expression Denial of Service in Addressable templates", "date": "2021-07-12", "description": "Within the URI template implementation in Addressable, a maliciously crafted template may result in uncontrolled resource consumption,\nleading to denial of service when matched against a URI. In typical usage, templates would not normally be read from untrusted user input,\nbut nonetheless, no previous security advisory for Addressable has cautioned against doing this.\nUsers of the parsing capabilities in Addressable but not the URI template capabilities are unaffected.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2021-32740", "osvdb": null, "ghsa": "jxhc-q857-3j6g", "unaffected_versions": ["< 2.3.0"], "patched_versions": [">= 2.8.0"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "better_errors", "version": "2.5.1"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/better_errors/CVE-2021-39197.yml", "id": "CVE-2021-39197", "url": "https://github.com/BetterErrors/better_errors/security/advisories/GHSA-w3j4-76qw-wwjm", "title": "Older releases of better_errors open to Cross-Site Request Forgery attack", "date": "2021-09-07", "description": "### Impact\nbetter_errors prior to 2.8.0 did not implement CSRF protection for its internal requests. It also did not enforce the correct \"Content-Type\" header for these requests, which allowed a cross-origin \"simple request\" to be made without CORS protection. These together left an application with better_errors enabled open to cross-origin attacks.\n\n_As a developer tool, better_errors documentation strongly recommends addition only to the `development` bundle group, so this vulnerability should only affect development environments. Please ensure that your project limits better_errors to the `development` group (or the non-Rails equivalent)._\n\n### Patches\nStarting with release 2.8.x, CSRF protection is enforced. It is recommended that you upgrade to the latest release, or minimally to \"~> 2.8.3\".\n\n### Workarounds\nThere are no known workarounds to mitigate the risk of using older releases of better_errors.\n\n### References\n- <PERSON> provided [an example attack that uses a now-patched vulnerability of webpack-dev-server in conjunction with Better Errors](https://about.gitlab.com/blog/2021/09/07/why-are-developers-vulnerable-to-driveby-attacks/)\n\n### For more information\nIf you have any questions or comments about this advisory, please\n- Add to the [discussion in better_errors](https://github.com/BetterErrors/better_errors/discussions/507)\n- Open an issue in [better_errors](https://github.com/BetterErrors/better_errors)\n", "cvss_v2": null, "cvss_v3": 6.3, "cve": "2021-39197", "osvdb": null, "ghsa": "w3j4-76qw-wwjm", "unaffected_versions": [], "patched_versions": [">= 2.8.0"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "doorkeeper", "version": "5.2.6"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/doorkeeper/CVE-2023-34246.yml", "id": "CVE-2023-34246", "url": "https://github.com/advisories/GHSA-7w2c-w47h-789w", "title": "Doorkeeper Improper Authentication vulnerability", "date": "2023-06-12", "description": "OAuth RFC 8252 says  https://www.rfc-editor.org/rfc/rfc8252#section-8.6\n\n> the authorization server SHOULD NOT process authorization requests\n> automatically without user consent or interaction, except when the\n> identity of the client can be assured. **This includes the case\n> where the user has previously approved an authorization request\n> for a given client id**\n\nBut Doorkeeper automatically processes authorization requests without\nuser consent for public clients that have been previous approved.\nPublic clients are inherently  vulnerable to impersonation, their\nidentity cannot be assured.\n\nIssue https://github.com/doorkeeper-gem/doorkeeper/issues/1589\n\nFix https://github.com/doorkeeper-gem/doorkeeper/pull/1646\n", "cvss_v2": null, "cvss_v3": 4.2, "cve": "2023-34246", "osvdb": null, "ghsa": "7w2c-w47h-789w", "unaffected_versions": [], "patched_versions": [">= 5.6.6"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "globalid", "version": "0.4.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/globalid/CVE-2023-22799.yml", "id": "CVE-2023-22799", "url": "https://github.com/rails/globalid/releases/tag/v1.0.1", "title": "ReDoS based DoS vulnerability in GlobalID", "date": "2023-01-18", "description": "There is a ReDoS based DoS vulnerability in the GlobalID gem. This\nvulnerability has been assigned the CVE identifier CVE-2023-22799.\n\nVersions Affected: >= 0.2.1\nNot affected: < 0.2.1\nFixed Versions: 1.0.1\n\n# Impact\n\nThere is a possible DoS vulnerability in the model name parsing section\nof the GlobalID gem. Carefully crafted input can cause the regular\nexpression engine to take an unexpected amount of time. All users running\nan affected release should either upgrade or use one of the workarounds\nimmediately.\n\n# Workarounds\n\nThere are no feasible workarounds for this issue.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-22799", "osvdb": null, "ghsa": "23c2-gwp5-pxw9", "unaffected_versions": ["< 0.2.1"], "patched_versions": [">= 1.0.1"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "image_processing", "version": "1.10.1"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/image_processing/CVE-2022-24720.yml", "id": "CVE-2022-24720", "url": "https://github.com/janko/image_processing/security/advisories/GHSA-cxf7-qrc5-9446", "title": "Remote shell execution vulnerability when applying commands from user input", "date": "2022-03-01", "description": "### Impact\n\nWhen using the `#apply` method from image_processing to apply a series of operations that are coming from unsanitized user input, this allows the attacker to execute shell commands:\n\n```rb\nImageProcessing::Vips.apply({ system: \"echo EXECUTED\" })\n#>> EXECUTED\n```\n\nThis method is called internally by Active Storage variants, so Active Storage is vulnerable as well.\n\n### Patches\n\nThe vulnerability has been fixed in version 1.12.2 of image_processing.\n\n### Workarounds\n\nIf you're processing based on user input, it's highly recommended that you always sanitize the user input, by allowing only a constrained set of operations. For example:\n\n```rb\noperations = params[:operations]\n  .map { |operation| [operation[:name], *operation[:value]] }\n  .select { |name, *| name.to_s.include? %w[resize_to_limit strip ...] } # sanitization\n\nImageProcessing::Vips.apply(operations)\n```\n", "cvss_v2": null, "cvss_v3": 9.8, "cve": "2022-24720", "osvdb": null, "ghsa": "cxf7-qrc5-9446", "unaffected_versions": [], "patched_versions": [">= 1.12.2"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "jmespath", "version": "1.4.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/jmespath/CVE-2022-32511.yml", "id": "CVE-2022-32511", "url": "https://github.com/jmespath/jmespath.rb/pull/55", "title": "JMESPath for Ruby using JSON.load instead of JSON.parse", "date": "2022-06-07", "description": "jmespath.rb (aka JMESPath for Ruby) before 1.6.1 uses JSON.load in a\nsituation where JSON.parse is preferable.\n", "cvss_v2": null, "cvss_v3": 9.8, "cve": "2022-32511", "osvdb": null, "ghsa": "5c5f-7vfq-3732", "unaffected_versions": [], "patched_versions": [">= 1.6.1"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "loofah", "version": "2.4.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/loofah/CVE-2022-23515.yml", "id": "CVE-2022-23515", "url": "https://github.com/flavorjones/loofah/security/advisories/GHSA-228g-948r-83gx", "title": "Improper neutralization of data URIs may allow XSS in Loofah", "date": "2022-12-13", "description": "## Summary\n\nLoofah `>= 2.1.0, < 2.19.1` is vulnerable to cross-site scripting via the `image/svg+xml` media type in data URIs.\n\n## Mitigation\n\nUpgrade to Loofah `>= 2.19.1`.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-23515", "osvdb": null, "ghsa": "228g-948r-83gx", "unaffected_versions": ["< 2.1.0"], "patched_versions": [">= 2.19.1"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "loofah", "version": "2.4.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/loofah/CVE-2022-23514.yml", "id": "CVE-2022-23514", "url": "https://github.com/flavorjones/loofah/security/advisories/GHSA-486f-hjj9-9vhh", "title": "Inefficient Regular Expression Complexity in Loofah", "date": "2022-12-13", "description": "## Summary\n\nLoofah `< 2.19.1` contains an inefficient regular expression that is susceptible to excessive backtracking when attempting to sanitize certain SVG attributes. This may lead to a denial of service through CPU resource consumption.\n\n## Mitigation\n\nUpgrade to Loofah `>= 2.19.1`.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-23514", "osvdb": null, "ghsa": "486f-hjj9-9vhh", "unaffected_versions": [], "patched_versions": [">= 2.19.1"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "loofah", "version": "2.4.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/loofah/CVE-2022-23516.yml", "id": "CVE-2022-23516", "url": "https://github.com/flavorjones/loofah/security/advisories/GHSA-3x8r-x6xp-q4vm", "title": "Uncontrolled Recursion in Loofah", "date": "2022-12-13", "description": "## Summary\n\nLoofah `>= 2.2.0, < 2.19.1` uses recursion for sanitizing `CDATA` sections, making it susceptible to stack exhaustion and raising a `SystemStackError` exception.  This may lead to a denial of service through CPU resource consumption.\n\n## Mitigation\n\nUpgrade to Loofah `>= 2.19.1`.\n\nUsers who are unable to upgrade may be able to mitigate this vulnerability by limiting the length of the strings that are sanitized.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-23516", "osvdb": null, "ghsa": "3x8r-x6xp-q4vm", "unaffected_versions": ["< 2.2.0"], "patched_versions": [">= 2.19.1"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2018-25032.yml", "id": "CVE-2018-25032", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-v6gp-9mmm-c6p5", "title": "Out-of-bounds Write in zlib affects Nokogiri", "date": "2022-04-11", "description": "## Summary\n\nNokogiri v1.13.4 updates the vendored zlib from 1.2.11\nto 1.2.12, which addresses [CVE-2018-25032](https://nvd.nist.gov/vuln/detail/CVE-2018-25032).\nThat CVE is scored as CVSS 7.4 \"High\" on the NVD record as of 2022-04-05.\n\nPlease note that this advisory only applies to the CRuby implementation of\nNokogiri `< 1.13.4`, and only if the packaged version of `zlib` is being used.\nPlease see [this document](https://nokogiri.org/LICENSE-DEPENDENCIES.html#default-platform-release-ruby)\nfor a complete description of which platform gems vendor `zlib`. If you've\noverridden defaults at installation time to use system libraries instead of\npackaged libraries, you should instead pay attention to your distro's `zlib`\nrelease announcements.\n\n## Mitigation\n\nUpgrade to Nokogiri `>= v1.13.4`.\n\n## Impact\n\n### [CVE-2018-25032](https://nvd.nist.gov/vuln/detail/CVE-2018-25032) in zlib\n\n- **Severity**: High\n- **Type**: [CWE-787](https://cwe.mitre.org/data/definitions/787.html)\n  Out of bounds write\n- **Description**: zlib before 1.2.12 allows memory corruption when\n  deflating (i.e., when compressing) if the input has many distant matches.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2018-25032", "osvdb": null, "ghsa": "v6gp-9mmm-c6p5", "unaffected_versions": [], "patched_versions": [">= 1.13.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2021-41098.yml", "id": "CVE-2021-41098", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-2rr5-8q37-2w7h", "title": "Improper Restriction of XML External Entity Reference (XXE) in Nokogiri on JRuby", "date": "2021-09-27", "description": "### Severity\n\nThe Nokogiri maintainers have evaluated this as [**High Severity** 7.5 (CVSS3.0)](https://www.first.org/cvss/calculator/3.0#CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N/E:H/RL:O/RC:C/MAV:N/MAC:L) for JRuby users. (This security advisory does not apply to CRuby users.)\n\n### Impact\n\nIn Nokogiri v1.12.4 and earlier, **on JRuby only**, the SAX parser resolves external entities by default.\n\nUsers of Nokogiri on JRuby who parse untrusted documents using any of these classes are affected:\n\n- Nokogiri::XML::SAX::Parser\n- Nokogiri::HTML4::SAX::Parser or its alias Nokogiri::HTML::SAX::Parser\n- Nokogiri::XML::SAX::PushParser\n- Nokogiri::HTML4::SAX::PushParser or its alias <PERSON><PERSON>giri::HTML::SAX::PushParser\n\n### Mitigation\n\nJRuby users should upgrade to Nokogiri v1.12.5 or later. There are no workarounds available for v1.12.4 or earlier.\n\nCRuby users are not affected.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2021-41098", "osvdb": null, "ghsa": "2rr5-8q37-2w7h", "unaffected_versions": [], "patched_versions": [">= 1.12.5"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2022-24839.yml", "id": "CVE-2022-24839", "url": "https://github.com/sparklemotion/nekohtml/security/advisories/GHSA-9849-p7jc-9rmv", "title": "Denial of Service (DoS) in Nokogiri on JRuby", "date": "2022-04-11", "description": "## Summary\n\n<PERSON><PERSON><PERSON> `v1.13.4` updates the vendored `org.cyberneko.html` library to\n`1.9.22.noko2` which addresses [CVE-2022-24839](https://github.com/sparklemotion/nekohtml/security/advisories/GHSA-9849-p7jc-9rmv).\nThat CVE is rated 7.5 (High Severity).\n\nSee [GHSA-9849-p7jc-9rmv](https://github.com/sparklemotion/nekohtml/security/advisories/GHSA-9849-p7jc-9rmv)\nfor more information.\n\nPlease note that this advisory only applies to the **JRuby** implementation of Nokogiri `< 1.13.4`.\n\n## Mitigation\n\nUpgrade to Nokogiri `>= 1.13.4`.\n\n## Impact\n\n### [CVE-2022-24839](https://github.com/sparklemotion/nekohtml/security/advisories/GHSA-9849-p7jc-9rmv) in nekohtml\n\n- **Severity**: High 7.5\n- **Type**: [CWE-400](https://cwe.mitre.org/data/definitions/400.html) Uncontrolled Resource Consumption\n- **Description**: The fork of `org.cyberneko.html` used by <PERSON><PERSON><PERSON> (Rubygem) raises a\n  `java.lang.OutOfMemoryError` exception when parsing ill-formed HTML markup.\n- **See also**: [GHSA-9849-p7jc-9rmv](https://github.com/sparklemotion/nekohtml/security/advisories/GHSA-9849-p7jc-9rmv)\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-24839", "osvdb": null, "ghsa": "gx8x-g87m-h5q6", "unaffected_versions": [], "patched_versions": [">= 1.13.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/GHSA-2qc6-mcvw-92cw.yml", "id": "GHSA-2qc6-mcvw-92cw", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-2qc6-mcvw-92cw", "title": "Update bundled libxml2 to v2.10.3 to resolve multiple CVEs", "date": "2022-10-18", "description": "### Summary\n\nNokogiri v1.13.9 upgrades the packaged version of its dependency libxml2 to\n[v2.10.3](https://gitlab.gnome.org/GNOME/libxml2/-/releases/v2.10.3) from\nv2.9.14.\n\nlibxml2 v2.10.3 addresses the following known vulnerabilities:\n\n- [CVE-2022-2309](https://nvd.nist.gov/vuln/detail/CVE-2022-2309)\n- [CVE-2022-40304](https://nvd.nist.gov/vuln/detail/CVE-2022-40304)\n- [CVE-2022-40303](https://nvd.nist.gov/vuln/detail/CVE-2022-40303)\n\nPlease note that this advisory only applies to the CRuby implementation of\nNokogiri `< 1.13.9`, and only if the _packaged_ libraries are being used. If\nyou've overridden defaults at installation time to use _system_ libraries\ninstead of packaged libraries, you should instead pay attention to your\ndistro's `libxml2` release announcements.\n\n\n### Mitigation\n\nUpgrade to Nokogiri `>= 1.13.9`.\n\nUsers who are unable to upgrade Nokogiri may also choose a more complicated\nmitigation: compile and link Nokogiri against external libraries libxml2\n`>= 2.10.3` which will also address these same issues.\n\n\n### Impact\n\n#### libxml2 [CVE-2022-2309](https://nvd.nist.gov/vuln/detail/CVE-2022-2309)\n\n- **CVSS3 score**: Under evaluation\n- **Type**: Denial of service\n- **Description**: NULL Pointer Dereference allows attackers to cause a denial\nof service (or application crash). This only applies when lxml is used\ntogether with libxml2 2.9.10 through 2.9.14. libxml2 2.9.9 and earlier are not\naffected. It allows triggering crashes through forged input data, given a\nvulnerable code sequence in the application. The vulnerability is caused by\nthe iterwalk function (also used by the canonicalize function). Such code\nshouldn't be in wide-spread use, given that parsing + iterwalk would usually\nbe replaced with the more efficient iterparse function. However, an XML\nconverter that serialises to C14N would also be vulnerable, for example, and\nthere are legitimate use cases for this code sequence. If untrusted input is\nreceived (also remotely) and processed via iterwalk function, a crash can be\ntriggered.\n\nNokogiri maintainers investigated at #2620 and determined this CVE does not\naffect Nokogiri users.\n\n\n#### libxml2 [CVE-2022-40304](https://nvd.nist.gov/vuln/detail/CVE-2022-40304)\n\n- **CVSS3 score**: Unspecified upstream\n- **Type**: Data corruption, denial of service\n- **Description**: When an entity reference cycle is detected, the entity\ncontent is cleared by setting its first byte to zero. But the entity content\nmight be allocated from a dict. In this case, the dict entry becomes corrupted\nleading to all kinds of logic errors, including memory errors like\ndouble-frees.\n\nSee https://gitlab.gnome.org/GNOME/libxml2/-/commit/644a89e080bced793295f61f18aac8cfad6bece2\n\n\n#### libxml2 [CVE-2022-40303](https://nvd.nist.gov/vuln/detail/CVE-2022-40303)\n\n- **CVSS3 score**: Unspecified upstream\n- **Type**: Integer overflow\n- **Description**: Integer overflows with XML_PARSE_HUGE\n\nSee https://gitlab.gnome.org/GNOME/libxml2/-/commit/c846986356fc149915a74972bf198abc266bc2c0\n", "cvss_v2": null, "cvss_v3": null, "cve": null, "osvdb": null, "ghsa": "2qc6-mcvw-92cw", "unaffected_versions": [], "patched_versions": [">= 1.13.9"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/GHSA-pxvg-2qj5-37jq.yml", "id": "GHSA-pxvg-2qj5-37jq", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-pxvg-2qj5-37jq", "title": "Update packaged libxml2 to v2.10.4 to resolve multiple CVEs", "date": "2023-04-11", "description": "### Summary\n\nNokogiri v1.14.3 upgrades the packaged version of its dependency libxml2 to\n[v2.10.4](https://gitlab.gnome.org/GNOME/libxml2/-/releases/v2.10.4) from v2.10.3.\n\nlibxml2 v2.10.4 addresses the following known vulnerabilities:\n\n- [CVE-2023-29469](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-29469): Hashing of\n  empty dict strings isn't deterministic\n- [CVE-2023-28484](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-28484): Fix null deref\n  in xmlSchemaFixupComplexType\n- Schemas: Fix null-pointer-deref in xmlSchemaCheckCOSSTDerivedOK\n\nPlease note that this advisory only applies to the CRuby implementation of Nokogiri `< 1.14.3`,\nand only if the _packaged_ libraries are being used. If you've overridden defaults at installation\ntime to use _system_ libraries instead of packaged libraries, you should instead pay attention to\nyour distro's `libxml2` release announcements.\n\n\n### Mitigation\n\nUpgrade to Nokogiri `>= 1.14.3`.\n\nUsers who are unable to upgrade Nokogiri may also choose a more complicated mitigation: compile\nand link Nokogiri against external libraries libxml2 `>= 2.10.4` which will also address these\nsame issues.\n\n\n### Impact\n\nNo public information has yet been published about the security-related issues other than the\nupstream commits. Examination of those changesets indicate that the more serious issues relate to\nlibxml2 dereferencing NULL pointers and potentially segfaulting while parsing untrusted inputs.\n\nThe commits can be examined at:\n\n- [\\[CVE-2023-29469\\] Hashing of empty dict strings isn't deterministic (09a2dd45)](https://gitlab.gnome.org/GNOME/libxml2/-/commit/09a2dd453007f9c7205274623acdd73747c22d64)\n- [\\[CVE-2023-28484\\] Fix null deref in xmlSchemaFixupComplexType (647e072e)](https://gitlab.gnome.org/GNOME/libxml2/-/commit/647e072ea0a2f12687fa05c172f4c4713fdb0c4f)\n- [schemas: Fix null-pointer-deref in xmlSchemaCheckCOSSTDerivedOK (4c6922f7)](https://gitlab.gnome.org/GNOME/libxml2/-/commit/4c6922f763ad958c48ff66f82823ae21f2e92ee6)\n", "cvss_v2": null, "cvss_v3": null, "cve": null, "osvdb": null, "ghsa": "pxvg-2qj5-37jq", "unaffected_versions": [], "patched_versions": [">= 1.14.3"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2021-3517.yml", "id": "CVE-2021-3517", "url": "https://bugzilla.redhat.com/show_bug.cgi?id=1954232", "title": "Nokogiri contains libxml Out-of-bounds Write vulnerability", "date": "2022-05-24", "description": "There is a flaw in the xml entity encoding functionality of libxml2 in versions before 2.9.11. An attacker who is able to supply a crafted file to be processed by an application linked with the affected functionality of libxml2 could trigger an out-of-bounds read. The most likely impact of this flaw is to application availability, with some potential impact to confidentiality and integrity if an attacker is able to use memory information to further exploit the application.\n\nNokogiri prior to version 1.11.4 used a vulnerable version of libxml2. Nokogiri 1.11.4 updated libxml2 to version 2.9.11 to address this and other vulnerabilities in libxml2.\n", "cvss_v2": null, "cvss_v3": 8.6, "cve": "2021-3517", "osvdb": null, "ghsa": "jw9f-hh49-cvp9", "unaffected_versions": [], "patched_versions": [">= 1.11.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2021-30560.yml", "id": "CVE-2021-30560", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-fq42-c5rg-92c2", "title": "Update packaged libxml2 (2.9.12 → 2.9.13) and libxslt (1.1.34 → 1.1.35)", "date": "2022-02-21", "description": "## Summary\n\nNokogiri v1.13.2 upgrades two of its packaged dependencies:\n\n* vendored libxml2 from v2.9.12 to v2.9.13\n* vendored libxslt from v1.1.34 to v1.1.35\n\nThose library versions address the following upstream CVEs:\n\n* libxslt: CVE-2021-30560 (CVSS 8.8, High severity)\n* libxml2: CVE-2022-23308 (Unspecified severity, see more information below)\n\nThose library versions also address numerous other issues including performance\nimprovements, regression fixes, and bug fixes, as well as memory leaks and other\nuse-after-free issues that were not assigned CVEs.\n\nPlease note that this advisory only applies to the CRuby implementation of\nNokogiri < 1.13.2, and only if the packaged libraries are being used. If you've\noverridden defaults at installation time to use system libraries instead of\npackaged libraries, you should instead pay attention to your distro's `libxml2`\nand `libxslt` release announcements.\n\n## Mitigation\n\nUpgrade to No<PERSON>giri >= 1.13.2.\n\nUsers who are unable to upgrade Noko<PERSON> may also choose a more complicated\nmitigation: compile and link an older version <PERSON><PERSON>giri against external libraries\nlibxml2 >= 2.9.13 and libxslt >= 1.1.35, which will also address these same CVEs.\n\n## Impact\n\n* libxslt CVE-2021-30560\n* CVSS3 score: 8.8 (High)\n\nFixed by https://gitlab.gnome.org/GNOME/libxslt/-/commit/50f9c9c\n\nAll versions of libxslt prior to v1.1.35 are affected.\n\nApplications using untrusted XSL stylesheets to transform XML are vulnerable to\na denial-of-service attack and should be upgraded immediately.\n\nlibxml2 CVE-2022-23308\n* As of the time this security advisory was published, there is no officially\npublished information available about this CVE's severity. The above NIST link\ndoes not yet have a published record, and the libxml2 maintainer has declined\nto provide a severity score.\n* Fixed by https://gitlab.gnome.org/GNOME/libxml2/-/commit/652dd12\n* Further explanation is at https://mail.gnome.org/archives/xml/2022-February/msg00015.html\n\nThe upstream commit and the explanation linked above indicate that an application\nmay be vulnerable to a denial of service, memory disclosure, or code execution if\nit parses an untrusted document with parse options `DTDVALID` set to true, and `NOENT`\nset to false.\n\nAn analysis of these parse options:\n\n* While `NOENT` is off by default for Document, DocumentFragment, Reader, and\nSchema parsing, it is on by default for XSLT (stylesheet) parsing in Nokogiri\nv1.12.0 and later.\n* `DTDVALID` is an option that Nokogiri does not set for any operations, and so\nthis CVE applies only to applications setting this option explicitly.\n\nIt seems reasonable to assume that any application explicitly setting the parse\noption `DTDVALID` when parsing untrusted documents is vulnerable and should be\nupgraded immediately.\n", "cvss_v2": null, "cvss_v3": 8.8, "cve": "2021-30560", "osvdb": null, "ghsa": "fq42-c5rg-92c2", "unaffected_versions": [], "patched_versions": [">= 1.13.2"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/GHSA-cgx6-hpwq-fhv5.yml", "id": "GHSA-cgx6-hpwq-fhv5", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-cgx6-hpwq-fhv5", "title": "Integer Overflow or Wraparound in libxml2 affects Nokogiri", "date": "2022-05-18", "description": "### Summary\n\nNokogiri v1.13.5 upgrades the packaged version of its dependency libxml2 from\nv2.9.13 to [v2.9.14](https://gitlab.gnome.org/GNOME/libxml2/-/releases/v2.9.14).\n\nlibxml2 v2.9.14 addresses [CVE-2022-29824](https://nvd.nist.gov/vuln/detail/CVE-2022-29824).\nThis version also includes several security-related bug fixes for which CVEs were not created,\nincluding a potential double-free, potential memory leaks, and integer-overflow.\n\nPlease note that this advisory only applies to the CRuby implementation of Nokogiri\n`< 1.13.5`, and only if the _packaged_ libraries are being used. If you've overridden\ndefaults at installation time to use _system_ libraries instead of packaged libraries,\nyou should instead pay attention to your distro's `libxml2` and `libxslt` release announcements.\n\n### Mitigation\n\nUpgrade to Nokogiri `>= 1.13.5`.\n\nUsers who are unable to upgrade <PERSON><PERSON><PERSON> may also choose a more complicated mitigation:\ncompile and link <PERSON><PERSON><PERSON> against external libraries libxml2 `>= 2.9.14` which will also\naddress these same issues.\n\n### Impact\n\n#### libxml2 [CVE-2022-29824](https://nvd.nist.gov/vuln/detail/CVE-2022-29824)\n\n- **CVSS3 score**:\n  - Unspecified upstream\n  - Nokogiri maintainers evaluate at 8.6 (High) ([CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:H](https://www.first.org/cvss/calculator/3.1#CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:H)). Note that this is different from the CVSS assessed by NVD.\n- **Type**: Denial of service, information disclosure\n- **Description**: In libxml2 before 2.9.14, several buffer handling functions in buf.c (xmlBuf*) and tree.c (xmlBuffer*) don't check for integer overflows. This can result in out-of-bounds memory writes. Exploitation requires a victim to open a crafted, multi-gigabyte XML file. Other software using libxml2's buffer functions, for example libxslt through 1.1.35, is affected as well.\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/-/commit/2554a24\n\nAll versions of libml2 prior to v2.9.14 are affected.\n\nApplications parsing or serializing multi-gigabyte documents (in excess of INT_MAX bytes) may be vulnerable to an integer overflow bug in buffer handling that could lead to exposure of confidential data, modification of unrelated data, or a segmentation fault resulting in a denial-of-service.\n\n\n### References\n\n- [libxml2 v2.9.14 release notes](https://gitlab.gnome.org/GNOME/libxml2/-/releases/v2.9.14)\n- [CVE-2022-29824](https://nvd.nist.gov/vuln/detail/CVE-2022-29824)\n- [CWE-119: Improper Restriction of Operations within the Bounds of a Memory Buffer](https://cwe.mitre.org/data/definitions/119.html)\n", "cvss_v2": null, "cvss_v3": 8.6, "cve": null, "osvdb": null, "ghsa": "cgx6-hpwq-fhv5", "unaffected_versions": [], "patched_versions": [">= 1.13.5"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/GHSA-7rrm-v45f-jp64.yml", "id": "GHSA-7rrm-v45f-jp64", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-7rrm-v45f-jp64", "title": "Update packaged dependency libxml2 from 2.9.10 to 2.9.12", "date": "2021-05-17", "description": "### Summary\n\nNokogiri v1.11.4 updates the vendored libxml2 from v2.9.10 to v2.9.12 which addresses:\n\n- [CVE-2019-20388](https://security.archlinux.org/CVE-2019-20388) (Medium severity)\n- [CVE-2020-24977](https://security.archlinux.org/CVE-2020-24977) (Medium severity)\n- [CVE-2021-3517](https://security.archlinux.org/CVE-2021-3517) (Medium severity)\n- [CVE-2021-3518](https://security.archlinux.org/CVE-2021-3518) (Medium severity)\n- [CVE-2021-3537](https://security.archlinux.org/CVE-2021-3537) (Low severity)\n- [CVE-2021-3541](https://security.archlinux.org/CVE-2021-3541) (Low severity)\n\nNote that two additional CVEs were addressed upstream but are not relevant to this release. [CVE-2021-3516](https://security.archlinux.org/CVE-2021-3516) via `xmllint` is not present in Nokogiri, and [CVE-2020-7595](https://security.archlinux.org/CVE-2020-7595) has been patched in Nokogiri since v1.10.8 (see #1992).\n\nPlease note that this advisory only applies to the CRuby implementation of Nokogiri `< 1.11.4`, and only if the packaged version of libxml2 is being used. If you've overridden defaults at installation time to use system libraries instead of packaged libraries, you should instead pay attention to your distro's `libxml2` release announcements.\n\n\n### Mitigation\n\nUpgrade to Nokogiri `>= 1.11.4`.\n\n\n### Impact\n\nI've done a brief analysis of the published CVEs that are addressed in this upstream release. The libxml2 maintainers have not released a canonical set of CVEs, and so this list is pieced together from secondary sources and may be incomplete.\n\nAll information below is sourced from [security.archlinux.org](https://security.archlinux.org), which appears to have the most up-to-date information as of this analysis.\n\n#### [CVE-2019-20388](https://security.archlinux.org/CVE-2019-20388)\n\n- **Severity**: Medium\n- **Type**: Denial of service\n- **Description**: A memory leak was found in the xmlSchemaValidateStream function of libxml2. Applications that use this library may be vulnerable to memory not being freed leading to a denial of service.\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/commit/7ffcd44d7e6c46704f8af0321d9314cd26e0e18a\n\nVerified that the fix commit first appears in v2.9.11. It seems possible that this issue would be present in programs using Nokogiri < v1.11.4.\n\n\n#### [CVE-2020-7595](https://security.archlinux.org/CVE-2020-7595)\n\n- **Severity**: Medium\n- **Type**: Denial of service\n- **Description**: xmlStringLenDecodeEntities in parser.c in libxml2 2.9.10 has an infinite loop in a certain end-of-file situation.\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/commit/0e1a49c8907645d2e155f0d89d4d9895ac5112b5\n\nThis has been patched in Nokogiri since v1.10.8 (see #1992).\n\n\n#### [CVE-2020-24977](https://security.archlinux.org/CVE-2020-24977)\n\n- **Severity**: Medium\n- **Type**: Information disclosure\n- **Description**: GNOME project libxml2 <= 2.9.10 has a global buffer over-read vulnerability in xmlEncodeEntitiesInternal at libxml2/entities.c.\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/commit/50f06b3efb638efb0abd95dc62dca05ae67882c2\n\nVerified that the fix commit first appears in v2.9.11. It seems possible that this issue would be present in programs using Nokogiri < v1.11.4.\n\n\n#### [CVE-2021-3516](https://security.archlinux.org/CVE-2021-3516)\n\n- **Severity**: Medium\n- **Type**: Arbitrary code execution (no remote vector)\n- **Description**: A use-after-free security issue was found libxml2 before version 2.9.11 when \"xmllint --html --push\" is used to process crafted files.\n- **Issue**: https://gitlab.gnome.org/GNOME/libxml2/-/issues/230\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/-/commit/1358d157d0bd83be1dfe356a69213df9fac0b539\n\nVerified that the fix commit first appears in v2.9.11. This vector does not exist within Nokogiri, which does not ship `xmllint`.\n\n\n#### [CVE-2021-3517](https://security.archlinux.org/CVE-2021-3517)\n\n- **Severity**: Medium\n- **Type**: Arbitrary code execution\n- **Description**: A heap-based buffer overflow was found in libxml2 before version 2.9.11 when processing truncated UTF-8 input.\n- **Issue**: https://gitlab.gnome.org/GNOME/libxml2/-/issues/235\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/-/commit/bf22713507fe1fc3a2c4b525cf0a88c2dc87a3a2\n\nVerified that the fix commit first appears in v2.9.11. It seems possible that this issue would be present in programs using Nokogiri < v1.11.4.\n\n\n#### [CVE-2021-3518](https://security.archlinux.org/CVE-2021-3518)\n\n- **Severity**: Medium\n- **Type**: Arbitrary code execution\n- **Description**: A use-after-free security issue was found in libxml2 before version 2.9.11 in xmlXIncludeDoProcess() in xinclude.c when processing crafted files.\n- **Issue**: https://gitlab.gnome.org/GNOME/libxml2/-/issues/237\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/-/commit/1098c30a040e72a4654968547f415be4e4c40fe7\n\nVerified that the fix commit first appears in v2.9.11. It seems possible that this issue would be present in programs using Nokogiri < v1.11.4.\n\n\n#### [CVE-2021-3537](https://security.archlinux.org/CVE-2021-3537)\n\n- **Severity**: Low\n- **Type**: Denial of service\n- **Description**: It was found that libxml2 before version 2.9.11 did not propagate errors while parsing XML mixed content, causing a NULL dereference. If an untrusted XML document was parsed in recovery mode and post-validated, the flaw could be used to crash the application.\n- **Issue**: https://gitlab.gnome.org/GNOME/libxml2/-/issues/243\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/-/commit/babe75030c7f64a37826bb3342317134568bef61\n\nVerified that the fix commit first appears in v2.9.11. It seems possible that this issue would be present in programs using Nokogiri < v1.11.4.\n\n\n#### [CVE-2021-3541](https://security.archlinux.org/CVE-2021-3541)\n\n- **Severity**: Low\n- **Type**: Denial of service\n- **Description**: A security issue was found in libxml2 before version 2.9.11. Exponential entity expansion attack its possible bypassing all existing protection mechanisms and leading to denial of service.\n- **Fixed**: https://gitlab.gnome.org/GNOME/libxml2/-/commit/8598060bacada41a0eb09d95c97744ff4e428f8e\n\nVerified that the fix commit first appears in v2.9.11. It seems possible that this issue would be present in programs using Nokogiri < v1.11.4, however Nokogiri's default parse options prevent the attack from succeeding (it is necessary to opt into `DTDLOAD` which is off by default).\n\nFor more details supporting this analysis of this CVE, please visit #2233.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": null, "osvdb": null, "ghsa": "7rrm-v45f-jp64", "unaffected_versions": [], "patched_versions": [">= 1.11.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2022-29181.yml", "id": "CVE-2022-29181", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-xh29-r2w5-wx8m", "title": "Improper Handling of Unexpected Data Type in Nokogiri", "date": "2022-05-23", "description": "### Summary\n\nNokogiri `< v1.13.6` does not type-check all inputs into the XML and HTML4 SAX parsers.\nFor CRuby users, this may allow specially crafted untrusted inputs to cause illegal\nmemory access errors (segfault) or reads from unrelated memory.\n\n### Severity\n\nThe Nokogiri maintainers have evaluated this as **High 8.2** (CVSS3.1).\n\n### Mitigation\n\nCRuby users should upgrade to Nokogiri `>= 1.13.6`.\n\nJRuby users are not affected.\n\n### Workarounds\n\nTo avoid this vulnerability in affected applications, ensure the untrusted input is a\n`String` by calling `#to_s` or equivalent.\n", "cvss_v2": null, "cvss_v3": 8.2, "cve": "2022-29181", "osvdb": null, "ghsa": "xh29-r2w5-wx8m", "unaffected_versions": [], "patched_versions": [">= 1.13.6"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2022-23437.yml", "id": "CVE-2022-23437", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-xxx9-3xcr-gjj3", "title": "XML Injection in Xerces Java affects Nokogiri", "date": "2022-04-11", "description": "## Summary\n\nNokogiri v1.13.4 updates the vendored `xerces:xercesImpl` from 2.12.0 to\n2.12.2, which addresses [CVE-2022-23437](https://nvd.nist.gov/vuln/detail/CVE-2022-23437).\nThat CVE is scored as CVSS 6.5 \"Medium\" on the NVD record.\n\nPlease note that this advisory only applies to the **JRuby** implementation\nof Nokogiri `< 1.13.4`.\n\n## Mitigation\n\nUpgrade to Nokogiri `>= v1.13.4`.\n\n## Impact\n\n### [CVE-2022-23437](https://nvd.nist.gov/vuln/detail/CVE-2022-23437) in xerces-J\n\n- **Severity**: Medium\n- **Type**: [CWE-91](https://cwe.mitre.org/data/definitions/91.html) XML Injection (aka Blind XPath Injection)\n- **Description**: There's a vulnerability within the Apache Xerces Java\n  (XercesJ) XML parser when handling specially crafted XML document payloads.\n  This causes, the XercesJ XML parser to wait in an infinite loop, which may\n  sometimes consume system resources for prolonged duration. This vulnerability\n  is present within XercesJ version 2.12.1 and the previous versions.\n- **See also**: https://github.com/advisories/GHSA-h65f-jvqw-m9fj\n", "cvss_v2": null, "cvss_v3": 6.5, "cve": "2022-23437", "osvdb": null, "ghsa": "xxx9-3xcr-gjj3", "unaffected_versions": [], "patched_versions": [">= 1.13.4"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2021-3518.yml", "id": "CVE-2021-3518", "url": "https://nokogiri.org/CHANGELOG.html#1114-2021-05-14", "title": "Nokogiri Implements libxml2 version vulnerable to use-after-free", "date": "2022-05-24", "description": "There's a flaw in libxml2 in versions before 2.9.11. An attacker\nwho is able to submit a crafted file to be processed by an application\nlinked with libxml2 could trigger a use-after-free. The greatest\nimpact from this flaw is to confidentiality, integrity, and availability.\n", "cvss_v2": null, "cvss_v3": 8.8, "cve": "2021-3518", "osvdb": null, "ghsa": "v4f8-2847-rwm7", "unaffected_versions": [], "patched_versions": [">= 1.11.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2022-24836.yml", "id": "CVE-2022-24836", "url": "https://github.com/sparklemotion/nokogiri/security/advisories/GHSA-crjr-9rc5-ghw8", "title": "Inefficient Regular Expression Complexity in Nokogiri", "date": "2022-04-11", "description": "## Summary\n\n<PERSON><PERSON>giri `< v1.13.4` contains an inefficient regular expression that is\nsusceptible to excessive backtracking when attempting to detect encoding\nin HTML documents.\n\n## Mitigation\n\nUpgrade to <PERSON><PERSON><PERSON> `>= 1.13.4`.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-24836", "osvdb": null, "ghsa": "crjr-9rc5-ghw8", "unaffected_versions": [], "patched_versions": [">= 1.13.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "<PERSON>kogiri", "version": "1.11.2"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/nokogiri/CVE-2021-3537.yml", "id": "CVE-2021-3537", "url": "https://nokogiri.org/CHANGELOG.html#1114-2021-05-14", "title": "Nokogiri Implements libxml2 version vulnerable to null pointer dereferencing", "date": "2022-05-24", "description": "A vulnerability found in libxml2 in versions before 2.9.11 shows\nthat it did not propagate errors while parsing XML mixed content,\ncausing a NULL dereference. If an untrusted XML document was parsed\nin recovery mode and post-validated, the flaw could be used to crash\nthe application. The highest threat from this vulnerability\nis to system availability.\n", "cvss_v2": 4.3, "cvss_v3": 5.9, "cve": "2021-3537", "osvdb": null, "ghsa": "286v-pcf5-25rc", "unaffected_versions": [], "patched_versions": [">= 1.11.4"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "puma", "version": "5.2.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/puma/CVE-2021-29509.yml", "id": "CVE-2021-29509", "url": "https://github.com/puma/puma/security/advisories/GHSA-q28m-8xjw-8vr5", "title": "Keepalive Connections Causing Denial Of Service in puma", "date": "2021-05-11", "description": "### Impact\n\nThe fix for CVE-2019-16770 was incomplete. The original fix only protected\nexisting connections that had already been accepted from having their\nrequests starved by greedy persistent-connections saturating all threads in\nthe same process. However, new connections may still be starved by greedy\npersistent-connections saturating all threads in all processes in the\ncluster.\n\nA puma server which received more concurrent keep-alive connections than the\nserver had threads in its threadpool would service only a subset of\nconnections, denying service to the unserved connections.\n\n### Patches\n\nThis problem has been fixed in puma 4.3.8 and 5.3.1.\n\n### Workarounds\n\nSetting queue_requests false also fixes the issue. This is not advised when\nusing puma without a reverse proxy, such as nginx or apache, because you will\nopen yourself to slow client attacks (e.g. [slowloris][1]).\n\nThe fix is very small. [A git patch is available here][2] for those using\n[unsupported versions][3] of Puma.\n\n[1]: https://en.wikipedia.org/wiki/Slowloris_(computer_security)\n[2]: https://gist.github.com/nateberkopec/4b3ea5676c0d70cbb37c82d54be25837\n[3]: https://github.com/puma/puma/security/policy#supported-versions\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2021-29509", "osvdb": null, "ghsa": "q28m-8xjw-8vr5", "unaffected_versions": [], "patched_versions": ["~> 4.3.8", ">= 5.3.1"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "puma", "version": "5.2.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/puma/CVE-2022-24790.yml", "id": "CVE-2022-24790", "url": "https://github.com/puma/puma/security/advisories/GHSA-h99w-9q5r-gjq9", "title": "HTTP Request Smuggling in puma", "date": "2022-03-30", "description": "### Impact\n\nWhen using <PERSON>uma behind a proxy that does not properly validate that the\nincoming HTTP request matches the RFC7230 standard, <PERSON><PERSON> and the frontend\nproxy may disagree on where a request starts and ends. This would allow\nrequests to be smuggled via the front-end proxy to <PERSON><PERSON>.\n\nThe following vulnerabilities are addressed by this advisory:\n- Lenient parsing of `Transfer-Encoding` headers, when unsupported encodings\n  should be rejected and the final encoding must be `chunked`.\n- Lenient parsing of malformed `Content-Length` headers and chunk sizes, when\n  only digits and hex digits should be allowed.\n- Lenient parsing of duplicate `Content-Length` headers, when they should be\n  rejected.\n- Lenient parsing of the ending of chunked segments, when they should end\n  with `\\r\\n`.\n\n### Patches\n\nThe vulnerability has been fixed in 5.6.4 and 4.3.12.\n\n### Workarounds\n\nWhen deploying a proxy in front of <PERSON><PERSON>, turning on any and all functionality\nto make sure that the request matches the RFC7230 standard.\n\nThese proxy servers are known to have \"good\" behavior re: this standard and\nupgrading Puma may not be necessary. Users are encouraged to validate for\nthemselves.\n\n- Nginx (latest)\n- Apache (latest)\n- Haproxy 2.5+\n- <PERSON><PERSON><PERSON> (latest)\n- <PERSON><PERSON><PERSON><PERSON> (latest)\n\n### References\n\n[HTTP Request Smuggling](https://portswigger.net/web-security/request-smuggling)\n", "cvss_v2": null, "cvss_v3": 9.1, "cve": "2022-24790", "osvdb": null, "ghsa": "h99w-9q5r-gjq9", "unaffected_versions": [], "patched_versions": ["~> 4.3.12", ">= 5.6.4"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "puma", "version": "5.2.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/puma/CVE-2023-40175.yml", "id": "CVE-2023-40175", "url": "https://github.com/puma/puma/security/advisories/GHSA-68xg-gqqm-vgj8", "title": "Inconsistent Interpretation of HTTP Requests ('HTTP Request/Response Smuggling') in puma", "date": "2023-08-18", "description": "### Impact\n\nPrior to version 6.3.1, pu<PERSON> exhibited incorrect behavior when parsing chunked transfer encoding bodies and zero-length Content-Length headers in a way that allowed HTTP request smuggling.\n\nThe following vulnerabilities are addressed by this advisory:\n- Incorrect parsing of trailing fields in chunked transfer encoding bodies\n- Parsing of blank/zero-length Content-Length headers`\\r\\n`\n\n### Patches\n\nThe vulnerability has been fixed in 6.3.1 and 5.6.7.\n\n### Workarounds\n\nNo known workarounds.\n\n### References\n\n[HTTP Request Smuggling](https://portswigger.net/web-security/request-smuggling)\n", "cvss_v2": null, "cvss_v3": 6.5, "cve": "2023-40175", "osvdb": null, "ghsa": "68xg-gqqm-vgj8", "unaffected_versions": [], "patched_versions": ["~> 5.6.7", ">= 6.3.1"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "puma", "version": "5.2.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/puma/CVE-2022-23634.yml", "id": "CVE-2022-23634", "url": "https://github.com/puma/puma/security/advisories/GHSA-rmj8-8hhh-gv5h", "title": "Information Exposure with Puma when used with Rails", "date": "2022-02-11", "description": "### Impact\n\nPrior to `puma` version `5.6.2`, `puma` may not always call\n`close` on the response body. Rails, prior to version `*******`, depended on the\nresponse body being closed in order for its `CurrentAttributes` implementation to\nwork correctly.\n\nFrom Rails:\n\n> Under certain circumstances response bodies will not be closed, for example\n> a bug in a webserver[1] or a bug in a Rack middleware. In the event a\n> response is not notified of a close, ActionDispatch::Executor will not know\n> to reset thread local state for the next request. This can lead to data\n> being leaked to subsequent requests, especially when interacting with\n> ActiveSupport::CurrentAttributes.\n\nThe combination of these two behaviors (Puma not closing the body + Rails'\nExecutor implementation) causes information leakage.\n\n### Patches\n\nThis problem is fixed in Puma versions 5.6.2 and 4.3.11.\n\nThis problem is fixed in Rails versions 7.02.2, *******, *******, and *******.\n\nSee: https://github.com/advisories/GHSA-wh98-p28r-vrc9\nfor details about the rails vulnerability\n\nUpgrading to a patched Rails _or_ Puma version fixes the vulnerability.\n\n### Workarounds\n\nUpgrade to Rails versions *******, *******, *******, and *******.\n\nThe [Rails CVE](https://groups.google.com/g/ruby-security-ann/c/FkTM-_7zSNA/m/K2RiMJBlBAAJ?utm_medium=email&utm_source=footer&pli=1)\nincludes a middleware that can be used instead.\n", "cvss_v2": null, "cvss_v3": 8.0, "cve": "2022-23634", "osvdb": null, "ghsa": "rmj8-8hhh-gv5h", "unaffected_versions": [], "patched_versions": ["~> 4.3.11", ">= 5.6.2"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "puma", "version": "5.2.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/puma/CVE-2021-41136.yml", "id": "CVE-2021-41136", "url": "https://github.com/puma/puma/security/advisories/GHSA-48w2-rm65-62xx", "title": "Inconsistent Interpretation of HTTP Requests ('HTTP Request Smuggling') in puma", "date": "2021-10-12", "description": "### Impact\n\nPrior to `puma` version 5.5.0, using `puma` with a proxy which forwards LF characters as line endings could allow HTTP request smuggling. A client could smuggle a request through a proxy, causing the proxy to send a response back to another unknown client.\n\nThis behavior (forwarding LF characters as line endings) is very uncommon amongst proxy servers, so we have graded the impact here as \"low\". <PERSON><PERSON> is only aware of a single proxy server which has this behavior.\n\nIf the proxy uses persistent connections and the client adds another request in via HTTP pipelining, the proxy may mistake it as the first request's body. Puma, however, would see it as two requests, and when processing the second request, send back a response that the proxy does not expect. If the proxy has reused the persistent connection to Puma to send another request for a different client, the second response from the first client will be sent to the second client.\n\n### Patches\n\nThis vulnerability was patched in Puma 5.5.1 and 4.3.9.\n\n### Workarounds\n\nThis vulnerability only affects Puma installations without any proxy in front.\n\nUse a proxy which does not forward LF characters as line endings.\n\nProxies which do not forward LF characters as line endings:\n\n* Nginx\n* Apache (>2.4.25)\n* Haproxy\n* Caddy\n* Traefik\n\n### Possible Breakage\n\nIf you are [dealing with legacy clients that want to send `LF` as a line ending](https://stackoverflow.com/questions/43574428/have-apache-accept-lf-vs-crlf-in-request-headers) in an HTTP header, this will cause those clients to receive a `400` error.\n\n### References\n\n* [HTTP Request Smuggling](https://portswigger.net/web-security/request-smuggling)\n", "cvss_v2": null, "cvss_v3": 3.7, "cve": "2021-41136", "osvdb": null, "ghsa": "48w2-rm65-62xx", "unaffected_versions": [], "patched_versions": ["~> 4.3.9", ">= 5.5.1"], "criticality": "low"}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2022-30123.yml", "id": "CVE-2022-30123", "url": "https://groups.google.com/g/ruby-security-ann/c/LWB10kWzag8", "title": "Possible shell escape sequence injection vulnerability in Rack", "date": "2022-06-27", "description": "There is a possible shell escape sequence injection vulnerability in the Lint\nand CommonLogger components of Rack.  This vulnerability has been assigned the\nCVE identifier CVE-2022-30123.\n\nVersions Affected:  All.\nNot affected:       None\nFixed Versions:     *******, *******, *******\n\n## Impact\nCarefully crafted requests can cause shell escape sequences to be written to\nthe terminal via <PERSON><PERSON>'s Lint middleware and CommonLogger middleware.  These\nescape sequences can be leveraged to possibly execute commands in the victim's\nterminal.\n\nImpacted applications will have either of these middleware installed, and\nvulnerable apps may have something like this:\n\n```\nuse Rack::Lint\n```\n\nOr\n\n```\nuse Rack::CommonLogger\n```\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\n## Workarounds\nRemove these middleware from your application\n", "cvss_v2": null, "cvss_v3": 10.0, "cve": "2022-30123", "osvdb": null, "ghsa": "wq4h-7r42-5hrr", "unaffected_versions": [], "patched_versions": ["~> 2.0.9, >= *******", "~> 2.1.4, >= *******", ">= *******"], "criticality": "critical"}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2022-30122.yml", "id": "CVE-2022-30122", "url": "https://groups.google.com/g/ruby-security-ann/c/L2Axto442qk", "title": "Denial of Service Vulnerability in Rack Multipart Parsing", "date": "2022-06-27", "description": "There is a possible denial of service vulnerability in the multipart parsing\ncomponent of <PERSON><PERSON>.  This vulnerability has been assigned the CVE identifier\nCVE-2022-30122.\n\nVersions Affected:  >= 1.2\nNot affected:       < 1.2\nFixed Versions:     *******, *******, *******\n\n## Impact\nCarefully crafted multipart POST requests can cause <PERSON><PERSON>'s multipart parser to\ntake much longer than expected, leading to a possible denial of service\nvulnerability.\n\nImpacted code will use <PERSON><PERSON>'s multipart parser to parse multipart posts.  This\nincludes directly using the multipart parser like this:\n\n```\nparams = Rack::Multipart.parse_multipart(env)\n```\n\nBut it also includes reading POST data from a Rack request object like this:\n\n```\np request.POST # read POST data\np request.params # reads both query params and POST data\n```\n\nAll users running an affected release should either upgrade or use one of the\nworkarounds immediately.\n\n## Workarounds\nThere are no feasible workarounds for this issue.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-30122", "osvdb": null, "ghsa": "hxqx-xwvh-44m2", "unaffected_versions": ["< 1.2"], "patched_versions": ["~> 2.0.9, >= *******", "~> 2.1.4, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2022-44571.yml", "id": "CVE-2022-44571", "url": "https://github.com/rack/rack/releases/tag/v*******", "title": "Denial of Service Vulnerability in Rack Content-Disposition parsing", "date": "2023-01-18", "description": "There is a denial of service vulnerability in the Content-Disposition parsing\ncomponent of Rack. This vulnerability has been assigned the CVE identifier\nCVE-2022-44571.\n\nVersions Affected: >= 2.0.0\nNot affected: None.\nFixed Versions: *******, *******, *******, *******\n\n# Impact\n\nCarefully crafted input can cause Content-Disposition header parsing in Rack\nto take an unexpected amount of time, possibly resulting in a denial of\nservice attack vector. This header is used typically used in multipart\nparsing. Any applications that parse multipart posts using Rack (virtually\nall Rails applications) are impacted.\n\n# Workarounds\n\nThere are no feasible workarounds for this issue.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2022-44571", "osvdb": null, "ghsa": "93pm-5p5f-3ghx", "unaffected_versions": [], "patched_versions": ["~> 2.0.9, >= *******", "~> 2.1.4, >= *******", "~> 2.2.6, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2022-44570.yml", "id": "CVE-2022-44570", "url": "https://github.com/rack/rack/releases/tag/v*******", "title": "Denial of service via header parsing in Rack", "date": "2023-01-18", "description": "There is a possible denial of service vulnerability in the Range header\nparsing component of Rack. This vulnerability has been assigned the CVE\nidentifier CVE-2022-44570.\n\nVersions Affected: >= 1.5.0\nNot affected: None.\nFixed Versions: *******, *******, *******, *******\n\n# Impact\n\nCarefully crafted input can cause the Range header parsing component in Ra<PERSON>\nto take an unexpected amount of time, possibly resulting in a denial of\nservice attack vector. Any applications that deal with Range requests (such\nas streaming applications, or applications that serve files) may be impacted.\n\n# Workarounds\n\nThere are no feasible workarounds for this issue.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-44570", "osvdb": null, "ghsa": "65f5-mfpf-vfhj", "unaffected_versions": [], "patched_versions": ["~> 2.0.9, >= *******", "~> 2.1.4, >= *******", "~> 2.2.6, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2023-27539.yml", "id": "CVE-2023-27539", "url": "https://discuss.rubyonrails.org/t/cve-2023-27539-possible-denial-of-service-vulnerability-in-racks-header-parsing/82466", "title": "Possible Denial of Service Vulnerability in <PERSON><PERSON>’s header parsing", "date": "2023-03-13", "description": "There is a denial of service vulnerability in the header parsing component of Rack. This vulnerability has been assigned the CVE identifier CVE-2023-27539.\n\nVersions Affected: >= 2.0.0 Not affected: None. Fixed Versions: *******, *******\n\n# Impact\nCarefully crafted input can cause header parsing in <PERSON><PERSON> to take an unexpected amount of time, possibly resulting in a denial of service attack vector. Any applications that parse headers using Rack (virtually all Rails applications) are impacted.\n\n# Workarounds\nSetting Regexp.timeout in Ruby 3.2 is a possible workaround.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2023-27539", "osvdb": null, "ghsa": "c6qg-cjj8-47qp", "unaffected_versions": [], "patched_versions": ["~> 2.0, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2022-44572.yml", "id": "CVE-2022-44572", "url": "https://github.com/rack/rack/releases/tag/v*******", "title": "Denial of service via multipart parsing in Rack", "date": "2023-01-18", "description": "There is a denial of service vulnerability in the multipart parsing component\nof <PERSON>ck. This vulnerability has been assigned the CVE identifier\nCVE-2022-44572.\n\nVersions Affected: >= 2.0.0\nNot affected: None.\nFixed Versions: *******, *******, *******, *******\n\n# Impact\n\nCarefully crafted input can cause RFC2183 multipart boundary parsing in Rack\nto take an unexpected amount of time, possibly resulting in a denial of\nservice attack vector. Any applications that parse multipart posts using\nRack (virtually all Rails applications) are impacted.\n\n# Workarounds\n\nThere are no feasible workarounds for this issue.\n", "cvss_v2": null, "cvss_v3": null, "cve": "2022-44572", "osvdb": null, "ghsa": "rqv2-275x-2jq5", "unaffected_versions": [], "patched_versions": ["~> 2.0.9, >= *******", "~> 2.1.4, >= *******", "~> 2.2.6, >= *******", ">= *******"], "criticality": null}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2020-8184.yml", "id": "CVE-2020-8184", "url": "https://groups.google.com/g/rubyonrails-security/c/OWtmozPH9Ak", "title": "Percent-encoded cookies can be used to overwrite existing prefixed cookie names", "date": "2020-06-15", "description": "It is possible to forge a secure or host-only cookie prefix in Rack using\nan arbitrary cookie write by using URL encoding (percent-encoding) on the\nname of the cookie. This could result in an application that is dependent on\nthis prefix to determine if a cookie is safe to process being manipulated\ninto processing an insecure or cross-origin request.\nThis vulnerability has been assigned the CVE identifier CVE-2020-8184.\n\nVersions Affected:  rack < 2.2.3, rack < 2.1.4\nNot affected:       Applications which do not rely on __Host- and __Secure- prefixes to determine if a cookie is safe to process\nFixed Versions:     rack >= 2.2.3, rack >= 2.1.4\n\nImpact\n------\n\nAn attacker may be able to trick a vulnerable application into processing an\ninsecure (non-SSL) or cross-origin request if they can gain the ability to write\narbitrary cookies that are sent to the application.\n\nWorkarounds\n-----------\n\nIf your application is impacted but you cannot upgrade to the released versions or apply\nthe provided patch, this issue can be temporarily addressed by adding the following workaround:\n\n```\nmodule Rack\n  module Utils\n    module_function def parse_cookies_header(header)\n      return {} unless header\n      header.split(/[;] */n).each_with_object({}) do |cookie, cookies|\n        next if cookie.empty?\n        key, value = cookie.split('=', 2)\n        cookies[key] = (unescape(value) rescue value) unless cookies.key?(key)\n      end\n    end\n  end\nend\n```\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2020-8184", "osvdb": null, "ghsa": "j6w9-fv6q-3q52", "unaffected_versions": [], "patched_versions": ["~> 2.1.4", ">= 2.2.3"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2020-8161.yml", "id": "CVE-2020-8161", "url": "https://groups.google.com/forum/#!topic/ruby-security-ann/T4ZIsfRf2eA", "title": "Directory traversal in Rack::Directory app bundled with Rack", "date": "2020-05-12", "description": "There was a possible directory traversal vulnerability in the Rack::Directory app\nthat is bundled with Rack.\n\nVersions Affected:  rack < 2.2.0\nNot affected:       Applications that do not use Rack::Directory.\nFixed Versions:     2.1.3, >= 2.2.0\n\nImpact\n------\n\nIf certain directories exist in a director that is managed by\n`Rack::Directory`, an attacker could, using this vulnerability, read the\ncontents of files on the server that were outside of the root specified in the\nRack::Directory initializer.\n\nWorkarounds\n-----------\n\nUntil such time as the patch is applied or their Rack version is upgraded,\nwe recommend that developers do not use Rack::Directory in their\napplications.\n", "cvss_v2": null, "cvss_v3": 8.6, "cve": "2020-8161", "osvdb": null, "ghsa": "5f9h-9pjv-v6j7", "unaffected_versions": [], "patched_versions": ["~> 2.1.3", ">= 2.2.0"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "rack", "version": "2.0.8"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rack/CVE-2023-27530.yml", "id": "CVE-2023-27530", "url": "https://discuss.rubyonrails.org/t/cve-2023-27530-possible-dos-vulnerability-in-multipart-mime-parsing/82388", "title": "Possible DoS Vulnerability in Multipart MIME parsing", "date": "2023-03-03", "description": "There is a possible DoS vulnerability in the Multipart MIME parsing code in Rack. This vulnerability has been assigned the CVE identifier CVE-2023-27530.\n\nVersions Affected: All. Not affected: None Fixed Versions: *******, *******, *******, *******\n\n# Impact\nThe Multipart MIME parsing code in Rack limits the number of file parts, but does not limit the total number of parts that can be uploaded. Carefully crafted requests can abuse this and cause multipart parsing to take longer than expected.\n\nAll users running an affected release should either upgrade or use one of the workarounds immediately.\n\n# Workarounds\nA proxy can be configured to limit the POST body size which will mitigate this issue.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2023-27530", "osvdb": null, "ghsa": "3h57-hmj3-gj3p", "unaffected_versions": [], "patched_versions": ["~> 2.0.9, >= *******", "~> 2.1.4, >= *******", "~> 2.2.6, >= *******", ">= *******"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "rails-html-sanitizer", "version": "1.3.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rails-html-sanitizer/CVE-2022-32209.yml", "id": "CVE-2022-32209", "url": "https://groups.google.com/g/rubyonrails-security/c/ce9PhUANQ6s", "title": "Possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer", "date": "2022-06-09", "description": "There is a possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer.\nThis vulnerability has been assigned the CVE identifier CVE-2022-32209.\n\nVersions Affected: ALL\nNot affected: NONE\nFixed Versions: v1.4.3\n\n## Impact\n\nA possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer\nmay allow an attacker to inject content if the application developer has overridden\nthe sanitizer's allowed tags to allow both `select` and `style` elements.\n\nCode is only impacted if allowed tags are being overridden. This may be done via\napplication configuration:\n\n```ruby\n# In config/application.rb\nconfig.action_view.sanitized_allowed_tags = [\"select\", \"style\"]\n```\n\nsee https://guides.rubyonrails.org/configuring.html#configuring-action-view\n\nOr it may be done with a `:tags` option to the Action View helper `sanitize`:\n\n```\n<%= sanitize @comment.body, tags: [\"select\", \"style\"] %>\n```\n\nsee https://api.rubyonrails.org/classes/ActionView/Helpers/SanitizeHelper.html#method-i-sanitize\n\nOr it may be done with Rails::Html::SafeListSanitizer directly:\n\n```ruby\n# class-level option\nRails::Html::SafeListSanitizer.allowed_tags = [\"select\", \"style\"]\n```\n\nor\n\n```ruby\n# instance-level option\nRails::Html::SafeListSanitizer.new.sanitize(@article.body, tags: [\"select\", \"style\"])\n```\n\nAll users overriding the allowed tags by any of the above mechanisms to include\nboth \"select\" and \"style\" should either upgrade or use one of the workarounds immediately.\n\n## Workarounds\n\nRemove either `select` or `style` from the overridden allowed tags.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-32209", "osvdb": null, "ghsa": "pg8v-g4xq-hww9", "unaffected_versions": [], "patched_versions": [">= 1.4.3"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "rails-html-sanitizer", "version": "1.3.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rails-html-sanitizer/CVE-2022-23517.yml", "id": "CVE-2022-23517", "url": "https://github.com/rails/rails-html-sanitizer/security/advisories/GHSA-5x79-w82f-gw8w", "title": "Inefficient Regular Expression Complexity in rails-html-sanitizer", "date": "2022-12-13", "description": "## Summary\n\nCertain configurations of rails-html-sanitizer `< 1.4.4` use an inefficient regular expression that is susceptible to excessive backtracking when attempting to sanitize certain SVG attributes. This may lead to a denial of service through CPU resource consumption.\n\n## Mitigation\n\nUpgrade to rails-html-sanitizer `>= 1.4.4`.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-23517", "osvdb": null, "ghsa": "5x79-w82f-gw8w", "unaffected_versions": [], "patched_versions": [">= 1.4.4"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "rails-html-sanitizer", "version": "1.3.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rails-html-sanitizer/CVE-2022-23520.yml", "id": "CVE-2022-23520", "url": "https://github.com/rails/rails-html-sanitizer/security/advisories/GHSA-rrfc-7g8p-99q8", "title": "Possible XSS vulnerability with certain configurations of rails-html-sanitizer", "date": "2022-12-13", "description": "## Summary\n\nThere is a possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer. This is due to an incomplete fix of CVE-2022-32209.\n\n- Versions affected: ALL\n- Not affected: NONE\n- Fixed versions: 1.4.4\n\n## Impact\n\nA possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer may allow an attacker to inject content if the application developer has overridden the sanitizer's allowed tags to allow both \"select\" and \"style\" elements.\n\nCode is only impacted if allowed tags are being overridden using either of the following two mechanisms:\n\n1. Using the Rails configuration `config.action_view.sanitized_allow_tags=`:\n\n  ```ruby\n  # In config/application.rb\n  config.action_view.sanitized_allowed_tags = [\"select\", \"style\"]\n  ```\n\n  (see https://guides.rubyonrails.org/configuring.html#configuring-action-view)\n\n2. Using the class method `Rails::Html::SafeListSanitizer.allowed_tags=`:\n\n  ```ruby\n  # class-level option\n  Rails::Html::SafeListSanitizer.allowed_tags = [\"select\", \"style\"]\n  ```\n\nAll users overriding the allowed tags by either of the above mechanisms to include both \"select\" and \"style\" should either upgrade or use one of the workarounds immediately.\n\nNOTE: Code is _not_ impacted if allowed tags are overridden using either of the following mechanisms:\n\n- the `:tags` option to the Action View helper method `sanitize`.\n- the `:tags` option to the instance method `SafeListSanitizer#sanitize`.\n\n## Workarounds\n\nRemove either \"select\" or \"style\" from the overridden allowed tags.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-23520", "osvdb": null, "ghsa": "rrfc-7g8p-99q8", "unaffected_versions": [], "patched_versions": [">= 1.4.4"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "rails-html-sanitizer", "version": "1.3.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rails-html-sanitizer/CVE-2022-23519.yml", "id": "CVE-2022-23519", "url": "https://github.com/rails/rails-html-sanitizer/security/advisories/GHSA-9h9g-93gc-623h", "title": "Possible XSS vulnerability with certain configurations of rails-html-sanitizer", "date": "2022-12-13", "description": "## Summary\n\nThere is a possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer.\n\n- Versions affected: ALL\n- Not affected: NONE\n- Fixed versions: 1.4.4\n\n## Impact\n\nA possible XSS vulnerability with certain configurations of Rails::Html::Sanitizer may allow an attacker to inject content if the application developer has overridden the sanitizer's allowed tags in either of the following ways:\n\n- allow both \"math\" and \"style\" elements,\n- or allow both \"svg\" and \"style\" elements\n\nCode is only impacted if allowed tags are being overridden. Applications may be doing this in four different ways:\n\n1. using application configuration:\n\n  ```ruby\n  # In config/application.rb\n  config.action_view.sanitized_allowed_tags = [\"math\", \"style\"]\n  # or\n  config.action_view.sanitized_allowed_tags = [\"svg\", \"style\"]\n  ```\n\n  see https://guides.rubyonrails.org/configuring.html#configuring-action-view\n\n2. using a `:tags` option to the Action View helper `sanitize`:\n\n  ```\n  <%= sanitize @comment.body, tags: [\"math\", \"style\"] %>\n  <%# or %>\n  <%= sanitize @comment.body, tags: [\"svg\", \"style\"] %>\n  ```\n\n  see https://api.rubyonrails.org/classes/ActionView/Helpers/SanitizeHelper.html#method-i-sanitize\n\n3. using Rails::Html::SafeListSanitizer class method `allowed_tags=`:\n\n  ```ruby\n  # class-level option\n  Rails::Html::SafeListSanitizer.allowed_tags = [\"math\", \"style\"]\n  # or\n  Rails::Html::SafeListSanitizer.allowed_tags = [\"svg\", \"style\"]\n  ```\n\n4. using a `:tags` options to the Rails::Html::SafeListSanitizer instance method `sanitize`:\n\n  ```ruby\n  # instance-level option\n  Rails::Html::SafeListSanitizer.new.sanitize(@article.body, tags: [\"math\", \"style\"])\n  # or\n  Rails::Html::SafeListSanitizer.new.sanitize(@article.body, tags: [\"svg\", \"style\"])\n  ```\n\nAll users overriding the allowed tags by any of the above mechanisms to include ((\"math\" or \"svg\") and \"style\") should either upgrade or use one of the workarounds immediately.\n\n## Workarounds\n\nRemove \"style\" from the overridden allowed tags, or remove \"math\" and \"svg\" from the overridden allowed tags.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-23519", "osvdb": null, "ghsa": "9h9g-93gc-623h", "unaffected_versions": [], "patched_versions": [">= 1.4.4"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "rails-html-sanitizer", "version": "1.3.0"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/rails-html-sanitizer/CVE-2022-23518.yml", "id": "CVE-2022-23518", "url": "https://github.com/rails/rails-html-sanitizer/security/advisories/GHSA-mcvf-2q2m-x72m", "title": "Improper neutralization of data URIs may allow XSS in rails-html-sanitizer", "date": "2022-12-13", "description": "## Summary\n\nrails-html-sanitizer `>= 1.0.3, < 1.4.4` is vulnerable to cross-site scripting via data URIs when used in combination with Loofah `>= 2.1.0`.\n\n## Mitigation\n\nUpgrade to rails-html-sanitizer `>= 1.4.4`.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2022-23518", "osvdb": null, "ghsa": "mcvf-2q2m-x72m", "unaffected_versions": ["< 1.0.3"], "patched_versions": [">= 1.4.4"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "sidekiq", "version": "6.0.4"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/sidekiq/CVE-2022-23837.yml", "id": "CVE-2022-23837", "url": "https://github.com/mperham/sidekiq/commit/7785ac1399f1b28992adb56055f6acd88fd1d956", "title": "Denial of service in sidekiq", "date": "2022-01-27", "description": "In api.rb in Sidekiq before 5.2.10 and 6.4.0, there is no limit on the number of\ndays when requesting stats for the graph. This overloads the system, affecting the\nWeb UI, and makes it unavailable to users.\n", "cvss_v2": 5.0, "cvss_v3": 7.5, "cve": "2022-23837", "osvdb": null, "ghsa": "jrfj-98qg-qjgv", "unaffected_versions": [], "patched_versions": [">= 6.4.0", "~> 5.2.10"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "sidekiq", "version": "6.0.4"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/sidekiq/CVE-2023-26141.yml", "id": "CVE-2023-26141", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-26141", "title": "sidekiq Denial of Service vulnerability", "date": "2023-09-14", "description": "Versions of the package sidekiq before 7.1.3 are vulnerable to Denial\nof Service (DoS) due to insufficient checks in the dashboard-charts.js\nfile. An attacker can exploit this vulnerability by manipulating the\nlocalStorage value which will cause excessive polling requests.\n", "cvss_v2": null, "cvss_v3": 4.9, "cve": "2023-26141", "osvdb": null, "ghsa": "3qc2-v3hp-6cv8", "unaffected_versions": [], "patched_versions": [">= 7.1.3"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "sidekiq", "version": "6.0.4"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/sidekiq/CVE-2021-30151.yml", "id": "CVE-2021-30151", "url": "https://github.com/advisories/GHSA-grh7-935j-hg6w", "title": "Cross-site Scripting in Sidekiq", "date": "2021-10-06", "description": "Sidekiq through 5.1.3 and 6.x through 6.2.0 allows XSS via the queue\nname of the live-poll feature when Internet Explorer is used.\n", "cvss_v2": null, "cvss_v3": 6.1, "cve": "2021-30151", "osvdb": null, "ghsa": "grh7-935j-hg6w", "unaffected_versions": [], "patched_versions": ["~> 5.2.0", ">= 6.2.1"], "criticality": "medium"}}, {"type": "unpatched_gem", "gem": {"name": "tzinfo", "version": "1.2.6"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/tzinfo/CVE-2022-31163.yml", "id": "CVE-2022-31163", "url": "https://github.com/tzinfo/tzinfo/security/advisories/GHSA-5cm2-9h8c-rvfx", "title": "TZInfo relative path traversal vulnerability allows loading of arbitrary files", "date": "2022-07-21", "description": "# Impact\n\n## Affected versions\n\n- 0.3.60 and earlier.\n- 1.0.0 to 1.2.9 when used with the Ruby data source (tzinfo-data).\n\n## Vulnerability\n\nWith the Ruby data source (the tzinfo-data gem for tzinfo version 1.0.0 and\nlater and built-in to earlier versions), time zones are defined in Ruby files.\nThere is one file per time zone. Time zone files are loaded with `require` on\ndemand. In the affected versions, `TZInfo::Timezone.get` fails to validate\ntime zone identifiers correctly, allowing a new line character within the\nidentifier. With Ruby version 1.9.3 and later, `TZInfo::Timezone.get` can be\nmade to load unintended files with `require`, executing them within the Ruby\nprocess.\n\nFor example, with version 1.2.9, you can run the following to load a file with\npath `/tmp/payload.rb`:\n\n```ruby\nTZInfo::Timezone.get(\\\"foo\\\n/../../../../../../../../../../../../../../../../tmp/payload\\\")\n```\n\nThe exact number of parent directory traversals needed will vary depending on\nthe location of the tzinfo-data gem.\n\nTZInfo versions 1.2.6 to 1.2.9 can be made to load files from outside of the\nRuby load path. Versions up to and including 1.2.5 can only be made to load\nfiles from directories within the load path.\n\nThis could be exploited in, for example, a Ruby on Rails application using\ntzinfo version 1.2.9, that allows file uploads and has a time zone selector\nthat accepts arbitrary time zone identifiers.\nThe CVSS score and severity have been set on this basis.\n\nVersions 2.0.0 and later are not vulnerable.\n\n# Patches\n\nVersions 0.3.61 and 1.2.10 include fixes to correctly validate time zone\nidentifiers.\n\nNote that version 0.3.61 can still load arbitrary files from the Ruby load\npath if their name follows the rules for a valid time zone identifier and the\nfile has a prefix of `tzinfo/definition` within a directory in the load path.\nFor example if `/tmp/upload` was in the load path, then\n`TZInfo::Timezone.get('foo')` could load a file with path\n`/tmp/upload/tzinfo/definition/foo.rb`. Applications should ensure that\nuntrusted files are not placed in a directory on the load path.\n\n# Workarounds\n\nAs a workaround, the time zone identifier can be validated before passing to\n`TZInfo::Timezone.get` by ensuring it matches the regular expression\n`\\\\A[A-Za-z0-9+\\\\-_]+(?:\\\\/[A-Za-z0-9+\\\\-_]+)*\\\\z`.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2022-31163", "osvdb": null, "ghsa": "5cm2-9h8c-rvfx", "unaffected_versions": [">= 2.0.0"], "patched_versions": ["~> 0.3.61", ">= 1.2.10"], "criticality": "high"}}, {"type": "unpatched_gem", "gem": {"name": "websocket-extensions", "version": "0.1.4"}, "advisory": {"path": "/Users/<USER>/.local/share/ruby-advisory-db/gems/websocket-extensions/CVE-2020-7663.yml", "id": "CVE-2020-7663", "url": "https://github.com/faye/websocket-extensions-ruby/security/advisories/GHSA-g6wq-qcwm-j5g2", "title": "Regular Expression Denial of Service in websocket-extensions (RubyGem)", "date": "2020-06-05", "description": "### Impact\n\nThe ReDoS flaw allows an attacker to exhaust the server's capacity to process\nincoming requests by sending a WebSocket handshake request containing a header\nof the following form:\n\n    Sec-WebSocket-Extensions: a; b=\"\\c\\c\\c\\c\\c\\c\\c\\c\\c\\c ...\n\nThat is, a header containing an unclosed string parameter value whose content is\na repeating two-byte sequence of a backslash and some other character. The\nparser takes exponential time to reject this header as invalid, and this will\nblock the processing of any other work on the same thread. Thus if you are\nrunning a single-threaded server, such a request can render your service\ncompletely unavailable.\n\n### Workarounds\n\nThere are no known work-arounds other than disabling any public-facing WebSocket functionality you are operating.\n", "cvss_v2": null, "cvss_v3": 7.5, "cve": "2020-7663", "osvdb": null, "ghsa": "g6wq-qcwm-j5g2", "unaffected_versions": [], "patched_versions": [">= 0.1.5"], "criticality": "high"}}]}