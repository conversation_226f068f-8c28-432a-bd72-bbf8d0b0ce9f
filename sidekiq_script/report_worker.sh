#!/usr/bin/env bash

EB_APP_DEPLOY_DIR=/var/app/current
#EB_APP_PID_DIR=$(/opt/elasticbeanstalk/bin/get-config container -k app_pid_dir)
EB_APP_USER=webapp
#EB_SCRIPT_DIR=$(/opt/elasticbeanstalk/bin/get-config container -k script_dir)
#EB_SUPPORT_DIR=$(/opt/elasticbeanstalk/bin/get-config container -k support_dir)

SIDEKIQ_PID=/var/pids/sidekiq-report-worker.pid

cd $EB_APP_DEPLOY_DIR

if [ -f $SIDEKIQ_PID ]
then
  su -s /bin/bash -c "kill -TERM `cat $SIDEKIQ_PID`" $EB_APP_USER
  su -s /bin/bash -c "rm -rf $SIDEKIQ_PID" $EB_APP_USER
fi

sleep 10

su -s /bin/bash -c "bundle exec sidekiq \
  -e $RACK_ENV \
  -P $SIDEKIQ_PID \
  -c 1 -q report, -q notification" $EB_APP_USER
